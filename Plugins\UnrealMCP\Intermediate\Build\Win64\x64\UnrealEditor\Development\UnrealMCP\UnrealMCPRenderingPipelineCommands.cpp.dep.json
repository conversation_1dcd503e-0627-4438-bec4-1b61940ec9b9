{"Version": "1.2", "Data": {"Source": "c:\\game\\auracron\\plugins\\unrealmcp\\source\\unrealmcp\\private\\commands\\unrealmcprenderingpipelinecommands.cpp", "ProvidedModule": "", "PCH": "c:\\game\\auracron\\intermediate\\build\\win64\\x64\\auracroneditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.valexpapi.cpp20.h.pch", "Includes": ["c:\\game\\auracron\\plugins\\unrealmcp\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\unrealmcp\\definitions.unrealmcp.h", "c:\\game\\auracron\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcprenderingpipelinecommands.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\renderersettings.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\legacyscreenpercentagedriver.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\legacyscreenpercentagedriver.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\renderersettings.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\postprocesscomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\shapecomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\shapecomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\postprocesscomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rendercore\\public\\shaderparameterstruct.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rendercore\\public\\rendergraphresources.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rhi\\public\\rhitransientresourceallocator.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rendercore\\public\\rendergraphparameter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rendercore\\public\\rendergraphresources.inl", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\renderer\\public\\screenpass.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\canvastypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\canvastypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rendercore\\public\\commonrenderresources.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rendercore\\public\\rendergraphbuilder.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rendercore\\public\\rendergraphblackboard.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\generatedtypename.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rendercore\\public\\rendergraphevent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rendercore\\public\\rendergraphevent.inl", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rendercore\\public\\rendergraphpass.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rendercore\\public\\rendergraphparameters.inl", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rendercore\\public\\rendergraphtrace.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rendercore\\public\\rendergraphvalidation.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\experimental\\containers\\robinhoodhashtable.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rendercore\\public\\rendergraphbuilder.inl", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rendercore\\public\\rendergraphutils.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\renderer\\public\\screenpass.inl", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\lumenvisualizationdata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\systemsettings.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\gameusersettings.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gameusersettings.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\enginesettings\\classes\\consolesettings.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\enginesettings\\uht\\consolesettings.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\rendercore\\public\\rendercore.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\engineutils.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}