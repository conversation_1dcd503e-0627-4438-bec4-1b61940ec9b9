"""
Rendering Pipeline Tools for Unreal Engine MCP Server

This module provides comprehensive Rendering Pipeline tools that are 100% compatible with the 
C++ implementations in UnrealMCPRenderingPipelineCommands.cpp. Based on UE 5.6 official documentation.
"""

import logging
import json
from typing import Dict, List, Any, Optional
from mcp.server.fastmcp import FastMCP

# Set up logging
logger = logging.getLogger("UnrealMCP")

def register_rendering_pipeline_tools(mcp: FastMCP):
    """Register Rendering Pipeline tools with the MCP server - 100% compatible with C++ implementations."""

    @mcp.tool()
    def configure_lumen_global_illumination(
        enable_lumen: bool = True,
        gi_quality: int = 3,
        reflection_quality: int = 2,
        enable_hardware_raytracing: bool = False,
        surface_cache_resolution: int = 256
    ) -> Dict[str, Any]:
        """
        Configure Lumen Global Illumination settings.
        Compatible with HandleConfigureLumenGlobalIllumination in C++.
        
        Args:
            enable_lumen: Enable Lumen Global Illumination
            gi_quality: Global Illumination quality (0-4)
            reflection_quality: Reflection quality (0-4)
            enable_hardware_raytracing: Enable hardware ray tracing for Lumen
            surface_cache_resolution: Surface cache resolution
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "enable_lumen": enable_lumen,
                "gi_quality": gi_quality,
                "reflection_quality": reflection_quality,
                "enable_hardware_raytracing": enable_hardware_raytracing,
                "surface_cache_resolution": surface_cache_resolution
            }

            response = unreal.send_command("configure_lumen_global_illumination", params)
            return response or {"success": True, "message": "Lumen Global Illumination configured successfully"}

        except Exception as e:
            logger.error(f"Error configuring Lumen Global Illumination: {e}")
            return {"success": False, "message": f"Error configuring Lumen Global Illumination: {e}"}

    @mcp.tool()
    def configure_nanite_virtualized_geometry(
        enable_nanite: bool = True,
        cluster_per_page: int = 8,
        max_candidate_clusters: int = 8192,
        max_nodes: int = 1048576,
        enable_tessellation: bool = True
    ) -> Dict[str, Any]:
        """
        Configure Nanite Virtualized Geometry settings.
        Compatible with HandleConfigureNaniteVirtualizedGeometry in C++.
        
        Args:
            enable_nanite: Enable Nanite Virtualized Geometry
            cluster_per_page: Clusters per page
            max_candidate_clusters: Maximum candidate clusters
            max_nodes: Maximum nodes
            enable_tessellation: Enable tessellation support
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "enable_nanite": enable_nanite,
                "cluster_per_page": cluster_per_page,
                "max_candidate_clusters": max_candidate_clusters,
                "max_nodes": max_nodes,
                "enable_tessellation": enable_tessellation
            }

            response = unreal.send_command("configure_nanite_virtualized_geometry", params)
            return response or {"success": True, "message": "Nanite Virtualized Geometry configured successfully"}

        except Exception as e:
            logger.error(f"Error configuring Nanite Virtualized Geometry: {e}")
            return {"success": False, "message": f"Error configuring Nanite Virtualized Geometry: {e}"}

    @mcp.tool()
    def configure_virtual_shadow_maps(
        enable_vsm: bool = True,
        max_physical_pages: int = 4096,
        resolution_lod_bias_directional: float = 0.0,
        resolution_lod_bias_local: float = 0.0,
        enable_caching: bool = True
    ) -> Dict[str, Any]:
        """
        Configure Virtual Shadow Maps settings.
        Compatible with HandleConfigureVirtualShadowMaps in C++.
        
        Args:
            enable_vsm: Enable Virtual Shadow Maps
            max_physical_pages: Maximum physical pages for shadow cache
            resolution_lod_bias_directional: Resolution LOD bias for directional lights
            resolution_lod_bias_local: Resolution LOD bias for local lights
            enable_caching: Enable shadow map caching
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "enable_vsm": enable_vsm,
                "max_physical_pages": max_physical_pages,
                "resolution_lod_bias_directional": resolution_lod_bias_directional,
                "resolution_lod_bias_local": resolution_lod_bias_local,
                "enable_caching": enable_caching
            }

            response = unreal.send_command("configure_virtual_shadow_maps", params)
            return response or {"success": True, "message": "Virtual Shadow Maps configured successfully"}

        except Exception as e:
            logger.error(f"Error configuring Virtual Shadow Maps: {e}")
            return {"success": False, "message": f"Error configuring Virtual Shadow Maps: {e}"}

    @mcp.tool()
    def setup_temporal_super_resolution(
        enable_tsr: bool = True,
        quality_level: int = 2,
        sharpness: float = 0.0,
        enable_alpha_channel: bool = False,
        history_sample_count: int = 8
    ) -> Dict[str, Any]:
        """
        Setup Temporal Super Resolution (TSR) settings.
        Compatible with HandleSetupTemporalSuperResolution in C++.
        
        Args:
            enable_tsr: Enable Temporal Super Resolution
            quality_level: TSR quality level (0-3)
            sharpness: Sharpness adjustment (-2.0 to 2.0)
            enable_alpha_channel: Enable alpha channel support
            history_sample_count: History sample count
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "enable_tsr": enable_tsr,
                "quality_level": quality_level,
                "sharpness": sharpness,
                "enable_alpha_channel": enable_alpha_channel,
                "history_sample_count": history_sample_count
            }

            response = unreal.send_command("setup_temporal_super_resolution", params)
            return response or {"success": True, "message": "Temporal Super Resolution configured successfully"}

        except Exception as e:
            logger.error(f"Error setting up Temporal Super Resolution: {e}")
            return {"success": False, "message": f"Error setting up Temporal Super Resolution: {e}"}

    @mcp.tool()
    def configure_hardware_ray_tracing(
        enable_ray_tracing: bool = True,
        enable_ray_traced_reflections: bool = True,
        enable_ray_traced_shadows: bool = True,
        enable_ray_traced_global_illumination: bool = False,
        max_ray_recursion_depth: int = 1
    ) -> Dict[str, Any]:
        """
        Configure Hardware Ray Tracing settings.
        Compatible with HandleConfigureHardwareRayTracing in C++.
        
        Args:
            enable_ray_tracing: Enable hardware ray tracing
            enable_ray_traced_reflections: Enable ray traced reflections
            enable_ray_traced_shadows: Enable ray traced shadows
            enable_ray_traced_global_illumination: Enable ray traced global illumination
            max_ray_recursion_depth: Maximum ray recursion depth
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "enable_ray_tracing": enable_ray_tracing,
                "enable_ray_traced_reflections": enable_ray_traced_reflections,
                "enable_ray_traced_shadows": enable_ray_traced_shadows,
                "enable_ray_traced_global_illumination": enable_ray_traced_global_illumination,
                "max_ray_recursion_depth": max_ray_recursion_depth
            }

            response = unreal.send_command("configure_hardware_ray_tracing", params)
            return response or {"success": True, "message": "Hardware Ray Tracing configured successfully"}

        except Exception as e:
            logger.error(f"Error configuring Hardware Ray Tracing: {e}")
            return {"success": False, "message": f"Error configuring Hardware Ray Tracing: {e}"}

    @mcp.tool()
    def setup_megalights_system(
        enable_megalights: bool = True,
        shadow_method: str = "RayTracing",
        max_lights_per_tile: int = 32,
        enable_area_shadows: bool = True,
        light_culling_tile_size: int = 16
    ) -> Dict[str, Any]:
        """
        Setup MegaLights system for massive dynamic lighting.
        Compatible with HandleSetupMegaLightsSystem in C++.
        
        Args:
            enable_megalights: Enable MegaLights system
            shadow_method: Shadow method (RayTracing, VirtualShadowMaps)
            max_lights_per_tile: Maximum lights per tile
            enable_area_shadows: Enable area shadows
            light_culling_tile_size: Light culling tile size
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "enable_megalights": enable_megalights,
                "shadow_method": shadow_method,
                "max_lights_per_tile": max_lights_per_tile,
                "enable_area_shadows": enable_area_shadows,
                "light_culling_tile_size": light_culling_tile_size
            }

            response = unreal.send_command("setup_megalights_system", params)
            return response or {"success": True, "message": "MegaLights system configured successfully"}

        except Exception as e:
            logger.error(f"Error setting up MegaLights system: {e}")
            return {"success": False, "message": f"Error setting up MegaLights system: {e}"}

    @mcp.tool()
    def configure_post_process_pipeline(
        enable_bloom: bool = True,
        enable_auto_exposure: bool = True,
        enable_tone_mapping: bool = True,
        tone_mapper_type: str = "ACES",
        exposure_compensation: float = 0.0
    ) -> Dict[str, Any]:
        """
        Configure Post Process Pipeline settings.
        Compatible with HandleConfigurePostProcessPipeline in C++.

        Args:
            enable_bloom: Enable bloom effect
            enable_auto_exposure: Enable auto exposure
            enable_tone_mapping: Enable tone mapping
            tone_mapper_type: Tone mapper type (ACES, Filmic, None)
            exposure_compensation: Exposure compensation value
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "enable_bloom": enable_bloom,
                "enable_auto_exposure": enable_auto_exposure,
                "enable_tone_mapping": enable_tone_mapping,
                "tone_mapper_type": tone_mapper_type,
                "exposure_compensation": exposure_compensation
            }

            response = unreal.send_command("configure_post_process_pipeline", params)
            return response or {"success": True, "message": "Post Process Pipeline configured successfully"}

        except Exception as e:
            logger.error(f"Error configuring Post Process Pipeline: {e}")
            return {"success": False, "message": f"Error configuring Post Process Pipeline: {e}"}

    @mcp.tool()
    def setup_rendering_scalability(
        scalability_group: str = "Epic",
        view_distance_scale: float = 1.0,
        anti_aliasing_quality: int = 4,
        shadow_quality: int = 4,
        post_process_quality: int = 4
    ) -> Dict[str, Any]:
        """
        Setup rendering scalability settings for different hardware tiers.
        Compatible with HandleSetupRenderingScalability in C++.

        Args:
            scalability_group: Scalability group (Low, Medium, High, Epic, Cinematic)
            view_distance_scale: View distance scale multiplier
            anti_aliasing_quality: Anti-aliasing quality (0-4)
            shadow_quality: Shadow quality (0-4)
            post_process_quality: Post process quality (0-4)
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "scalability_group": scalability_group,
                "view_distance_scale": view_distance_scale,
                "anti_aliasing_quality": anti_aliasing_quality,
                "shadow_quality": shadow_quality,
                "post_process_quality": post_process_quality
            }

            response = unreal.send_command("setup_rendering_scalability", params)
            return response or {"success": True, "message": "Rendering scalability configured successfully"}

        except Exception as e:
            logger.error(f"Error setting up rendering scalability: {e}")
            return {"success": False, "message": f"Error setting up rendering scalability: {e}"}

    @mcp.tool()
    def optimize_rendering_performance(
        target_platform: str = "Windows",
        target_fps: int = 60,
        enable_gpu_profiling: bool = True,
        enable_stat_commands: bool = True,
        optimization_level: str = "Balanced"
    ) -> Dict[str, Any]:
        """
        Optimize rendering performance for target platform and FPS.
        Compatible with HandleOptimizeRenderingPerformance in C++.

        Args:
            target_platform: Target platform (Windows, PlayStation5, XboxSeriesX, etc.)
            target_fps: Target frame rate
            enable_gpu_profiling: Enable GPU profiling
            enable_stat_commands: Enable stat commands for debugging
            optimization_level: Optimization level (Performance, Balanced, Quality)
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "target_platform": target_platform,
                "target_fps": target_fps,
                "enable_gpu_profiling": enable_gpu_profiling,
                "enable_stat_commands": enable_stat_commands,
                "optimization_level": optimization_level
            }

            response = unreal.send_command("optimize_rendering_performance", params)
            return response or {"success": True, "message": "Rendering performance optimized successfully"}

        except Exception as e:
            logger.error(f"Error optimizing rendering performance: {e}")
            return {"success": False, "message": f"Error optimizing rendering performance: {e}"}

    @mcp.tool()
    def analyze_rendering_metrics(
        analysis_duration: float = 30.0,
        include_gpu_stats: bool = True,
        include_draw_calls: bool = True,
        include_memory_usage: bool = True,
        output_format: str = "JSON"
    ) -> Dict[str, Any]:
        """
        Analyze rendering performance metrics and generate report.
        Compatible with HandleAnalyzeRenderingMetrics in C++.

        Args:
            analysis_duration: Duration of analysis in seconds
            include_gpu_stats: Include GPU statistics
            include_draw_calls: Include draw call statistics
            include_memory_usage: Include memory usage statistics
            output_format: Output format (JSON, CSV, XML)
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "analysis_duration": analysis_duration,
                "include_gpu_stats": include_gpu_stats,
                "include_draw_calls": include_draw_calls,
                "include_memory_usage": include_memory_usage,
                "output_format": output_format
            }

            response = unreal.send_command("analyze_rendering_metrics", params)
            return response or {"success": True, "message": "Rendering metrics analysis completed successfully"}

        except Exception as e:
            logger.error(f"Error analyzing rendering metrics: {e}")
            return {"success": False, "message": f"Error analyzing rendering metrics: {e}"}
