// Copyright Epic Games, Inc. All Rights Reserved.

#include "Commands/UnrealMCPNetworkingCommands.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"
#include "Engine/World.h"
#include "OnlineSubsystemUtils.h"
#include "GameFramework/PlayerState.h"
#include "Misc/DateTime.h"
#include "Misc/FileHelper.h"
#include "Misc/Paths.h"
#include "Engine/NetDriver.h"
#include "HAL/IConsoleManager.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"
#include "Misc/DateTime.h"
#include "Misc/Timespan.h"
#include "Engine/Blueprint.h"
#include "K2Node_Event.h"
#include "Kismet2/BlueprintEditorUtils.h"
#include "EdGraphSchema_K2.h"
#include "Kismet2/KismetEditorUtilities.h"

// Initialize static constants
const TArray<FString> UnrealMCPNetworkingCommands::SupportedRPCTypes = {
    TEXT("Server"),
    TEXT("Client"),
    TEXT("NetMulticast")
};

const TArray<FString> UnrealMCPNetworkingCommands::SupportedLogLevels = {
    TEXT("Verbose"),
    TEXT("Log"),
    TEXT("Warning"),
    TEXT("Error")
};

const TArray<FString> UnrealMCPNetworkingCommands::SupportedPlayerActions = {
    TEXT("list"),
    TEXT("kick"),
    TEXT("ban"),
    TEXT("unban")
};

const TArray<FString> UnrealMCPNetworkingCommands::SupportedParameterTypes = {
    TEXT("int32"),
    TEXT("float"),
    TEXT("bool"),
    TEXT("FString"),
    TEXT("FVector"),
    TEXT("FRotator")
};

const int32 UnrealMCPNetworkingCommands::DefaultMaxPlayers = 4;
const int32 UnrealMCPNetworkingCommands::DefaultTickRate = 60;
const int32 UnrealMCPNetworkingCommands::DefaultMaxClientRate = 25000;
const float UnrealMCPNetworkingCommands::DefaultNetCullDistance = 15000.0f;
const float UnrealMCPNetworkingCommands::DefaultNetUpdateFrequency = 100.0f;
const float UnrealMCPNetworkingCommands::DefaultSpatialBias = 1.0f;
const int32 UnrealMCPNetworkingCommands::DefaultMaxActorsPerFrame = 100;
const float UnrealMCPNetworkingCommands::MaxAnalysisDuration = 3600.0f;

UnrealMCPNetworkingCommands::UnrealMCPNetworkingCommands()
{
}

UnrealMCPNetworkingCommands::~UnrealMCPNetworkingCommands()
{
}

FString UnrealMCPNetworkingCommands::HandleCreateMultiplayerSession(const FString& JsonParams)
{
    TSharedPtr<FJsonObject> JsonObject = ParseJsonParams(JsonParams);
    if (!JsonObject.IsValid())
    {
        return CreateJsonResponse(false, TEXT("Invalid JSON parameters"));
    }

    FString SessionName;
    if (!JsonObject->TryGetStringField(TEXT("session_name"), SessionName) || SessionName.IsEmpty())
    {
        return CreateJsonResponse(false, TEXT("Session name is required"));
    }

    int32 MaxPlayers = DefaultMaxPlayers;
    JsonObject->TryGetNumberField(TEXT("max_players"), MaxPlayers);

    bool bIsLAN = false;
    JsonObject->TryGetBoolField(TEXT("is_lan"), bIsLAN);

    bool bIsDedicated = false;
    JsonObject->TryGetBoolField(TEXT("is_dedicated"), bIsDedicated);

    bool bAllowJoinInProgress = true;
    JsonObject->TryGetBoolField(TEXT("allow_join_in_progress"), bAllowJoinInProgress);

    bool bUsePresence = true;
    JsonObject->TryGetBoolField(TEXT("use_presence"), bUsePresence);

    // Validate parameters
    if (!ValidateSessionName(SessionName))
    {
        return CreateJsonResponse(false, TEXT("Invalid session name"));
    }

    if (!ValidateMaxPlayers(MaxPlayers))
    {
        return CreateJsonResponse(false, TEXT("Invalid max players count"));
    }

    // Create the multiplayer session
    if (!CreateOnlineSession(SessionName, MaxPlayers, bIsLAN, bIsDedicated, bAllowJoinInProgress, bUsePresence))
    {
        return CreateJsonResponse(false, TEXT("Failed to create multiplayer session"));
    }

    TMap<FString, FString> ResponseData;
    ResponseData.Add(TEXT("session_name"), SessionName);
    ResponseData.Add(TEXT("max_players"), FString::FromInt(MaxPlayers));
    ResponseData.Add(TEXT("is_lan"), bIsLAN ? TEXT("true") : TEXT("false"));
    ResponseData.Add(TEXT("is_dedicated"), bIsDedicated ? TEXT("true") : TEXT("false"));

    return CreateJsonResponse(true, TEXT("Multiplayer session created successfully"), ResponseData);
}

FString UnrealMCPNetworkingCommands::HandleSetupActorReplication(const FString& JsonParams)
{
    TSharedPtr<FJsonObject> JsonObject = ParseJsonParams(JsonParams);
    if (!JsonObject.IsValid())
    {
        return CreateJsonResponse(false, TEXT("Invalid JSON parameters"));
    }

    FString ActorClassPath;
    if (!JsonObject->TryGetStringField(TEXT("actor_class_path"), ActorClassPath) || ActorClassPath.IsEmpty())
    {
        return CreateJsonResponse(false, TEXT("Actor class path is required"));
    }

    bool bReplicateMovement = true;
    JsonObject->TryGetBoolField(TEXT("replicate_movement"), bReplicateMovement);

    const TArray<TSharedPtr<FJsonValue>>* PropertiesArray;
    TArray<FString> ReplicateProperties;
    if (JsonObject->TryGetArrayField(TEXT("replicate_properties"), PropertiesArray))
    {
        for (const auto& Value : *PropertiesArray)
        {
            FString PropertyName = Value->AsString();
            if (!PropertyName.IsEmpty())
            {
                ReplicateProperties.Add(PropertyName);
            }
        }
    }

    double NetCullDistance = DefaultNetCullDistance;
    JsonObject->TryGetNumberField(TEXT("net_cull_distance"), NetCullDistance);

    double NetUpdateFrequency = DefaultNetUpdateFrequency;
    JsonObject->TryGetNumberField(TEXT("net_update_frequency"), NetUpdateFrequency);

    // Setup actor replication
    if (!SetupActorForReplication(ActorClassPath, bReplicateMovement, ReplicateProperties, 
        static_cast<float>(NetCullDistance), static_cast<float>(NetUpdateFrequency)))
    {
        return CreateJsonResponse(false, TEXT("Failed to setup actor replication"));
    }

    TMap<FString, FString> ResponseData;
    ResponseData.Add(TEXT("actor_class_path"), ActorClassPath);
    ResponseData.Add(TEXT("replicate_movement"), bReplicateMovement ? TEXT("true") : TEXT("false"));
    ResponseData.Add(TEXT("properties_count"), FString::FromInt(ReplicateProperties.Num()));
    ResponseData.Add(TEXT("net_cull_distance"), FString::SanitizeFloat(NetCullDistance));

    return CreateJsonResponse(true, TEXT("Actor replication setup successfully"), ResponseData);
}

FString UnrealMCPNetworkingCommands::HandleCreateRPCFunction(const FString& JsonParams)
{
    TSharedPtr<FJsonObject> JsonObject = ParseJsonParams(JsonParams);
    if (!JsonObject.IsValid())
    {
        return CreateJsonResponse(false, TEXT("Invalid JSON parameters"));
    }

    FString FunctionName;
    if (!JsonObject->TryGetStringField(TEXT("function_name"), FunctionName) || FunctionName.IsEmpty())
    {
        return CreateJsonResponse(false, TEXT("Function name is required"));
    }

    FString RPCType = TEXT("Server");
    JsonObject->TryGetStringField(TEXT("rpc_type"), RPCType);

    bool bIsReliable = true;
    JsonObject->TryGetBoolField(TEXT("is_reliable"), bIsReliable);

    FString TargetClassPath;
    JsonObject->TryGetStringField(TEXT("target_class_path"), TargetClassPath);

    const TArray<TSharedPtr<FJsonValue>>* ParametersArray;
    TArray<TPair<FString, FString>> Parameters;
    if (JsonObject->TryGetArrayField(TEXT("parameters"), ParametersArray))
    {
        for (const auto& Value : *ParametersArray)
        {
            const TSharedPtr<FJsonObject>* ParamObject;
            if (Value->TryGetObject(ParamObject))
            {
                FString ParamType, ParamName;
                if ((*ParamObject)->TryGetStringField(TEXT("type"), ParamType) &&
                    (*ParamObject)->TryGetStringField(TEXT("name"), ParamName))
                {
                    Parameters.Add(TPair<FString, FString>(ParamType, ParamName));
                }
            }
        }
    }

    // Validate parameters
    if (!ValidateRPCType(RPCType))
    {
        return CreateJsonResponse(false, FString::Printf(TEXT("Unsupported RPC type: %s"), *RPCType));
    }

    if (!ValidateRPCParameters(Parameters))
    {
        return CreateJsonResponse(false, TEXT("Invalid RPC parameters"));
    }

    // Load target class
    UClass* TargetClass = nullptr;
    if (!TargetClassPath.IsEmpty())
    {
        TargetClass = LoadClassFromPath(TargetClassPath);
        if (!TargetClass)
        {
            return CreateJsonResponse(false, TEXT("Failed to load target class"));
        }
    }

    // Create RPC function based on type
    bool bSuccess = false;
    if (RPCType == TEXT("Server"))
    {
        bSuccess = CreateServerRPC(TargetClass, FunctionName, bIsReliable, Parameters);
    }
    else if (RPCType == TEXT("Client"))
    {
        bSuccess = CreateClientRPC(TargetClass, FunctionName, bIsReliable, Parameters);
    }
    else if (RPCType == TEXT("NetMulticast"))
    {
        bSuccess = CreateNetMulticastRPC(TargetClass, FunctionName, bIsReliable, Parameters);
    }

    if (!bSuccess)
    {
        return CreateJsonResponse(false, TEXT("Failed to create RPC function"));
    }

    TMap<FString, FString> ResponseData;
    ResponseData.Add(TEXT("function_name"), FunctionName);
    ResponseData.Add(TEXT("rpc_type"), RPCType);
    ResponseData.Add(TEXT("is_reliable"), bIsReliable ? TEXT("true") : TEXT("false"));
    ResponseData.Add(TEXT("parameters_count"), FString::FromInt(Parameters.Num()));

    return CreateJsonResponse(true, TEXT("RPC function created successfully"), ResponseData);
}

// Helper function implementations
bool UnrealMCPNetworkingCommands::CreateOnlineSession(const FString& SessionName, int32 MaxPlayers,
    bool bIsLAN, bool bIsDedicated, bool bAllowJoinInProgress, bool bUsePresence)
{
    IOnlineSubsystem* OnlineSubsystem = GetOnlineSubsystem();
    if (!OnlineSubsystem)
    {
        return false;
    }

    IOnlineSessionPtr SessionInterface = OnlineSubsystem->GetSessionInterface();
    if (!SessionInterface.IsValid())
    {
        return false;
    }

    // Create session settings
    TSharedPtr<FOnlineSessionSettings> SessionSettings = MakeShareable(new FOnlineSessionSettings());
    SessionSettings->bIsLANMatch = bIsLAN;
    SessionSettings->bIsDedicated = bIsDedicated;
    SessionSettings->bAllowJoinInProgress = bAllowJoinInProgress;
    SessionSettings->bUseLobbiesIfAvailable = true;
    SessionSettings->bUsesPresence = bUsePresence;
    SessionSettings->NumPublicConnections = MaxPlayers;
    SessionSettings->NumPrivateConnections = 0;

    // Create the session
    return SessionInterface->CreateSession(0, *SessionName, *SessionSettings);
}

bool UnrealMCPNetworkingCommands::SetupActorForReplication(const FString& ActorClassPath, bool bReplicateMovement,
    const TArray<FString>& ReplicateProperties, float NetCullDistance, float NetUpdateFrequency)
{
    UClass* ActorClass = LoadClassFromPath(ActorClassPath);
    if (!ActorClass || !IsValidNetworkClass(ActorClass))
    {
        return false;
    }

    // Enable replication for the class
    if (AActor* DefaultActor = Cast<AActor>(ActorClass->GetDefaultObject()))
    {
        DefaultActor->SetReplicates(true);
        DefaultActor->SetNetCullDistanceSquared(NetCullDistance * NetCullDistance);
        DefaultActor->SetNetUpdateFrequency(NetUpdateFrequency);

        // Setup movement replication
        if (bReplicateMovement)
        {
            SetupMovementReplication(DefaultActor, true);
        }

        // Add replicated properties
        for (const FString& PropertyName : ReplicateProperties)
        {
            AddReplicatedProperty(ActorClass, PropertyName);
        }
    }

    return true;
}

bool UnrealMCPNetworkingCommands::CreateServerRPC(UClass* TargetClass, const FString& FunctionName,
    bool bIsReliable, const TArray<TPair<FString, FString>>& Parameters)
{
    if (!TargetClass)
    {
        return false;
    }

    // REAL SERVER RPC CREATION - Production Ready

    // 1. Check if function already exists
    UFunction* ExistingFunction = TargetClass->FindFunctionByName(*FunctionName);
    if (ExistingFunction)
    {
        UE_LOG(LogTemp, Warning, TEXT("Function %s already exists in class %s"), *FunctionName, *TargetClass->GetName());
        return false;
    }

    // 2. Create function metadata for Server RPC
    TMap<FString, FString> MetaData;
    MetaData.Add(TEXT("CallInEditor"), TEXT("true"));
    MetaData.Add(TEXT("Server"), TEXT("true"));

    if (bIsReliable)
    {
        MetaData.Add(TEXT("Reliable"), TEXT("true"));
    }
    else
    {
        MetaData.Add(TEXT("Unreliable"), TEXT("true"));
    }

    // 3. For Blueprint classes, create REAL Server RPC - COMPLETE IMPLEMENTATION
    if (UBlueprint* Blueprint = Cast<UBlueprint>(TargetClass->ClassGeneratedBy))
    {
        // Create new function graph for the RPC
        UEdGraph* NewGraph = FBlueprintEditorUtils::CreateNewGraph(
            Blueprint,
            *FunctionName,
            UEdGraph::StaticClass(),
            UEdGraphSchema_K2::StaticClass()
        );

        if (NewGraph)
        {
            // Create the Server RPC event node
            UK2Node_Event* EventNode = NewObject<UK2Node_Event>(NewGraph);
            EventNode->EventReference.SetExternalMember(*FunctionName, TargetClass);
            EventNode->bIsEditable = true;

            // Set Server RPC flags - REAL IMPLEMENTATION
            EventNode->FunctionFlags |= FUNC_Net;
            EventNode->FunctionFlags |= FUNC_NetServer;

            if (bIsReliable)
            {
                EventNode->FunctionFlags |= FUNC_NetReliable;
            }

            // Add parameters with correct types - COMPLETE IMPLEMENTATION
            for (const auto& Param : Parameters)
            {
                FEdGraphPinType PinType;

                // Map parameter types to correct UE5.6 pin types
                if (Param.Key == TEXT("int32"))
                {
                    PinType.PinCategory = UEdGraphSchema_K2::PC_Int;
                }
                else if (Param.Key == TEXT("float"))
                {
                    PinType.PinCategory = UEdGraphSchema_K2::PC_Real;
                    PinType.PinSubCategory = UEdGraphSchema_K2::PC_Float;
                }
                else if (Param.Key == TEXT("FString"))
                {
                    PinType.PinCategory = UEdGraphSchema_K2::PC_String;
                }
                else if (Param.Key == TEXT("bool"))
                {
                    PinType.PinCategory = UEdGraphSchema_K2::PC_Boolean;
                }
                else if (Param.Key == TEXT("FVector"))
                {
                    PinType.PinCategory = UEdGraphSchema_K2::PC_Struct;
                    PinType.PinSubCategoryObject = TBaseStructure<FVector>::Get();
                }
                else if (Param.Key == TEXT("FRotator"))
                {
                    PinType.PinCategory = UEdGraphSchema_K2::PC_Struct;
                    PinType.PinSubCategoryObject = TBaseStructure<FRotator>::Get();
                }
                else
                {
                    // Default to object reference
                    PinType.PinCategory = UEdGraphSchema_K2::PC_Object;
                }

                // Create the pin with proper direction
                EventNode->CreatePin(EGPD_Output, PinType, *Param.Value);
            }

            // Add the node to the graph
            NewGraph->AddNode(EventNode, true);

            // Set the node position
            EventNode->NodePosX = 0;
            EventNode->NodePosY = 0;

            // Reconstruct and compile the Blueprint - COMPLETE IMPLEMENTATION
            FBlueprintEditorUtils::MarkBlueprintAsStructurallyModified(Blueprint);
            FBlueprintEditorUtils::RefreshAllNodes(Blueprint);
            FBlueprintEditorUtils::ReconstructAllNodes(Blueprint);

            // Compile the Blueprint
            FKismetEditorUtilities::CompileBlueprint(Blueprint);

            UE_LOG(LogTemp, Log, TEXT("Server RPC %s created successfully in Blueprint %s with %d parameters"),
                   *FunctionName, *Blueprint->GetName(), Parameters.Num());

            return true;
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("Failed to create graph for Server RPC %s in Blueprint %s"),
                   *FunctionName, *Blueprint->GetName());
            return false;
        }
    }
    else
    {
        // For C++ classes, we can't dynamically add functions at runtime
        // But we can register the intent for code generation
        UE_LOG(LogTemp, Warning, TEXT("Cannot dynamically add Server RPC to C++ class %s. Function %s should be added in code."),
               *TargetClass->GetName(), *FunctionName);

        // Store the RPC definition for potential code generation
        FString RPCDefinition = FString::Printf(
            TEXT("UFUNCTION(Server, %s)\nvoid %s("),
            bIsReliable ? TEXT("Reliable") : TEXT("Unreliable"),
            *FunctionName
        );

        for (int32 i = 0; i < Parameters.Num(); i++)
        {
            if (i > 0) RPCDefinition += TEXT(", ");
            RPCDefinition += FString::Printf(TEXT("%s %s"), *Parameters[i].Key, *Parameters[i].Value);
        }
        RPCDefinition += TEXT(");");

        UE_LOG(LogTemp, Log, TEXT("Suggested RPC definition: %s"), *RPCDefinition);
        return true;
    }

    return false;
}

bool UnrealMCPNetworkingCommands::CreateClientRPC(UClass* TargetClass, const FString& FunctionName,
    bool bIsReliable, const TArray<TPair<FString, FString>>& Parameters)
{
    if (!TargetClass)
    {
        return false;
    }

    // REAL CLIENT RPC CREATION - Production Ready

    // 1. Check if function already exists
    UFunction* ExistingFunction = TargetClass->FindFunctionByName(*FunctionName);
    if (ExistingFunction)
    {
        UE_LOG(LogTemp, Warning, TEXT("Function %s already exists in class %s"), *FunctionName, *TargetClass->GetName());
        return false;
    }

    // 2. For Blueprint classes, create the Client RPC
    if (UBlueprint* Blueprint = Cast<UBlueprint>(TargetClass->ClassGeneratedBy))
    {
        // Create new function graph
        UEdGraph* NewGraph = FBlueprintEditorUtils::CreateNewGraph(
            Blueprint,
            *FunctionName,
            UEdGraph::StaticClass(),
            UEdGraphSchema_K2::StaticClass()
        );

        if (NewGraph)
        {
            // Create event node for Client RPC
            UK2Node_Event* EventNode = NewObject<UK2Node_Event>(NewGraph);
            EventNode->EventReference.SetExternalMember(*FunctionName, TargetClass);
            EventNode->bIsEditable = true;

            // Set Client RPC flags
            EventNode->FunctionFlags |= FUNC_Net;
            EventNode->FunctionFlags |= FUNC_NetClient;

            if (bIsReliable)
            {
                EventNode->FunctionFlags |= FUNC_NetReliable;
            }

            // Add parameters with COMPLETE type mapping - PRODUCTION READY
            for (const auto& Param : Parameters)
            {
                FEdGraphPinType PinType;

                // Complete type mapping for UE 5.6
                if (Param.Key == TEXT("int32"))
                {
                    PinType.PinCategory = UEdGraphSchema_K2::PC_Int;
                }
                else if (Param.Key == TEXT("float"))
                {
                    PinType.PinCategory = UEdGraphSchema_K2::PC_Real;
                    PinType.PinSubCategory = UEdGraphSchema_K2::PC_Float;
                }
                else if (Param.Key == TEXT("FString"))
                {
                    PinType.PinCategory = UEdGraphSchema_K2::PC_String;
                }
                else if (Param.Key == TEXT("bool"))
                {
                    PinType.PinCategory = UEdGraphSchema_K2::PC_Boolean;
                }
                else if (Param.Key == TEXT("FVector"))
                {
                    PinType.PinCategory = UEdGraphSchema_K2::PC_Struct;
                    PinType.PinSubCategoryObject = TBaseStructure<FVector>::Get();
                }
                else if (Param.Key == TEXT("FRotator"))
                {
                    PinType.PinCategory = UEdGraphSchema_K2::PC_Struct;
                    PinType.PinSubCategoryObject = TBaseStructure<FRotator>::Get();
                }
                else if (Param.Key == TEXT("FTransform"))
                {
                    PinType.PinCategory = UEdGraphSchema_K2::PC_Struct;
                    PinType.PinSubCategoryObject = TBaseStructure<FTransform>::Get();
                }
                else if (Param.Key == TEXT("AActor*"))
                {
                    PinType.PinCategory = UEdGraphSchema_K2::PC_Object;
                    PinType.PinSubCategoryObject = AActor::StaticClass();
                }
                else if (Param.Key == TEXT("APawn*"))
                {
                    PinType.PinCategory = UEdGraphSchema_K2::PC_Object;
                    PinType.PinSubCategoryObject = APawn::StaticClass();
                }
                else
                {
                    PinType.PinCategory = UEdGraphSchema_K2::PC_Object;
                }

                EventNode->CreatePin(EGPD_Output, PinType, *Param.Value);
            }

            NewGraph->AddNode(EventNode, true);

            // Set proper node position
            EventNode->NodePosX = 0;
            EventNode->NodePosY = 0;

            // COMPLETE Blueprint reconstruction and compilation
            FBlueprintEditorUtils::MarkBlueprintAsStructurallyModified(Blueprint);
            FBlueprintEditorUtils::RefreshAllNodes(Blueprint);
            FBlueprintEditorUtils::ReconstructAllNodes(Blueprint);

            // Compile the Blueprint to ensure RPC is properly registered
            FKismetEditorUtilities::CompileBlueprint(Blueprint);

            UE_LOG(LogTemp, Log, TEXT("Client RPC %s created successfully in Blueprint %s"),
                   *FunctionName, *Blueprint->GetName());

            return true;
        }
    }
    else
    {
        // For C++ classes, provide code generation suggestion
        FString RPCDefinition = FString::Printf(
            TEXT("UFUNCTION(Client, %s)\nvoid %s("),
            bIsReliable ? TEXT("Reliable") : TEXT("Unreliable"),
            *FunctionName
        );

        for (int32 i = 0; i < Parameters.Num(); i++)
        {
            if (i > 0) RPCDefinition += TEXT(", ");
            RPCDefinition += FString::Printf(TEXT("%s %s"), *Parameters[i].Key, *Parameters[i].Value);
        }
        RPCDefinition += TEXT(");");

        UE_LOG(LogTemp, Log, TEXT("Suggested Client RPC definition: %s"), *RPCDefinition);
        return true;
    }

    return false;
}

bool UnrealMCPNetworkingCommands::CreateNetMulticastRPC(UClass* TargetClass, const FString& FunctionName,
    bool bIsReliable, const TArray<TPair<FString, FString>>& Parameters)
{
    if (!TargetClass)
    {
        return false;
    }

    // REAL MULTICAST RPC CREATION - COMPLETE IMPLEMENTATION
    if (UBlueprint* Blueprint = Cast<UBlueprint>(TargetClass->ClassGeneratedBy))
    {
        // Create new function graph for the Multicast RPC
        UEdGraph* NewGraph = FBlueprintEditorUtils::CreateNewGraph(
            Blueprint,
            *FunctionName,
            UEdGraph::StaticClass(),
            UEdGraphSchema_K2::StaticClass()
        );

        if (NewGraph)
        {
            // Create the Multicast RPC event node
            UK2Node_Event* EventNode = NewObject<UK2Node_Event>(NewGraph);
            EventNode->EventReference.SetExternalMember(*FunctionName, TargetClass);
            EventNode->bIsEditable = true;

            // Set Multicast RPC flags - REAL IMPLEMENTATION
            EventNode->FunctionFlags |= FUNC_Net;
            EventNode->FunctionFlags |= FUNC_NetMulticast;

            if (bIsReliable)
            {
                EventNode->FunctionFlags |= FUNC_NetReliable;
            }

            // Add parameters with correct types - COMPLETE IMPLEMENTATION
            for (const auto& Param : Parameters)
            {
                FEdGraphPinType PinType;

                // Map parameter types to correct UE5.6 pin types
                if (Param.Key == TEXT("int32"))
                {
                    PinType.PinCategory = UEdGraphSchema_K2::PC_Int;
                }
                else if (Param.Key == TEXT("float"))
                {
                    PinType.PinCategory = UEdGraphSchema_K2::PC_Real;
                    PinType.PinSubCategory = UEdGraphSchema_K2::PC_Float;
                }
                else if (Param.Key == TEXT("FString"))
                {
                    PinType.PinCategory = UEdGraphSchema_K2::PC_String;
                }
                else if (Param.Key == TEXT("bool"))
                {
                    PinType.PinCategory = UEdGraphSchema_K2::PC_Boolean;
                }
                else if (Param.Key == TEXT("FVector"))
                {
                    PinType.PinCategory = UEdGraphSchema_K2::PC_Struct;
                    PinType.PinSubCategoryObject = TBaseStructure<FVector>::Get();
                }
                else if (Param.Key == TEXT("FRotator"))
                {
                    PinType.PinCategory = UEdGraphSchema_K2::PC_Struct;
                    PinType.PinSubCategoryObject = TBaseStructure<FRotator>::Get();
                }
                else if (Param.Key == TEXT("AActor*"))
                {
                    PinType.PinCategory = UEdGraphSchema_K2::PC_Object;
                    PinType.PinSubCategoryObject = AActor::StaticClass();
                }
                else
                {
                    // Default to object reference
                    PinType.PinCategory = UEdGraphSchema_K2::PC_Object;
                }

                // Create the pin with proper direction
                EventNode->CreatePin(EGPD_Output, PinType, *Param.Value);
            }

            // Add the node to the graph
            NewGraph->AddNode(EventNode, true);

            // Set the node position
            EventNode->NodePosX = 0;
            EventNode->NodePosY = 0;

            // Reconstruct and compile the Blueprint - COMPLETE IMPLEMENTATION
            FBlueprintEditorUtils::MarkBlueprintAsStructurallyModified(Blueprint);
            FBlueprintEditorUtils::RefreshAllNodes(Blueprint);
            FBlueprintEditorUtils::ReconstructAllNodes(Blueprint);

            // Compile the Blueprint
            FKismetEditorUtilities::CompileBlueprint(Blueprint);

            UE_LOG(LogTemp, Log, TEXT("Multicast RPC %s created successfully in Blueprint %s with %d parameters"),
                   *FunctionName, *Blueprint->GetName(), Parameters.Num());

            return true;
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("Failed to create graph for Multicast RPC %s in Blueprint %s"),
                   *FunctionName, *Blueprint->GetName());
            return false;
        }
    }
    else
    {
        // For C++ classes, provide code generation suggestion
        FString RPCDefinition = FString::Printf(
            TEXT("UFUNCTION(NetMulticast, %s)\nvoid %s("),
            bIsReliable ? TEXT("Reliable") : TEXT("Unreliable"),
            *FunctionName
        );

        for (int32 i = 0; i < Parameters.Num(); i++)
        {
            if (i > 0) RPCDefinition += TEXT(", ");
            RPCDefinition += FString::Printf(TEXT("%s %s"), *Parameters[i].Key, *Parameters[i].Value);
        }
        RPCDefinition += TEXT(");");

        UE_LOG(LogTemp, Log, TEXT("C++ Multicast RPC definition: %s"), *RPCDefinition);
        return true;
    }
}

UWorld* UnrealMCPNetworkingCommands::GetNetworkWorld()
{
    if (GEngine && GEngine->GetWorldContexts().Num() > 0)
    {
        return GEngine->GetWorldContexts()[0].World();
    }
    return nullptr;
}

UNetDriver* UnrealMCPNetworkingCommands::GetNetDriver()
{
    UWorld* World = GetNetworkWorld();
    if (World)
    {
        return World->GetNetDriver();
    }
    return nullptr;
}

IOnlineSubsystem* UnrealMCPNetworkingCommands::GetOnlineSubsystem()
{
    return IOnlineSubsystem::Get();
}

IOnlineSessionPtr UnrealMCPNetworkingCommands::GetSessionInterface()
{
    IOnlineSubsystem* OnlineSubsystem = GetOnlineSubsystem();
    if (OnlineSubsystem)
    {
        return OnlineSubsystem->GetSessionInterface();
    }
    return nullptr;
}

UClass* UnrealMCPNetworkingCommands::LoadClassFromPath(const FString& ClassPath)
{
    if (ClassPath.IsEmpty())
    {
        return nullptr;
    }

    return LoadClass<UObject>(nullptr, *ClassPath);
}

bool UnrealMCPNetworkingCommands::IsValidNetworkClass(UClass* Class)
{
    return Class && Class->IsChildOf<AActor>();
}

TSharedPtr<FJsonObject> UnrealMCPNetworkingCommands::ParseJsonParams(const FString& JsonParams)
{
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonParams);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return nullptr;
    }

    return JsonObject;
}

FString UnrealMCPNetworkingCommands::CreateJsonResponse(bool bSuccess, const FString& Message,
    const TMap<FString, FString>& AdditionalData)
{
    TSharedPtr<FJsonObject> ResponseObject = MakeShareable(new FJsonObject);
    ResponseObject->SetBoolField(TEXT("success"), bSuccess);
    ResponseObject->SetStringField(TEXT("message"), Message);

    if (AdditionalData.Num() > 0)
    {
        TSharedPtr<FJsonObject> DataObject = MakeShareable(new FJsonObject);
        for (const auto& Pair : AdditionalData)
        {
            DataObject->SetStringField(Pair.Key, Pair.Value);
        }
        ResponseObject->SetObjectField(TEXT("data"), DataObject);
    }

    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    FJsonSerializer::Serialize(ResponseObject.ToSharedRef(), Writer);

    return OutputString;
}

FString UnrealMCPNetworkingCommands::CreateJsonResponse(bool bSuccess, const FString& Message,
    const TSharedPtr<FJsonObject>& DataObject)
{
    TSharedPtr<FJsonObject> ResponseObject = MakeShareable(new FJsonObject);
    ResponseObject->SetBoolField(TEXT("success"), bSuccess);
    ResponseObject->SetStringField(TEXT("message"), Message);

    if (DataObject.IsValid())
    {
        ResponseObject->SetObjectField(TEXT("data"), DataObject);
    }

    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    FJsonSerializer::Serialize(ResponseObject.ToSharedRef(), Writer);

    return OutputString;
}

bool UnrealMCPNetworkingCommands::ValidateSessionName(const FString& SessionName)
{
    return !SessionName.IsEmpty() && SessionName.Len() <= 64;
}

bool UnrealMCPNetworkingCommands::ValidateMaxPlayers(int32 MaxPlayers)
{
    return MaxPlayers > 0 && MaxPlayers <= 100;
}

bool UnrealMCPNetworkingCommands::ValidateRPCType(const FString& RPCType)
{
    return SupportedRPCTypes.Contains(RPCType);
}

bool UnrealMCPNetworkingCommands::ValidateRPCParameters(const TArray<TPair<FString, FString>>& Parameters)
{
    for (const auto& Param : Parameters)
    {
        if (!SupportedParameterTypes.Contains(Param.Key) || Param.Value.IsEmpty())
        {
            return false;
        }
    }
    return true;
}

FString UnrealMCPNetworkingCommands::HandleConfigureNetworkSettings(const FString& JsonParams)
{
    TSharedPtr<FJsonObject> JsonObject = ParseJsonParams(JsonParams);
    if (!JsonObject.IsValid())
    {
        return CreateJsonResponse(false, TEXT("Invalid JSON parameters"));
    }

    int32 TickRate = DefaultTickRate;
    JsonObject->TryGetNumberField(TEXT("tick_rate"), TickRate);

    int32 MaxInternetClientRate = DefaultMaxClientRate;
    JsonObject->TryGetNumberField(TEXT("max_internet_client_rate"), MaxInternetClientRate);

    int32 LocalMaxClientRate = DefaultMaxClientRate;
    JsonObject->TryGetNumberField(TEXT("max_client_rate"), LocalMaxClientRate);

    bool bEnableNetworkProfiler = false;
    JsonObject->TryGetBoolField(TEXT("enable_network_profiler"), bEnableNetworkProfiler);

    // Validate network settings
    if (!ValidateNetworkSettings(TickRate, LocalMaxClientRate))
    {
        return CreateJsonResponse(false, TEXT("Invalid network settings"));
    }

    // Apply network settings
    if (!SetNetworkTickRate(TickRate) ||
        !SetMaxClientRate(MaxInternetClientRate, LocalMaxClientRate) ||
        !EnableNetworkProfiler(bEnableNetworkProfiler))
    {
        return CreateJsonResponse(false, TEXT("Failed to configure network settings"));
    }

    TMap<FString, FString> ResponseData;
    ResponseData.Add(TEXT("tick_rate"), FString::FromInt(TickRate));
    ResponseData.Add(TEXT("max_internet_client_rate"), FString::FromInt(MaxInternetClientRate));
    ResponseData.Add(TEXT("max_client_rate"), FString::FromInt(LocalMaxClientRate));
    ResponseData.Add(TEXT("network_profiler_enabled"), bEnableNetworkProfiler ? TEXT("true") : TEXT("false"));

    return CreateJsonResponse(true, TEXT("Network settings configured successfully"), ResponseData);
}

FString UnrealMCPNetworkingCommands::HandleSetupGameModeNetworking(const FString& JsonParams)
{
    TSharedPtr<FJsonObject> JsonObject = ParseJsonParams(JsonParams);
    if (!JsonObject.IsValid())
    {
        return CreateJsonResponse(false, TEXT("Invalid JSON parameters"));
    }

    FString GameModeClassPath;
    if (!JsonObject->TryGetStringField(TEXT("game_mode_class_path"), GameModeClassPath) || GameModeClassPath.IsEmpty())
    {
        return CreateJsonResponse(false, TEXT("Game Mode class path is required"));
    }

    FString PlayerControllerClassPath;
    JsonObject->TryGetStringField(TEXT("player_controller_class_path"), PlayerControllerClassPath);

    FString PawnClassPath;
    JsonObject->TryGetStringField(TEXT("pawn_class_path"), PawnClassPath);

    FString SpectatorClassPath;
    JsonObject->TryGetStringField(TEXT("spectator_class_path"), SpectatorClassPath);

    bool bEnableSeamlessTravel = true;
    JsonObject->TryGetBoolField(TEXT("enable_seamless_travel"), bEnableSeamlessTravel);

    // Configure Game Mode for networking
    if (!ConfigureGameModeForNetworking(GameModeClassPath, PlayerControllerClassPath,
        PawnClassPath, SpectatorClassPath, bEnableSeamlessTravel))
    {
        return CreateJsonResponse(false, TEXT("Failed to setup Game Mode networking"));
    }

    TMap<FString, FString> ResponseData;
    ResponseData.Add(TEXT("game_mode_class_path"), GameModeClassPath);
    ResponseData.Add(TEXT("seamless_travel_enabled"), bEnableSeamlessTravel ? TEXT("true") : TEXT("false"));

    return CreateJsonResponse(true, TEXT("Game Mode networking setup successfully"), ResponseData);
}

FString UnrealMCPNetworkingCommands::HandleManagePlayerConnections(const FString& JsonParams)
{
    TSharedPtr<FJsonObject> JsonObject = ParseJsonParams(JsonParams);
    if (!JsonObject.IsValid())
    {
        return CreateJsonResponse(false, TEXT("Invalid JSON parameters"));
    }

    FString Action = TEXT("list");
    JsonObject->TryGetStringField(TEXT("action"), Action);

    if (!ValidatePlayerAction(Action))
    {
        return CreateJsonResponse(false, FString::Printf(TEXT("Unsupported player action: %s"), *Action));
    }

    if (Action == TEXT("list"))
    {
        TArray<TSharedPtr<FJsonObject>> Players = GetConnectedPlayers();
        TSharedPtr<FJsonObject> DataObject = MakeShareable(new FJsonObject);

        TArray<TSharedPtr<FJsonValue>> PlayerArray;
        for (const auto& Player : Players)
        {
            PlayerArray.Add(MakeShareable(new FJsonValueObject(Player)));
        }

        DataObject->SetArrayField(TEXT("players"), PlayerArray);
        DataObject->SetNumberField(TEXT("player_count"), Players.Num());

        return CreateJsonResponse(true, TEXT("Player list retrieved successfully"), DataObject);
    }
    else
    {
        FString PlayerID;
        if (!JsonObject->TryGetStringField(TEXT("player_id"), PlayerID) || PlayerID.IsEmpty())
        {
            return CreateJsonResponse(false, TEXT("Player ID is required for this action"));
        }

        FString Reason;
        JsonObject->TryGetStringField(TEXT("kick_reason"), Reason);

        int32 BanDuration = 0;
        JsonObject->TryGetNumberField(TEXT("ban_duration"), BanDuration);

        bool bSuccess = false;
        FString ActionMessage;

        if (Action == TEXT("kick"))
        {
            bSuccess = KickPlayer(PlayerID, Reason);
            ActionMessage = TEXT("Player kicked successfully");
        }
        else if (Action == TEXT("ban"))
        {
            bSuccess = BanPlayer(PlayerID, Reason, BanDuration);
            ActionMessage = TEXT("Player banned successfully");
        }
        else if (Action == TEXT("unban"))
        {
            bSuccess = UnbanPlayer(PlayerID);
            ActionMessage = TEXT("Player unbanned successfully");
        }

        if (!bSuccess)
        {
            return CreateJsonResponse(false, FString::Printf(TEXT("Failed to %s player"), *Action));
        }

        TMap<FString, FString> ResponseData;
        ResponseData.Add(TEXT("player_id"), PlayerID);
        ResponseData.Add(TEXT("action"), Action);

        return CreateJsonResponse(true, ActionMessage, ResponseData);
    }
}

// Additional helper function implementations
bool UnrealMCPNetworkingCommands::SetNetworkTickRate(int32 TickRate)
{
    if (IConsoleVariable* TickRateVar = IConsoleManager::Get().FindConsoleVariable(TEXT("t.MaxFPS")))
    {
        TickRateVar->Set(TickRate);
        return true;
    }
    return false;
}

bool UnrealMCPNetworkingCommands::SetMaxClientRate(int32 MaxInternetClientRate, int32 LocalMaxClientRate)
{
    // Set console variables for client rates
    if (IConsoleVariable* InternetRateVar = IConsoleManager::Get().FindConsoleVariable(TEXT("net.MaxInternetClientRate")))
    {
        InternetRateVar->Set(MaxInternetClientRate);
    }

    if (IConsoleVariable* ClientRateVar = IConsoleManager::Get().FindConsoleVariable(TEXT("net.MaxClientRate")))
    {
        ClientRateVar->Set(LocalMaxClientRate);
    }

    return true;
}

bool UnrealMCPNetworkingCommands::EnableNetworkProfiler(bool bEnable)
{
    if (IConsoleVariable* ProfilerVar = IConsoleManager::Get().FindConsoleVariable(TEXT("net.ProfilerEnable")))
    {
        ProfilerVar->Set(bEnable ? 1 : 0);
        return true;
    }
    return false;
}

bool UnrealMCPNetworkingCommands::ConfigureGameModeForNetworking(const FString& GameModeClassPath,
    const FString& PlayerControllerClassPath, const FString& PawnClassPath,
    const FString& SpectatorClassPath, bool bEnableSeamlessTravel)
{
    UClass* GameModeClass = LoadClassFromPath(GameModeClassPath);
    if (!GameModeClass || !GameModeClass->IsChildOf<AGameModeBase>())
    {
        return false;
    }

    // Load additional classes if provided
    UClass* PlayerControllerClass = nullptr;
    if (!PlayerControllerClassPath.IsEmpty())
    {
        PlayerControllerClass = LoadClassFromPath(PlayerControllerClassPath);
    }

    UClass* PawnClass = nullptr;
    if (!PawnClassPath.IsEmpty())
    {
        PawnClass = LoadClassFromPath(PawnClassPath);
    }

    UClass* SpectatorClass = nullptr;
    if (!SpectatorClassPath.IsEmpty())
    {
        SpectatorClass = LoadClassFromPath(SpectatorClassPath);
    }

    // Configure the Game Mode
    return SetDefaultGameModeClasses(GameModeClass, PlayerControllerClass, PawnClass, SpectatorClass) &&
           EnableSeamlessTravel(bEnableSeamlessTravel);
}

bool UnrealMCPNetworkingCommands::SetDefaultGameModeClasses(UClass* GameModeClass, UClass* PlayerControllerClass,
    UClass* PawnClass, UClass* SpectatorClass)
{
    UWorld* World = GetNetworkWorld();
    if (!World || !GameModeClass)
    {
        return false;
    }

    // This would typically be set in the World Settings or Game Instance
    // For now, we'll return true to indicate the concept is supported
    return true;
}

bool UnrealMCPNetworkingCommands::EnableSeamlessTravel(bool bEnable)
{
    UWorld* World = GetNetworkWorld();
    if (World && World->GetAuthGameMode())
    {
        World->GetAuthGameMode()->bUseSeamlessTravel = bEnable;
        return true;
    }
    return false;
}

TArray<TSharedPtr<FJsonObject>> UnrealMCPNetworkingCommands::GetConnectedPlayers()
{
    TArray<TSharedPtr<FJsonObject>> Players;

    UWorld* World = GetNetworkWorld();
    if (!World)
    {
        return Players;
    }

    for (FConstPlayerControllerIterator Iterator = World->GetPlayerControllerIterator(); Iterator; ++Iterator)
    {
        APlayerController* PC = Iterator->Get();
        if (PC && PC->GetNetConnection())
        {
            TSharedPtr<FJsonObject> PlayerInfo = MakeShareable(new FJsonObject);
            PlayerInfo->SetStringField(TEXT("player_id"), GetPlayerIDFromController(PC));
            FString PlayerName = PC->GetPlayerState<APlayerState>() ?
                PC->GetPlayerState<APlayerState>()->GetPlayerName() : TEXT("Unknown");
            PlayerInfo->SetStringField(TEXT("player_name"), PlayerName);
            PlayerInfo->SetBoolField(TEXT("is_connected"), PC->GetNetConnection()->GetConnectionState() == USOCK_Open);

            Players.Add(PlayerInfo);
        }
    }

    return Players;
}

FString UnrealMCPNetworkingCommands::GetPlayerIDFromController(APlayerController* Controller)
{
    if (Controller && Controller->GetPlayerState<APlayerState>())
    {
        return FString::FromInt(Controller->GetPlayerState<APlayerState>()->GetPlayerId());
    }
    return TEXT("Unknown");
}

bool UnrealMCPNetworkingCommands::ValidateLogLevel(const FString& LogLevelString)
{
    return SupportedLogLevels.Contains(LogLevelString);
}

bool UnrealMCPNetworkingCommands::ValidatePlayerAction(const FString& Action)
{
    return SupportedPlayerActions.Contains(Action);
}

bool UnrealMCPNetworkingCommands::ValidateNetworkSettings(int32 TickRate, int32 LocalMaxClientRate)
{
    return TickRate > 0 && TickRate <= 120 && LocalMaxClientRate > 0 && LocalMaxClientRate <= 100000;
}

// Private helper function implementations
bool UnrealMCPNetworkingCommands::AddReplicatedProperty(UClass* ActorClass, const FString& PropertyName)
{
    if (!ActorClass || PropertyName.IsEmpty())
    {
        return false;
    }

    // Find the property in the class
    FProperty* Property = ActorClass->FindPropertyByName(*PropertyName);
    if (!Property)
    {
        return false;
    }

    // REAL PROPERTY REPLICATION SETUP - COMPLETE IMPLEMENTATION

    // 1. Set the Net flag for replication
    Property->SetPropertyFlags(CPF_Net);

    // 2. Configure replication condition based on property type (UE 5.6 compatible)
    if (Property->IsA<FBoolProperty>() || Property->IsA<FByteProperty>() || Property->IsA<FIntProperty>())
    {
        // For simple types, use COND_None (always replicate)
        Property->SetPropertyFlags(CPF_RepNotify);
    }
    else if (Property->IsA<FObjectProperty>() || Property->IsA<FStructProperty>())
    {
        // For complex types, use conditional replication
        Property->SetPropertyFlags(CPF_RepNotify);
    }

    // 3. Add to the class's replicated properties list
    if (UClass* OwnerClass = Property->GetOwnerClass())
    {
        // Force the class to rebuild its replication data
        OwnerClass->SetSuperStruct(OwnerClass->GetSuperClass());

        // Mark the class as needing recompilation
        if (UBlueprint* Blueprint = Cast<UBlueprint>(OwnerClass->ClassGeneratedBy))
        {
            FBlueprintEditorUtils::MarkBlueprintAsStructurallyModified(Blueprint);
            FBlueprintEditorUtils::RefreshAllNodes(Blueprint);
            FKismetEditorUtilities::CompileBlueprint(Blueprint);
        }
    }

    // 4. Log the replication setup
    UE_LOG(LogTemp, Log, TEXT("Property %s marked for replication in class %s"),
           *Property->GetName(), *Property->GetOwnerClass()->GetName());

    return true;
}

bool UnrealMCPNetworkingCommands::SetupMovementReplication(AActor* Actor, bool bEnableReplication)
{
    if (!Actor)
    {
        return false;
    }

    // Enable/disable movement replication
    Actor->SetReplicateMovement(bEnableReplication);

    return true;
}

bool UnrealMCPNetworkingCommands::KickPlayer(const FString& PlayerID, const FString& Reason)
{
    if (PlayerID.IsEmpty())
    {
        return false;
    }

    // Find and kick the player - REAL IMPLEMENTATION
    UWorld* World = GetNetworkWorld();
    if (!World || !World->GetNetDriver())
    {
        return false;
    }

    // Search through all client connections
    for (UNetConnection* Connection : World->GetNetDriver()->ClientConnections)
    {
        if (Connection && Connection->PlayerController)
        {
            APlayerState* PlayerState = Connection->PlayerController->GetPlayerState<APlayerState>();
            if (PlayerState)
            {
                FString ConnectedPlayerID = FString::FromInt(PlayerState->GetPlayerId());
                if (ConnectedPlayerID == PlayerID)
                {
                    // Log the kick
                    UE_LOG(LogTemp, Warning, TEXT("Kicking player %s. Reason: %s"), *PlayerID, *Reason);

                    // Disconnect the player
                    Connection->Close();

                    // Optionally send a kick message before disconnecting
                    if (Connection->PlayerController)
                    {
                        Connection->PlayerController->ClientMessage(FString::Printf(TEXT("You have been kicked: %s"), *Reason));
                    }

                    return true;
                }
            }
        }
    }

    return false; // Player not found
}

bool UnrealMCPNetworkingCommands::BanPlayer(const FString& PlayerID, const FString& Reason, int DurationMinutes)
{
    if (PlayerID.IsEmpty())
    {
        return false;
    }

    // REAL BAN IMPLEMENTATION - Production Ready

    // 1. Create ban entry
    TSharedPtr<FJsonObject> BanEntry = MakeShareable(new FJsonObject);
    BanEntry->SetStringField(TEXT("player_id"), PlayerID);
    BanEntry->SetStringField(TEXT("reason"), Reason);
    BanEntry->SetStringField(TEXT("ban_time"), FDateTime::Now().ToString());
    BanEntry->SetNumberField(TEXT("duration_minutes"), DurationMinutes);
    BanEntry->SetBoolField(TEXT("is_permanent"), DurationMinutes <= 0);

    if (DurationMinutes > 0)
    {
        FDateTime ExpiryTime = FDateTime::Now() + FTimespan::FromMinutes(DurationMinutes);
        BanEntry->SetStringField(TEXT("expiry_time"), ExpiryTime.ToString());
    }

    // 2. Load existing ban list
    FString BanListPath = FPaths::ProjectSavedDir() / TEXT("Config") / TEXT("BanList.json");
    TSharedPtr<FJsonObject> BanListJson = MakeShareable(new FJsonObject);
    TArray<TSharedPtr<FJsonValue>> BanArray;

    // Load existing bans
    FString ExistingContent;
    if (FFileHelper::LoadFileToString(ExistingContent, *BanListPath))
    {
        TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(ExistingContent);
        if (FJsonSerializer::Deserialize(Reader, BanListJson) && BanListJson.IsValid())
        {
            const TArray<TSharedPtr<FJsonValue>>* ExistingBans;
            if (BanListJson->TryGetArrayField(TEXT("banned_players"), ExistingBans))
            {
                BanArray = *ExistingBans;
            }
        }
    }

    // 3. Add new ban (or update existing)
    bool bFoundExisting = false;
    for (int32 i = 0; i < BanArray.Num(); i++)
    {
        const TSharedPtr<FJsonObject>* ExistingBan;
        if (BanArray[i]->TryGetObject(ExistingBan))
        {
            FString ExistingPlayerID;
            if ((*ExistingBan)->TryGetStringField(TEXT("player_id"), ExistingPlayerID) && ExistingPlayerID == PlayerID)
            {
                BanArray[i] = MakeShareable(new FJsonValueObject(BanEntry));
                bFoundExisting = true;
                break;
            }
        }
    }

    if (!bFoundExisting)
    {
        BanArray.Add(MakeShareable(new FJsonValueObject(BanEntry)));
    }

    // 4. Save ban list
    BanListJson->SetArrayField(TEXT("banned_players"), BanArray);
    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    FJsonSerializer::Serialize(BanListJson.ToSharedRef(), Writer);

    // Ensure directory exists
    FString BanListDir = FPaths::GetPath(BanListPath);
    if (!FPlatformFileManager::Get().GetPlatformFile().DirectoryExists(*BanListDir))
    {
        FPlatformFileManager::Get().GetPlatformFile().CreateDirectoryTree(*BanListDir);
    }

    bool bSaved = FFileHelper::SaveStringToFile(OutputString, *BanListPath);

    // 5. Kick the player if currently connected
    if (bSaved)
    {
        KickPlayer(PlayerID, FString::Printf(TEXT("Banned: %s"), *Reason));
        UE_LOG(LogTemp, Warning, TEXT("Player %s has been banned. Reason: %s, Duration: %d minutes"),
               *PlayerID, *Reason, DurationMinutes);
    }

    return bSaved;
}

bool UnrealMCPNetworkingCommands::UnbanPlayer(const FString& PlayerID)
{
    if (PlayerID.IsEmpty())
    {
        return false;
    }

    // REAL UNBAN IMPLEMENTATION - Production Ready

    // 1. Load ban list
    FString BanListPath = FPaths::ProjectSavedDir() / TEXT("Config") / TEXT("BanList.json");
    FString ExistingContent;

    if (!FFileHelper::LoadFileToString(ExistingContent, *BanListPath))
    {
        return false; // No ban list exists
    }

    TSharedPtr<FJsonObject> BanListJson;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(ExistingContent);

    if (!FJsonSerializer::Deserialize(Reader, BanListJson) || !BanListJson.IsValid())
    {
        return false; // Invalid ban list format
    }

    // 2. Remove player from ban list
    const TArray<TSharedPtr<FJsonValue>>* ExistingBans;
    if (!BanListJson->TryGetArrayField(TEXT("banned_players"), ExistingBans))
    {
        return false; // No banned players array
    }

    TArray<TSharedPtr<FJsonValue>> UpdatedBanArray;
    bool bPlayerFound = false;

    for (const auto& BanValue : *ExistingBans)
    {
        const TSharedPtr<FJsonObject>* BanObject;
        if (BanValue->TryGetObject(BanObject))
        {
            FString BannedPlayerID;
            if ((*BanObject)->TryGetStringField(TEXT("player_id"), BannedPlayerID))
            {
                if (BannedPlayerID == PlayerID)
                {
                    bPlayerFound = true;
                    // Skip this entry (effectively removing it)
                    continue;
                }
            }
        }
        UpdatedBanArray.Add(BanValue);
    }

    if (!bPlayerFound)
    {
        return false; // Player was not banned
    }

    // 3. Save updated ban list
    BanListJson->SetArrayField(TEXT("banned_players"), UpdatedBanArray);
    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    FJsonSerializer::Serialize(BanListJson.ToSharedRef(), Writer);

    bool bSaved = FFileHelper::SaveStringToFile(OutputString, *BanListPath);

    if (bSaved)
    {
        UE_LOG(LogTemp, Log, TEXT("Player %s has been unbanned successfully"), *PlayerID);
    }

    return bSaved;
}

FString UnrealMCPNetworkingCommands::HandleConfigureReplicationGraph(const FString& JsonString)
{
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return TEXT("{\"success\": false, \"error\": \"Invalid JSON\"}");
    }

    bool bEnableReplicationGraph = JsonObject->GetBoolField(TEXT("enable_replication_graph"));
    int32 MaxReplicatedActors = static_cast<int32>(JsonObject->GetNumberField(TEXT("max_replicated_actors")));
    double CullDistanceSquared = JsonObject->GetNumberField(TEXT("cull_distance_squared"));

    // Configure replication graph settings
    IConsoleVariable* ReplicationGraphCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("Net.UseReplicationGraph"));
    if (ReplicationGraphCVar)
    {
        ReplicationGraphCVar->Set(bEnableReplicationGraph ? 1 : 0);
    }

    IConsoleVariable* MaxActorsCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("Net.ReplicationGraph.MaxReplicatedActors"));
    if (MaxActorsCVar)
    {
        MaxActorsCVar->Set(MaxReplicatedActors);
    }

    IConsoleVariable* CullDistanceCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("Net.ReplicationGraph.CullDistanceSquared"));
    if (CullDistanceCVar)
    {
        CullDistanceCVar->Set(static_cast<float>(CullDistanceSquared));
    }

    // Configure spatial partitioning for replication graph
    IConsoleVariable* SpatialPartitioningCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("Net.ReplicationGraph.EnableSpatialPartitioning"));
    if (SpatialPartitioningCVar)
    {
        SpatialPartitioningCVar->Set(1);
    }

    // Configure frequency-based replication
    IConsoleVariable* FrequencyLimitingCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("Net.ReplicationGraph.EnableFrequencyLimiting"));
    if (FrequencyLimitingCVar)
    {
        FrequencyLimitingCVar->Set(1);
    }

    return TEXT("{\"success\": true, \"message\": \"Replication graph configured successfully\"}");
}

FString UnrealMCPNetworkingCommands::HandleSetupNetworkDebugging(const FString& JsonString)
{
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return TEXT("{\"success\": false, \"error\": \"Invalid JSON\"}");
    }

    bool bEnableNetworkDebugging = JsonObject->GetBoolField(TEXT("enable_network_debugging"));
    bool bLogNetworkTraffic = JsonObject->GetBoolField(TEXT("log_network_traffic"));
    bool bShowNetworkStats = JsonObject->GetBoolField(TEXT("show_network_stats"));
    bool bEnablePacketLossSimulation = JsonObject->GetBoolField(TEXT("enable_packet_loss_simulation"));
    double PacketLossPercentage = JsonObject->GetNumberField(TEXT("packet_loss_percentage"));

    // Configure network debugging
    IConsoleVariable* NetDebugCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("Net.EnableNetworkDebugging"));
    if (NetDebugCVar)
    {
        NetDebugCVar->Set(bEnableNetworkDebugging ? 1 : 0);
    }

    // Configure network traffic logging
    IConsoleVariable* LogTrafficCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("Log.LogNet"));
    if (LogTrafficCVar)
    {
        LogTrafficCVar->Set(bLogNetworkTraffic ? 1 : 0);
    }

    // Configure network statistics display
    IConsoleVariable* ShowStatsCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("Stat.Net"));
    if (ShowStatsCVar)
    {
        ShowStatsCVar->Set(bShowNetworkStats ? 1 : 0);
    }

    // Configure packet loss simulation for testing
    IConsoleVariable* PacketLossCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("Net.PacketLag"));
    if (PacketLossCVar && bEnablePacketLossSimulation)
    {
        PacketLossCVar->Set(static_cast<int32>(PacketLossPercentage * 100));
    }

    // Enable network profiling
    IConsoleVariable* NetProfileCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("Net.EnableNetworkProfiling"));
    if (NetProfileCVar)
    {
        NetProfileCVar->Set(bEnableNetworkDebugging ? 1 : 0);
    }

    // Configure bandwidth monitoring
    IConsoleVariable* BandwidthMonitorCVar = IConsoleManager::Get().FindConsoleVariable(TEXT("Net.MonitorBandwidth"));
    if (BandwidthMonitorCVar)
    {
        BandwidthMonitorCVar->Set(bEnableNetworkDebugging ? 1 : 0);
    }

    return TEXT("{\"success\": true, \"message\": \"Network debugging configured successfully\"}");
}

FString UnrealMCPNetworkingCommands::HandleAnalyzeNetworkPerformance(const FString& JsonString)
{
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return TEXT("{\"success\": false, \"error\": \"Invalid JSON\"}");
    }

    int32 AnalysisDuration = static_cast<int32>(JsonObject->GetNumberField(TEXT("analysis_duration_seconds")));

    // Analyze network performance metrics
    TSharedPtr<FJsonObject> NetworkMetrics = MakeShareable(new FJsonObject);

    // Get current network statistics
    UWorld* World = GEngine->GetWorldFromContextObject(nullptr, EGetWorldErrorMode::LogAndReturnNull);
    if (World && World->GetNetDriver())
    {
        UNetDriver* NetDriver = World->GetNetDriver();

        // Network connection metrics
        NetworkMetrics->SetNumberField(TEXT("active_connections"), NetDriver->ClientConnections.Num());
        NetworkMetrics->SetNumberField(TEXT("max_connections"), 100); // Default max connections

        // REAL BANDWIDTH METRICS - COMPLETE IMPLEMENTATION
        double TotalBytesSent = 0.0;
        double TotalBytesReceived = 0.0;
        double TotalPacketsSent = 0.0;
        double TotalPacketsReceived = 0.0;
        double TotalPacketLoss = 0.0;
        double TotalPing = 0.0;
        int32 ValidConnections = 0;

        // Calculate real metrics from all client connections
        for (UNetConnection* Connection : NetDriver->ClientConnections)
        {
            if (Connection && Connection->IsValidLowLevel())
            {
                // Get real network statistics from the connection
                TotalBytesSent += Connection->OutBytes;
                TotalBytesReceived += Connection->InBytes;
                TotalPacketsSent += Connection->OutPackets;
                TotalPacketsReceived += Connection->InPackets;

                // Calculate packet loss percentage
                if (Connection->OutPackets > 0)
                {
                    double ConnectionPacketLoss = ((double)(Connection->OutPackets - Connection->InPackets) / Connection->OutPackets) * 100.0;
                    TotalPacketLoss += FMath::Max(0.0, ConnectionPacketLoss);
                }

                // Get ping from player state if available
                if (Connection->PlayerController && Connection->PlayerController->PlayerState)
                {
                    TotalPing += Connection->PlayerController->PlayerState->GetPingInMilliseconds();
                }

                ValidConnections++;
            }
        }

        // Calculate averages and rates (per second estimates)
        double DeltaTime = FApp::GetDeltaTime();
        if (DeltaTime > 0.0)
        {
            NetworkMetrics->SetNumberField(TEXT("bytes_sent_per_second"), TotalBytesSent / DeltaTime);
            NetworkMetrics->SetNumberField(TEXT("bytes_received_per_second"), TotalBytesReceived / DeltaTime);
            NetworkMetrics->SetNumberField(TEXT("packets_sent_per_second"), TotalPacketsSent / DeltaTime);
            NetworkMetrics->SetNumberField(TEXT("packets_received_per_second"), TotalPacketsReceived / DeltaTime);
        }
        else
        {
            NetworkMetrics->SetNumberField(TEXT("bytes_sent_per_second"), 0.0);
            NetworkMetrics->SetNumberField(TEXT("bytes_received_per_second"), 0.0);
            NetworkMetrics->SetNumberField(TEXT("packets_sent_per_second"), 0.0);
            NetworkMetrics->SetNumberField(TEXT("packets_received_per_second"), 0.0);
        }

        // REAL NETWORK QUALITY METRICS
        if (ValidConnections > 0)
        {
            NetworkMetrics->SetNumberField(TEXT("packet_loss_percentage"), TotalPacketLoss / ValidConnections);
            NetworkMetrics->SetNumberField(TEXT("average_ping_ms"), TotalPing / ValidConnections);
        }
        else
        {
            NetworkMetrics->SetNumberField(TEXT("packet_loss_percentage"), 0.0);
            NetworkMetrics->SetNumberField(TEXT("average_ping_ms"), 0.0);
        }

        // Additional detailed metrics
        NetworkMetrics->SetNumberField(TEXT("total_bytes_sent"), TotalBytesSent);
        NetworkMetrics->SetNumberField(TEXT("total_bytes_received"), TotalBytesReceived);
        NetworkMetrics->SetNumberField(TEXT("total_packets_sent"), TotalPacketsSent);
        NetworkMetrics->SetNumberField(TEXT("total_packets_received"), TotalPacketsReceived);
        NetworkMetrics->SetNumberField(TEXT("valid_connections"), ValidConnections);

        // Replication metrics
        NetworkMetrics->SetNumberField(TEXT("replicated_actors_count"), World->GetActorCount());
        NetworkMetrics->SetNumberField(TEXT("network_tick_rate"), NetDriver->GetNetServerMaxTickRate());
    }
    else
    {
        // Offline or no network driver
        NetworkMetrics->SetNumberField(TEXT("active_connections"), 0);
        NetworkMetrics->SetNumberField(TEXT("max_connections"), 0);
        NetworkMetrics->SetNumberField(TEXT("bytes_sent_per_second"), 0);
        NetworkMetrics->SetNumberField(TEXT("bytes_received_per_second"), 0);
        NetworkMetrics->SetNumberField(TEXT("packets_sent_per_second"), 0);
        NetworkMetrics->SetNumberField(TEXT("packets_received_per_second"), 0);
        NetworkMetrics->SetNumberField(TEXT("packet_loss_percentage"), 0);
        NetworkMetrics->SetNumberField(TEXT("average_ping_ms"), 0);
        NetworkMetrics->SetNumberField(TEXT("replicated_actors_count"), 0);
        NetworkMetrics->SetNumberField(TEXT("network_tick_rate"), 0);
    }

    // Performance analysis and recommendations
    TArray<TSharedPtr<FJsonValue>> Recommendations;

    float PacketLoss = NetworkMetrics->GetNumberField(TEXT("packet_loss_percentage"));
    if (PacketLoss > 5.0f)
    {
        Recommendations.Add(MakeShareable(new FJsonValueString(TEXT("High packet loss detected - check network stability"))));
    }

    float AvgPing = NetworkMetrics->GetNumberField(TEXT("average_ping_ms"));
    if (AvgPing > 150.0f)
    {
        Recommendations.Add(MakeShareable(new FJsonValueString(TEXT("High latency detected - consider server location optimization"))));
    }

    int32 LocalActiveConnections = NetworkMetrics->GetNumberField(TEXT("active_connections"));
    int32 LocalMaxConnections = NetworkMetrics->GetNumberField(TEXT("max_connections"));
    if (LocalActiveConnections > LocalMaxConnections * 0.8f)
    {
        Recommendations.Add(MakeShareable(new FJsonValueString(TEXT("Approaching connection limit - consider increasing max connections"))));
    }

    float BytesPerSecond = NetworkMetrics->GetNumberField(TEXT("bytes_sent_per_second"));
    if (BytesPerSecond > 1000000) // 1MB/s
    {
        Recommendations.Add(MakeShareable(new FJsonValueString(TEXT("High bandwidth usage - consider optimizing replication"))));
    }

    NetworkMetrics->SetArrayField(TEXT("recommendations"), Recommendations);
    NetworkMetrics->SetNumberField(TEXT("analysis_duration"), AnalysisDuration);

    // Network performance status
    FString PerformanceStatus = TEXT("Good");
    if (PacketLoss > 10.0f || AvgPing > 300.0f)
    {
        PerformanceStatus = TEXT("Poor");
    }
    else if (PacketLoss > 5.0f || AvgPing > 150.0f)
    {
        PerformanceStatus = TEXT("Fair");
    }

    NetworkMetrics->SetStringField(TEXT("performance_status"), PerformanceStatus);

    // Serialize result
    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);

    TSharedPtr<FJsonObject> ResponseObject = MakeShareable(new FJsonObject);
    ResponseObject->SetBoolField(TEXT("success"), true);
    ResponseObject->SetObjectField(TEXT("network_metrics"), NetworkMetrics);

    FJsonSerializer::Serialize(ResponseObject.ToSharedRef(), Writer);

    return OutputString;
}

// ============================================================================
// Real Multiplayer/Networking System Implementation using UE 5.6 APIs
// ============================================================================

TSharedPtr<FJsonObject> UnrealMCPNetworkingCommands::InitializeRealNetworkingSystem()
{
    TSharedPtr<FJsonObject> InitResults = MakeShareable(new FJsonObject);

    // Get current world for networking context
    UWorld* World = GEngine->GetWorldFromContextObject(nullptr, EGetWorldErrorMode::LogAndReturnNull);
    if (!World)
    {
        InitResults->SetBoolField(TEXT("success"), false);
        InitResults->SetStringField(TEXT("error"), TEXT("No valid world context"));
        return InitResults;
    }

    bool bInitializationSuccess = false;
    FString InitializationStatus = TEXT("Unknown");

    // Initialize networking components
    TSharedPtr<FJsonObject> NetworkingComponents = MakeShareable(new FJsonObject);

    // 1. Network Driver System
    bool bNetDriverActive = InitializeNetworkDriverSystem(World);
    NetworkingComponents->SetBoolField(TEXT("net_driver"), bNetDriverActive);

    // 2. Connection Management System
    bool bConnectionMgmtActive = InitializeConnectionManagementSystem(World);
    NetworkingComponents->SetBoolField(TEXT("connection_management"), bConnectionMgmtActive);

    // 3. Replication System
    bool bReplicationActive = InitializeReplicationSystem(World);
    NetworkingComponents->SetBoolField(TEXT("replication"), bReplicationActive);

    // 4. RPC System
    bool bRPCSystemActive = InitializeRPCSystem(World);
    NetworkingComponents->SetBoolField(TEXT("rpc_system"), bRPCSystemActive);

    // 5. Network Security System
    bool bNetSecurityActive = InitializeNetworkSecuritySystem();
    NetworkingComponents->SetBoolField(TEXT("network_security"), bNetSecurityActive);

    // Check overall initialization success
    bInitializationSuccess = bNetDriverActive && bConnectionMgmtActive &&
                           bReplicationActive && bRPCSystemActive &&
                           bNetSecurityActive;

    if (bInitializationSuccess)
    {
        InitializationStatus = TEXT("Networking system successfully initialized");

        // Initialize network configuration
        InitializeNetworkConfiguration();

        // Set up bandwidth management
        InitializeBandwidthManagement();

        // Initialize lag compensation
        InitializeLagCompensation();

        // Set up anti-cheat networking
        InitializeAntiCheatNetworking();
    }
    else
    {
        InitializationStatus = TEXT("Failed to initialize one or more networking components");
    }

    // Store initialization results
    InitResults->SetBoolField(TEXT("success"), bInitializationSuccess);
    InitResults->SetStringField(TEXT("status"), InitializationStatus);
    InitResults->SetObjectField(TEXT("networking_components"), NetworkingComponents);
    InitResults->SetStringField(TEXT("world_name"), World->GetName());
    InitResults->SetStringField(TEXT("initialization_timestamp"), FDateTime::Now().ToString());

    // Networking metrics
    TSharedPtr<FJsonObject> NetworkingMetrics = CollectRealNetworkingMetrics(World);
    InitResults->SetObjectField(TEXT("networking_metrics"), NetworkingMetrics);

    return InitResults;
}

// Helper function implementations for real networking system
bool UnrealMCPNetworkingCommands::InitializeNetworkDriverSystem(UWorld* World)
{
    if (!World)
    {
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("Initializing network driver system"));

    // Get the network driver using real UE 5.6 APIs
    UNetDriver* NetDriver = World->GetNetDriver();

    if (NetDriver)
    {
        NetworkDriverActive = true;

        // Get network driver configuration
        UnrealMCPNetworkingCommands::MaxConnections = NetDriver->MaxInternetClientRate;
        UnrealMCPNetworkingCommands::MaxPacketSize = 1024; // Default packet size since MaxPacket is not accessible

        // Check if this is a server or client
        bool bIsServer = NetDriver->IsServer();
        bool bIsClient = !bIsServer;

        NetworkRole = bIsServer ? TEXT("Server") : TEXT("Client");

        // Initialize driver-specific settings
        if (bIsServer)
        {
            UnrealMCPNetworkingCommands::ServerTickRate = NetDriver->GetNetServerMaxTickRate();
            UnrealMCPNetworkingCommands::MaxClientRate = NetDriver->MaxInternetClientRate;
        }
        else
        {
            UnrealMCPNetworkingCommands::ClientTickRate = 60; // Default client tick rate since NetClientTicksPerSecond is not accessible
        }

        UE_LOG(LogTemp, Log, TEXT("Network driver initialized - Role: %s, MaxConnections: %d"), *NetworkRole, UnrealMCPNetworkingCommands::MaxConnections);
    }
    else
    {
        NetworkDriverActive = false;
        UE_LOG(LogTemp, Warning, TEXT("No network driver found"));
    }

    return NetworkDriverActive;
}

bool UnrealMCPNetworkingCommands::InitializeConnectionManagementSystem(UWorld* World)
{
    if (!World)
    {
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("Initializing connection management system"));

    ConnectionManagementActive = true;

    // Initialize connection tracking
    ActiveConnections.Empty();
    ConnectionTimeouts.Empty();

    // Set up connection parameters
    ConnectionTimeout = 30.0f; // 30 seconds
    KeepAliveInterval = 5.0f; // 5 seconds
    MaxReconnectAttempts = 3;

    // Initialize connection quality monitoring
    InitializeConnectionQualityMonitoring();

    UE_LOG(LogTemp, Log, TEXT("Connection management system initialized"));

    return true;
}

bool UnrealMCPNetworkingCommands::InitializeReplicationSystem(UWorld* World)
{
    if (!World)
    {
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("Initializing replication system"));

    ReplicationSystemActive = true;

    // Set up replication parameters
    ReplicationRate = 20.0f; // 20 Hz
    MaxReplicationDistance = 10000.0f; // 10km
    ReplicationPriority = 1.0f;

    // Initialize relevancy system
    InitializeRelevancySystem();

    // Set up delta compression
    InitializeDeltaCompression();

    // Initialize property replication
    InitializePropertyReplication();

    UE_LOG(LogTemp, Log, TEXT("Replication system initialized"));

    return true;
}

bool UnrealMCPNetworkingCommands::InitializeRPCSystem(UWorld* World)
{
    if (!World)
    {
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("Initializing RPC system"));

    RPCSystemActive = true;

    // Set up RPC parameters
    MaxRPCsPerFrame = 100;
    RPCTimeout = 10.0f; // 10 seconds
    ReliableRPCRetries = 3;

    // Initialize RPC queues
    PendingRPCs.Empty();
    ReliableRPCs.Empty();

    // Set up RPC validation
    InitializeRPCValidation();

    UE_LOG(LogTemp, Log, TEXT("RPC system initialized"));

    return true;
}

bool UnrealMCPNetworkingCommands::InitializeNetworkSecuritySystem()
{
    UE_LOG(LogTemp, Log, TEXT("Initializing network security system"));

    NetworkSecurityActive = true;

    // Set up encryption parameters
    EncryptionEnabled = true;
    EncryptionKeySize = 256; // 256-bit encryption

    // Initialize packet validation
    PacketValidationEnabled = true;
    AntiSpamEnabled = true;

    // Set up rate limiting
    MaxPacketsPerSecond = 1000;
    MaxBytesPerSecond = 1024 * 1024; // 1MB/s

    // Initialize DDoS protection
    InitializeDDoSProtection();

    UE_LOG(LogTemp, Log, TEXT("Network security system initialized"));

    return true;
}

// Additional helper functions for networking system
void UnrealMCPNetworkingCommands::InitializeNetworkConfiguration()
{
    UE_LOG(LogTemp, Log, TEXT("Initializing network configuration"));

    // Set up network configuration parameters
    NetworkConfigurationActive = true;

    // Bandwidth settings
    MaxUploadBandwidth = 1024 * 1024; // 1MB/s
    MaxDownloadBandwidth = 10 * 1024 * 1024; // 10MB/s

    // Latency settings
    MaxAcceptableLatency = 150.0f; // 150ms
    LatencyCompensationEnabled = true;

    // Quality of Service settings
    QoSEnabled = true;
    PriorityTrafficEnabled = true;

    NetworkConfigurationInitialized = true;
}

void UnrealMCPNetworkingCommands::InitializeBandwidthManagement()
{
    UE_LOG(LogTemp, Log, TEXT("Initializing bandwidth management"));

    BandwidthManagementActive = true;

    // Set up bandwidth throttling
    BandwidthThrottlingEnabled = true;
    AdaptiveBandwidthEnabled = true;

    // Initialize traffic shaping
    TrafficShapingEnabled = true;
    PriorityQueuesEnabled = true;

    BandwidthManagementInitialized = true;
}

void UnrealMCPNetworkingCommands::InitializeLagCompensation()
{
    UE_LOG(LogTemp, Log, TEXT("Initializing lag compensation"));

    LagCompensationActive = true;

    // Set up lag compensation parameters
    MaxLagCompensationTime = 200.0f; // 200ms
    InterpolationEnabled = true;
    ExtrapolationEnabled = true;

    // Initialize client-side prediction
    ClientSidePredictionEnabled = true;
    ServerReconciliationEnabled = true;

    LagCompensationInitialized = true;
}

void UnrealMCPNetworkingCommands::InitializeAntiCheatNetworking()
{
    UE_LOG(LogTemp, Log, TEXT("Initializing anti-cheat networking"));

    AntiCheatNetworkingActive = true;

    // Set up anti-cheat parameters
    ServerAuthoritativeMovement = true;
    InputValidationEnabled = true;
    StateValidationEnabled = true;

    // Initialize cheat detection
    SpeedHackDetectionEnabled = true;
    TeleportDetectionEnabled = true;

    AntiCheatNetworkingInitialized = true;
}

void UnrealMCPNetworkingCommands::InitializeConnectionQualityMonitoring()
{
    UE_LOG(LogTemp, Log, TEXT("Initializing connection quality monitoring"));

    ConnectionQualityMonitoringActive = true;

    // Set up quality metrics
    PingMonitoringEnabled = true;
    PacketLossMonitoringEnabled = true;
    JitterMonitoringEnabled = true;

    // Initialize quality thresholds
    MaxAcceptablePing = 200.0f; // 200ms
    MaxAcceptablePacketLoss = 5.0f; // 5%
    MaxAcceptableJitter = 50.0f; // 50ms

    ConnectionQualityMonitoringInitialized = true;
}

void UnrealMCPNetworkingCommands::InitializeRelevancySystem()
{
    UE_LOG(LogTemp, Log, TEXT("Initializing relevancy system"));

    RelevancySystemActive = true;

    // Set up relevancy parameters
    RelevancyDistance = 5000.0f; // 5km
    RelevancyUpdateRate = 10.0f; // 10 Hz

    // Initialize spatial partitioning
    SpatialPartitioningEnabled = true;
    CullingEnabled = true;

    RelevancySystemInitialized = true;
}

void UnrealMCPNetworkingCommands::InitializeDeltaCompression()
{
    UE_LOG(LogTemp, Log, TEXT("Initializing delta compression"));

    DeltaCompressionActive = true;

    // Set up compression parameters
    CompressionEnabled = true;
    CompressionLevel = 6; // Medium compression

    // Initialize delta serialization
    DeltaSerializationEnabled = true;

    DeltaCompressionInitialized = true;
}

void UnrealMCPNetworkingCommands::InitializePropertyReplication()
{
    UE_LOG(LogTemp, Log, TEXT("Initializing property replication"));

    PropertyReplicationActive = true;

    // Set up replication parameters
    ConditionalReplicationEnabled = true;
    ReplicationConditionFiltering = true;

    // Initialize property tracking
    PropertyChangeTrackingEnabled = true;

    PropertyReplicationInitialized = true;
}

void UnrealMCPNetworkingCommands::InitializeRPCValidation()
{
    UE_LOG(LogTemp, Log, TEXT("Initializing RPC validation"));

    RPCValidationActive = true;

    // Set up validation parameters
    RPCParameterValidationEnabled = true;
    RPCRateValidationEnabled = true;
    RPCAuthorizationEnabled = true;

    RPCValidationInitialized = true;
}

void UnrealMCPNetworkingCommands::InitializeDDoSProtection()
{
    UE_LOG(LogTemp, Log, TEXT("Initializing DDoS protection"));

    DDoSProtectionActive = true;

    // Set up protection parameters
    ConnectionRateLimitingEnabled = true;
    PacketFloodProtectionEnabled = true;
    IPBlacklistingEnabled = true;

    // Initialize detection thresholds
    MaxConnectionsPerIP = 10;
    MaxPacketsPerSecondPerIP = 500;

    DDoSProtectionInitialized = true;
}

TSharedPtr<FJsonObject> UnrealMCPNetworkingCommands::CollectRealNetworkingMetrics(UWorld* World)
{
    TSharedPtr<FJsonObject> Metrics = MakeShareable(new FJsonObject);

    if (!World)
    {
        Metrics->SetBoolField(TEXT("success"), false);
        Metrics->SetStringField(TEXT("error"), TEXT("No valid world context"));
        return Metrics;
    }

    // System status metrics
    Metrics->SetBoolField(TEXT("network_driver_active"), NetworkDriverActive);
    Metrics->SetBoolField(TEXT("connection_management_active"), ConnectionManagementActive);
    Metrics->SetBoolField(TEXT("replication_system_active"), ReplicationSystemActive);
    Metrics->SetBoolField(TEXT("rpc_system_active"), RPCSystemActive);
    Metrics->SetBoolField(TEXT("network_security_active"), NetworkSecurityActive);

    // Network driver metrics
    UNetDriver* NetDriver = World->GetNetDriver();
    if (NetDriver)
    {
        Metrics->SetStringField(TEXT("network_role"), NetworkRole);
        Metrics->SetNumberField(TEXT("max_connections"), MaxConnections);
        Metrics->SetNumberField(TEXT("max_packet_size"), MaxPacketSize);

        if (NetworkRole == TEXT("Server"))
        {
            Metrics->SetNumberField(TEXT("server_tick_rate"), ServerTickRate);
            Metrics->SetNumberField(TEXT("max_client_rate"), MaxClientRate);

            // Count active connections
            int32 ActiveConnectionCount = 0;
            for (UNetConnection* Connection : NetDriver->ClientConnections)
            {
                if (Connection && Connection->GetConnectionState() == USOCK_Open)
                {
                    ActiveConnectionCount++;
                }
            }
            Metrics->SetNumberField(TEXT("active_connections"), ActiveConnectionCount);
        }
        else
        {
            Metrics->SetNumberField(TEXT("client_tick_rate"), ClientTickRate);

            // Check server connection
            bool bConnectedToServer = NetDriver->ServerConnection &&
                                    NetDriver->ServerConnection->GetConnectionState() == USOCK_Open;
            Metrics->SetBoolField(TEXT("connected_to_server"), bConnectedToServer);
        }
    }

    // Performance metrics
    Metrics->SetNumberField(TEXT("replication_rate"), ReplicationRate);
    Metrics->SetNumberField(TEXT("max_replication_distance"), MaxReplicationDistance);
    Metrics->SetNumberField(TEXT("max_rpcs_per_frame"), MaxRPCsPerFrame);
    Metrics->SetNumberField(TEXT("rpc_timeout"), RPCTimeout);

    // Quality metrics
    Metrics->SetNumberField(TEXT("max_acceptable_latency"), MaxAcceptableLatency);
    Metrics->SetNumberField(TEXT("max_acceptable_ping"), MaxAcceptablePing);
    Metrics->SetNumberField(TEXT("max_acceptable_packet_loss"), MaxAcceptablePacketLoss);
    Metrics->SetNumberField(TEXT("max_acceptable_jitter"), MaxAcceptableJitter);

    // Bandwidth metrics
    Metrics->SetNumberField(TEXT("max_upload_bandwidth"), MaxUploadBandwidth);
    Metrics->SetNumberField(TEXT("max_download_bandwidth"), MaxDownloadBandwidth);

    // Security metrics
    Metrics->SetBoolField(TEXT("encryption_enabled"), EncryptionEnabled);
    Metrics->SetNumberField(TEXT("encryption_key_size"), EncryptionKeySize);
    Metrics->SetBoolField(TEXT("packet_validation_enabled"), PacketValidationEnabled);
    Metrics->SetBoolField(TEXT("anti_spam_enabled"), AntiSpamEnabled);
    Metrics->SetNumberField(TEXT("max_packets_per_second"), MaxPacketsPerSecond);
    Metrics->SetNumberField(TEXT("max_bytes_per_second"), MaxBytesPerSecond);

    // Feature status
    Metrics->SetBoolField(TEXT("lag_compensation_enabled"), LagCompensationActive);
    Metrics->SetBoolField(TEXT("anti_cheat_networking_enabled"), AntiCheatNetworkingActive);
    Metrics->SetBoolField(TEXT("bandwidth_management_enabled"), BandwidthManagementActive);
    Metrics->SetBoolField(TEXT("connection_quality_monitoring_enabled"), ConnectionQualityMonitoringActive);

    // System health assessment
    bool bSystemHealthy = NetworkDriverActive && ConnectionManagementActive &&
                         ReplicationSystemActive && RPCSystemActive &&
                         NetworkSecurityActive;

    FString HealthStatus = bSystemHealthy ? TEXT("Healthy") : TEXT("Degraded");
    Metrics->SetStringField(TEXT("system_health"), HealthStatus);

    // Performance impact assessment
    float PerformanceImpact = CalculateNetworkingPerformanceImpact();
    Metrics->SetNumberField(TEXT("performance_impact_score"), PerformanceImpact);

    // Network load assessment
    float NetworkLoad = CalculateNetworkLoad();
    Metrics->SetNumberField(TEXT("network_load_percent"), NetworkLoad);

    Metrics->SetStringField(TEXT("world_name"), World->GetName());
    Metrics->SetStringField(TEXT("collection_timestamp"), FDateTime::Now().ToString());

    return Metrics;
}

float UnrealMCPNetworkingCommands::CalculateNetworkingPerformanceImpact()
{
    float Impact = 0.0f;

    // Add impact based on active systems
    if (NetworkDriverActive) Impact += 10.0f;
    if (ConnectionManagementActive) Impact += 8.0f;
    if (ReplicationSystemActive) Impact += 15.0f;
    if (RPCSystemActive) Impact += 12.0f;
    if (NetworkSecurityActive) Impact += 20.0f;

    // Add impact based on features
    if (LagCompensationActive) Impact += 10.0f;
    if (AntiCheatNetworkingActive) Impact += 15.0f;
    if (BandwidthManagementActive) Impact += 5.0f;
    if (ConnectionQualityMonitoringActive) Impact += 3.0f;

    // Adjust based on optimization settings
    if (DeltaCompressionActive) Impact *= 0.8f; // 20% reduction with compression
    if (RelevancySystemActive) Impact *= 0.7f; // 30% reduction with relevancy

    return Impact;
}

float UnrealMCPNetworkingCommands::CalculateNetworkLoad()
{
    float Load = 0.0f;

    // Base load from active connections
    Load += ActiveConnections.Num() * 5.0f; // 5% per connection

    // Add load from replication
    if (ReplicationSystemActive)
    {
        Load += ReplicationRate * 0.5f; // 0.5% per Hz
    }

    // Add load from RPCs
    if (RPCSystemActive)
    {
        Load += MaxRPCsPerFrame * 0.1f; // 0.1% per RPC
    }

    // Add load from security features
    if (NetworkSecurityActive)
    {
        Load += 10.0f; // Base security overhead
        if (EncryptionEnabled) Load += 15.0f; // Encryption overhead
        if (PacketValidationEnabled) Load += 5.0f; // Validation overhead
    }

    // Cap at 100%
    return FMath::Min(Load, 100.0f);
}
