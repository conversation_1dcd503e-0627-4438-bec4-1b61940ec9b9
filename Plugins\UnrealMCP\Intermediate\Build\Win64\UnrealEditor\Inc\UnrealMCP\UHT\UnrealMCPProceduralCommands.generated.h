// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

// IWYU pragma: private, include "Commands/UnrealMCPProceduralCommands.h"

#ifdef UNREALMCP_UnrealMCPProceduralCommands_generated_h
#error "UnrealMCPProceduralCommands.generated.h already included, missing '#pragma once' in UnrealMCPProceduralCommands.h"
#endif
#define UNREALMCP_UnrealMCPProceduralCommands_generated_h

#include "UObject/ObjectMacros.h"
#include "UObject/ScriptMacros.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

// ********** Begin ScriptStruct FBiomeData ********************************************************
#define FID_Game_Auracron_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPProceduralCommands_h_25_GENERATED_BODY \
	friend struct Z_Construct_UScriptStruct_FBiomeData_Statics; \
	UNREALMCP_API static class UScriptStruct* StaticStruct();


struct FBiomeData;
// ********** End ScriptStruct FBiomeData **********************************************************

#undef CURRENT_FILE_ID
#define CURRENT_FILE_ID FID_Game_Auracron_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPProceduralCommands_h

PRAGMA_ENABLE_DEPRECATION_WARNINGS
