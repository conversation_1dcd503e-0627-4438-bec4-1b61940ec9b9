{"Version": "1.2", "Data": {"Source": "c:\\game\\auracron\\plugins\\unrealmcp\\source\\unrealmcp\\private\\commands\\unrealmcpaudiosystemcommands.cpp", "ProvidedModule": "", "PCH": "c:\\game\\auracron\\intermediate\\build\\win64\\x64\\auracroneditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.valexpapi.cpp20.h.pch", "Includes": ["c:\\game\\auracron\\plugins\\unrealmcp\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\unrealmcp\\definitions.unrealmcp.h", "c:\\game\\auracron\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpaudiosystemcommands.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\soundcue.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\audioextensions\\public\\iaudioparametertransmitter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\soundnode.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\soundnode.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\soundcue.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\soundsubmix.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\signalprocessing\\public\\dsp\\spectrumanalyzer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\signalprocessing\\public\\dsp\\audiofft.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\signalprocessing\\public\\dsp\\fftalgorithm.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\signalprocessing\\public\\samplebuffer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\signalprocessing\\public\\dsp\\floatarraymath.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\audioextensions\\public\\iaudioendpoint.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\signalprocessing\\public\\dsp\\multithreadedpatching.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\audioextensions\\uht\\iaudioendpoint.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\audioextensions\\public\\isoundfieldendpoint.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\audioextensions\\uht\\isoundfieldendpoint.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\samplebufferio.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\soundeffectsubmix.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\soundeffectsubmix.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\soundsubmix.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\ambientsound.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\ambientsound.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\audiocomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\audio\\soundparametercontrollerinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\soundparametercontrollerinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\audiomixer\\public\\quartz\\audiomixerquantizedcommands.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\audiomixer\\public\\quartz\\audiomixerclock.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\audiomixer\\public\\quartz\\quartzmetronome.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\quartzsubscription.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\quartzsubscriptiontoken.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\quartzinterfaces.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\audiocomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\audiodevice.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\audiodynamicparameter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\audiovirtualloop.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\activesound.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\audio\\audiodebug.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\signalprocessing\\public\\dsp\\volumefader.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\soundsourcebus.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\soundwaveprocedural.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\soundwaveprocedural.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\audiobus.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\audiobus.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\soundsourcebus.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\audiomixercore\\public\\audiomixertrace.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\soundclass.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\soundclass.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\soundmix.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\soundmix.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\subsystems\\audioenginesubsystem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\audiodevicemanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\audiothread.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\audioenginesubsystem.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\audiomixer\\public\\audiomixerdevice.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\soundgenerator.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\audiomixer\\public\\quartz\\audiomixerclockmanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\audiomixer\\uht\\audiomixerdevice.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundengine\\public\\metasoundsource.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundengine\\public\\interfaces\\metasoundoutputformatinterfaces.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasoundfrontenddocument.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasoundaccessptr.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasoundfrontendliteral.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundgraphcore\\public\\metasoundliteral.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\intermediate\\build\\win64\\unrealeditor\\inc\\metasoundfrontend\\uht\\metasoundfrontendliteral.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundgraphcore\\public\\metasoundoperatordata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundgraphcore\\public\\metasoundnodeinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundgraphcore\\public\\metasounddatafactory.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundgraphcore\\public\\metasounddatareference.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundgraphcore\\public\\metasoundoperatorsettings.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundgraphcore\\public\\metasoundoperatorinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundgraphcore\\public\\metasounddatareferencecollection.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundgraphcore\\public\\metasoundvertex.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundgraphcore\\public\\metasoundenvironment.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundgraphcore\\public\\metasoundrendercost.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundgraphcore\\public\\metasoundvertexdata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\algo\\allof.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundgraphcore\\public\\metasoundlog.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\structutils\\instancedstruct.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\structutils\\structutils.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\coreuobject\\uht\\instancedstruct.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\structutils\\sharedstruct.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\structutils\\structutilstypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\coreuobject\\uht\\sharedstruct.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\noexporttypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\propertyaccessutil.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\serialization\\testundeclaredscriptstructobjectreferences.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\math\\unitconversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\math\\unitconversion.inl", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\internationalization\\polyglottextdata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\intermediate\\build\\win64\\unrealeditor\\inc\\metasoundfrontend\\uht\\metasoundfrontenddocument.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\intermediate\\build\\win64\\unrealeditor\\inc\\metasoundengine\\uht\\metasoundoutputformatinterfaces.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundengine\\public\\metasound.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasoundassetbase.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasoundassetmanager.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\jsonutilities\\public\\jsonobjectconverter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\jsonutilities\\public\\jsonobjectwrapper.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\jsonutilities\\uht\\jsonobjectwrapper.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasoundassetkey.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasoundfrontendregistrykey.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\intermediate\\build\\win64\\unrealeditor\\inc\\metasoundfrontend\\uht\\metasoundassetkey.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasounddocumentinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundgraphcore\\public\\metasoundbuilderinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasoundfrontenddocumentmodifydelegates.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\intermediate\\build\\win64\\unrealeditor\\inc\\metasoundfrontend\\uht\\metasounddocumentinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasoundfrontendquery.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\interfaces\\metasoundfrontendinterfaceregistry.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasoundfrontendcontroller.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasoundfrontend.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundgraphcore\\public\\metasoundgraph.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundgraphcore\\public\\metasoundnode.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasoundfrontenddatatyperegistry.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasoundenum.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasoundfrontendregistries.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasoundfrontendregistrycontainer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasoundfrontendnodeclassregistry.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasoundnodeconstructorparams.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasoundfrontenddocumentaccessptr.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasoundfrontendnodetemplateregistry.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\intermediate\\build\\win64\\unrealeditor\\inc\\metasoundfrontend\\uht\\metasoundfrontendnodetemplateregistry.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\structutils\\structview.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\intermediate\\build\\win64\\unrealeditor\\inc\\metasoundfrontend\\uht\\metasoundfrontendquery.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\assetregistrytagscontext.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\cookenums.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\intermediate\\build\\win64\\unrealeditor\\inc\\metasoundfrontend\\uht\\metasoundassetmanager.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasoundparametertransmitter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasoundprimitives.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundgraphcore\\public\\metasounddatareferencemacro.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundgraphcore\\public\\metasoundvariable.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasoundrouter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundgraphcore\\public\\metasoundaudiobuffer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundgraphcore\\public\\metasoundgraphcoremodule.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundgraphcore\\public\\metasoundexecutableoperator.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasoundtrigger.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasoundtime.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasoundfrontendtransform.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundengine\\public\\metasounduobjectregistry.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\objectsavecontext.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\objectsaveoverride.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\cooker\\cookdependency.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\intermediate\\build\\win64\\unrealeditor\\inc\\metasoundengine\\uht\\metasound.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\intermediate\\build\\win64\\unrealeditor\\inc\\metasoundengine\\uht\\metasoundsource.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundgenerator\\public\\metasoundgenerator.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundgraphcore\\public\\metasoundgraphoperator.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundgenerator\\public\\metasoundinstancecounter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\profilingdebugging\\counterstrace.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundgraphcore\\public\\metasoundoperatorbuilder.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundgraphcore\\public\\metasoundoperatorbuildersettings.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasoundparameterpack.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasounddatatyperegistrationmacro.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasoundarraynodesregistration.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasoundarraynodes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\interfaces\\metasoundfrontendsourceinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundgraphcore\\public\\metasoundfacade.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundgraphcore\\public\\metasoundbasicnode.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasoundnoderegistrationmacro.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\traits\\metasoundnodeconstructortraits.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\traits\\metasoundnodestaticmembertraits.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundgraphcore\\public\\metasoundparamhelper.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasoundarrayshufflenode.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasoundarrayrandomnode.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\circularqueue.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasoundautoconverternode.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundgraphcore\\public\\metasoundbuilderror.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasoundfrontendnodescategories.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasoundconverternoderegistrationmacro.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasoundinputnode.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasoundfrontenddatatypetraits.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasoundoutputnode.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasoundliteralnode.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasoundreceivenode.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasoundsendnode.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasoundtransmissionregistration.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundgraphcore\\public\\metasoundvariablenodes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasoundparameterpackfixedarray.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\intermediate\\build\\win64\\unrealeditor\\inc\\metasoundfrontend\\uht\\metasoundparameterpack.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\analysis\\metasoundfrontendgraphanalyzer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\analysis\\metasoundfrontendvertexanalyzer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\analysis\\metasoundfrontendanalyzeraddress.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\metasound\\source\\metasoundfrontend\\public\\metasoundoutputstorage.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\gameplaystatics.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\kismetsystemlibrary.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\kismetsystemlibrary.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\dialoguetypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\dialoguetypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\gameplaystaticstypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gameplaystaticstypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gameplaystatics.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\audiomixer\\public\\audiomixerblueprintlibrary.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\audiomixer\\classes\\submixeffects\\audiomixersubmixeffectdynamicsprocessor.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\signalprocessing\\public\\dsp\\dynamicsprocessor.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\signalprocessing\\public\\dsp\\envelopefollower.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\signalprocessing\\public\\dsp\\integerdelay.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\signalprocessing\\public\\dsp\\alignedblockbuffer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\signalprocessing\\public\\dsp\\filter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\signalprocessing\\public\\dsp\\modulationmatrix.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\audiomixer\\uht\\audiomixersubmixeffectdynamicsprocessor.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\audiomixer\\uht\\audiomixerblueprintlibrary.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\audioanalytics.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\engineanalytics.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\analytics\\analyticset\\public\\ianalyticsprovideret.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\analytics\\analyticset\\public\\analyticset.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\analytics\\analytics\\public\\analyticsproviderconfigurationdelegate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\analytics\\analytics\\public\\interfaces\\ianalyticsprovidermodule.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\analytics\\analytics\\public\\analyticseventattribute.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\analytics\\analytics\\public\\analyticsconversion.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\analytics\\analytics\\public\\interfaces\\ianalyticsprovider.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\assetregistry\\public\\assetregistry\\assetregistrymodule.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\assetregistry\\public\\assetregistry\\iassetregistry.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\misc\\assetregistryinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\assetregistry\\uht\\iassetregistry.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\savepackage.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\serialization\\packagewriter.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\io\\iodispatcher.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\io\\iobuffer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\io\\iocontainerid.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\ienginecrypto.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\soundnoderandom.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\soundnoderandom.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\soundnodewaveplayer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\soundnodeassetreferencer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\soundnodeassetreferencer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\soundnodewaveplayer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\soundnodemixer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\soundnodemixer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\soundnodelooping.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\soundnodelooping.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\engineutils.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}