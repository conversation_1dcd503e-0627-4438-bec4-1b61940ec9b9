// Copyright Epic Games, Inc. All Rights Reserved.

using UnrealBuildTool;

public class UnrealMCP : ModuleRules
{
	public UnrealMCP(ReadOnlyTargetRules Target) : base(Target)
	{
		PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;
		// Use IWYUSupport instead of the deprecated bEnforceIWYU in UE5.5
		IWYUSupport = IWYUSupport.Full;

		PublicIncludePaths.AddRange(
			new string[] {
				// ... add public include paths required here ...
			}
		);
		
		PrivateIncludePaths.AddRange(
			new string[] {
				// ... add other private include paths required here ...
			}
		);
		
		PublicDependencyModuleNames.AddRange(
			new string[]
			{
				"Core",
				"CoreUObject",
				"Engine",
				"InputCore",
				"Networking",
				"Sockets",
				"HTTP",
				"Json",
				"JsonUtilities",
				"DeveloperSettings",
				"Niagara",
				"NiagaraEditor",
				"Landscape",
				"Foliage",
				"NavigationSystem",
				"AIModule",
				"GameplayTasks",
				"MassEntity",
				"MassMovement",
				"MassCommon",
				"MassRepresentation",
				"MassLOD",
				"MassSimulation",
				"MassSpawner",
				"StateTreeModule",
				"GameplayAbilities",
				"GameplayTags",
				"DataRegistry",
				"PCG",
				"UnrealEd",
				"BlueprintGraph",
				"KismetCompiler",
				"ToolMenus",
				"Slate",
				"SlateCore",
				"DesktopPlatform",
				"ClothingSystemRuntimeCommon",
				"Chaos",
				"ChaosCore",
				"ChaosSolverEngine",
				"PhysicsCore",
				"GeometryCollectionEditor",
				"MetasoundEngine",
				"MetasoundFrontend",
				"MetasoundGenerator",
				"ReplicationGraph",
				"IrisCore",
				"OnlineSubsystem",
				"OnlineSubsystemUtils",
				"GeometryCollectionEngine",
				"FieldSystemEngine",
				"RHI",
				"RHICore",
				"RenderCore",
				"Renderer",
				"Niagara",
				"NiagaraCore",
				"NiagaraShader",
				"BlueprintGraph",
				"KismetCompiler",
				"Kismet",
				"KismetWidgets",
				"UnrealEd",
				"MaterialEditor",
				"NetCore"
			}
		);
		
		PrivateDependencyModuleNames.AddRange(
			new string[]
			{
				"UnrealEd",
				"EditorScriptingUtilities",
				"EditorSubsystem",
				"Slate",
				"SlateCore",
				"UMG",
				"Kismet",
				"KismetCompiler",
				"BlueprintGraph",
				"Projects",
				"AssetRegistry"
			}
		);
		
		if (Target.bBuildEditor == true)
		{
			PrivateDependencyModuleNames.AddRange(
				new string[]
				{
					"PropertyEditor",      // For widget property editing
					"ToolMenus",           // For editor UI
					"BlueprintEditorLibrary", // For Blueprint utilities
					"UMGEditor"           // For WidgetBlueprint.h and other UMG editor functionality
				}
			);
		}
		
		DynamicallyLoadedModuleNames.AddRange(
			new string[]
			{
				// ... add any modules that your module loads dynamically here ...
			}
		);
	}
}