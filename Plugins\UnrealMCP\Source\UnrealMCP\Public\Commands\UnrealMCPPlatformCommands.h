// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "HAL/Platform.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/ConfigCacheIni.h"
#include "Misc/Paths.h"
#include "GameFramework/GameUserSettings.h"
#include "RHI.h"
#include "RHIDefinitions.h"
#include "DeviceProfiles/DeviceProfile.h"
#include "DeviceProfiles/DeviceProfileManager.h"
#include "Engine/RendererSettings.h"
#include "Engine/StreamableManager.h"
#include "OnlineSubsystem.h"
#include "Kismet/GameplayStatics.h"
#include "Stats/Stats.h"
#include "ProfilingDebugging/CpuProfilerTrace.h"

/**
 * UnrealMCPPlatformCommands
 * 
 * Handles all Platform-Specific related commands for the MCP server.
 * Provides comprehensive platform configuration and optimization tools
 * compatible with Unreal Engine 5.6 platform systems.
 * 
 * Features:
 * - Platform-specific settings configuration
 * - Mobile platform setup and optimization
 * - Console platform configuration
 * - Cross-platform features management
 * - Performance optimization per platform
 * - Platform-specific input configuration
 * - Packaging and deployment setup
 * - Store integration management
 * - Platform compatibility analysis
 * - Multi-platform support
 */
class UNREALMCP_API UnrealMCPPlatformCommands
{
public:
    UnrealMCPPlatformCommands();
    ~UnrealMCPPlatformCommands();

    // Core Platform Functions
    static FString HandleConfigurePlatformSettings(const FString& JsonParams);
    static FString HandleSetupMobilePlatform(const FString& JsonParams);
    static FString HandleConfigureConsolePlatform(const FString& JsonParams);
    static FString HandleSetupCrossPlatformFeatures(const FString& JsonParams);

    // Performance and Optimization
    static FString HandleOptimizePlatformPerformance(const FString& JsonParams);
    static FString HandleConfigurePlatformInput(const FString& JsonParams);

    // Packaging and Deployment
    static FString HandleSetupPlatformPackaging(const FString& JsonParams);
    static FString HandleManagePlatformStoreIntegration(const FString& JsonParams);
    static FString HandleAnalyzePlatformCompatibility(const FString& JsonParams);

private:
    // Helper Functions for Platform Configuration
    static bool ConfigurePlatformQualitySettings(const FString& PlatformName, const FString& QualityLevel,
        const FString& MaterialQuality, const FString& TextureQuality, const FString& ShadowQuality);
    static bool EnableVulkanRenderer(const FString& PlatformName, bool bEnable);
    static bool SetPlatformDeviceProfile(const FString& PlatformName, const FString& ProfileName);
    static bool ApplyPlatformSpecificSettings(const FString& PlatformName, const TMap<FString, FString>& Settings);
    static UDeviceProfile* GetOrCreateDeviceProfile(const FString& PlatformName);
    
    // Helper Functions for Mobile Platform Setup
    static bool ConfigureMobileSDK(const FString& Platform, const FString& SDKPath, 
        int32 MinSDKVersion, int32 TargetSDKVersion);
    static bool SetupMobileArchitectures(const FString& Platform, bool bEnableARM64, bool bEnableX86_64);
    static bool ConfigureMobileRendering(const FString& Platform, bool bEnableVulkan, bool bEnableMetal);
    static bool SetupMobileInput(const FString& Platform, bool bEnableTouch, bool bEnableGamepad);
    static bool OptimizeMobilePerformance(const FString& Platform, int32 TargetFPS, int32 MemoryBudget);
    
    // Helper Functions for Console Platform Configuration
    static bool SetupConsoleDevelopmentKit(const FString& ConsoleType, const FString& DevKitPath);
    static bool ConfigureConsolePerformanceProfile(const FString& ConsoleType, const FString& Profile);
    static bool EnableConsoleRayTracing(const FString& ConsoleType, bool bEnable);
    static bool SetupConsoleNetworking(const FString& ConsoleType, bool bEnableOnlineFeatures);
    static bool ConfigureConsoleMemorySettings(const FString& ConsoleType, int32 MemoryBudgetMB);
    
    // Helper Functions for Cross-Platform Features
    static bool EnableCrossPlatformPlay(bool bEnable, const FString& OnlineSubsystem);
    static bool EnableCrossPlatformProgression(bool bEnable, const FString& AuthMethod);
    static bool ConfigureOnlineSubsystem(const FString& SubsystemName, const TMap<FString, FString>& Settings);
    static bool SetupPlatformServices(const TArray<FString>& Services);
    static bool ConfigureAuthentication(const FString& Method, const TMap<FString, FString>& Settings);
    
    // Helper Functions for Performance Optimization
    static bool SetTargetFrameRate(const FString& Platform, int32 TargetFPS);
    static bool ConfigureMemoryBudget(const FString& Platform, int32 MemoryBudgetMB);
    static bool SetupLODSystem(const FString& Platform, bool bEnable, float CullingDistance);
    static bool ConfigureTextureStreaming(const FString& Platform, bool bEnable, int32 PoolSizeMB);
    static bool OptimizeRenderingSettings(const FString& Platform, const TMap<FString, float>& Settings);
    
    // Helper Functions for Input Configuration
    static bool ConfigureInputDevices(const FString& Platform, const TArray<FString>& InputDevices);
    static bool SetupTouchInput(const FString& Platform, bool bEnable);
    static bool SetupGamepadInput(const FString& Platform, bool bEnable);
    static bool SetupKeyboardMouseInput(const FString& Platform, bool bEnable);
    static bool ApplyCustomInputMappings(const FString& Platform, const TMap<FString, FString>& Mappings);
    
    // Helper Functions for Packaging
    static bool ConfigurePackagingSettings(const FString& Platform, const FString& BuildConfig, 
        const FString& Architecture, const FString& CompressionMethod);
    static bool SetupPlatformPrerequisites(const FString& Platform, bool bInclude);
    static bool CreatePlatformInstaller(const FString& Platform, bool bCreate);
    static bool ValidatePackagingConfiguration(const FString& Platform);
    
    // Helper Functions for Store Integration
    static bool ConfigureStoreIntegration(const FString& Platform, const FString& StoreType, const FString& AppID);
    static bool SetupAchievements(const FString& Platform, bool bEnable);
    static bool SetupLeaderboards(const FString& Platform, bool bEnable);
    static bool SetupCloudSaves(const FString& Platform, bool bEnable);
    static bool ValidateStoreConfiguration(const FString& Platform, const FString& StoreType);
    
    // Helper Functions for Compatibility Analysis
    static TSharedPtr<FJsonObject> AnalyzeAssetCompatibility(const FString& ProjectPath, 
        const TArray<FString>& TargetPlatforms);
    static TSharedPtr<FJsonObject> AnalyzeCodeCompatibility(const FString& ProjectPath, 
        const TArray<FString>& TargetPlatforms);
    static TSharedPtr<FJsonObject> GenerateCompatibilityReport(const TSharedPtr<FJsonObject>& AssetAnalysis, 
        const TSharedPtr<FJsonObject>& CodeAnalysis);
    static bool SaveCompatibilityReport(const TSharedPtr<FJsonObject>& Report, const FString& OutputPath);
    
    // Utility Functions
    static FString GetCurrentPlatformName();
    static TArray<FString> GetSupportedPlatforms();
    static bool IsPlatformSupported(const FString& PlatformName);
    static bool IsMobilePlatform(const FString& PlatformName);
    static bool IsConsolePlatform(const FString& PlatformName);
    static bool IsDesktopPlatform(const FString& PlatformName);
    static FString GetPlatformSDKPath(const FString& PlatformName);
    static FString GetPlatformConfigPath(const FString& PlatformName);
    
    // JSON Parsing Helpers
    static TSharedPtr<FJsonObject> ParseJsonParams(const FString& JsonParams);
    static FString CreateJsonResponse(bool bSuccess, const FString& Message, 
        const TMap<FString, FString>& AdditionalData = TMap<FString, FString>());
    static FString CreateJsonResponse(bool bSuccess, const FString& Message, 
        const TSharedPtr<FJsonObject>& DataObject);
    
    // Validation Functions
    static bool ValidatePlatformName(const FString& PlatformName);
    static bool ValidateQualityLevel(const FString& QualityLevel);
    static bool ValidateBuildConfiguration(const FString& BuildConfig);
    static bool ValidateArchitecture(const FString& Architecture);
    static bool ValidateCompressionMethod(const FString& CompressionMethod);
    static bool ValidateOnlineSubsystem(const FString& SubsystemName);
    static bool ValidateStoreType(const FString& StoreType);
    
    // Constants for Platform Configuration
    static const TArray<FString> SupportedPlatforms;
    static const TArray<FString> SupportedQualityLevels;
    static const TArray<FString> SupportedBuildConfigurations;
    static const TArray<FString> SupportedArchitectures;
    static const TArray<FString> SupportedCompressionMethods;
    static const TArray<FString> SupportedOnlineSubsystems;
    static const TArray<FString> SupportedStoreTypes;
    static const TArray<FString> MobilePlatforms;
    static const TArray<FString> ConsolePlatforms;
    static const TArray<FString> DesktopPlatforms;
    
    // Platform Constants
    static const int32 DefaultTargetFPS;
    static const int32 DefaultMemoryBudgetMB;
    static const float DefaultCullingDistance;
    static const int32 DefaultTexturePoolSizeMB;
    static const int32 DefaultMinSDKVersion;
    static const int32 DefaultTargetSDKVersion;
};
