#include "Commands/UnrealMCPCloudServicesCommands.h"
#include "Engine/Engine.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"
#include "Misc/Paths.h"
#include "Misc/ConfigCacheIni.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"
#include "OnlineSubsystem.h"
#include "OnlineSubsystemUtils.h"
#include "Interfaces/OnlineIdentityInterface.h"
#include "HAL/PlatformProcess.h"
#include "Misc/Base64.h"

// === Constants ===

const FString UnrealMCPCloudServicesCommands::AWS_CONFIG_SECTION = TEXT("AWS");
const FString UnrealMCPCloudServicesCommands::AWS_DEFAULT_REGION = TEXT("us-east-1");

const FString UnrealMCPCloudServicesCommands::AZURE_CONFIG_SECTION = TEXT("Azure");
const FString UnrealMCPCloudServicesCommands::AZURE_DEFAULT_REGION = TEXT("eastus");

const FString UnrealMCPCloudServicesCommands::GCP_CONFIG_SECTION = TEXT("GoogleCloudPlatform");
const FString UnrealMCPCloudServicesCommands::GCP_DEFAULT_REGION = TEXT("us-central1");

const FString UnrealMCPCloudServicesCommands::FIREBASE_CONFIG_SECTION = TEXT("Firebase");
const FString UnrealMCPCloudServicesCommands::FIREBASE_SDK_VERSION = TEXT("10.7.0");

const FString UnrealMCPCloudServicesCommands::PLAYFAB_CONFIG_SECTION = TEXT("PlayFab");
const FString UnrealMCPCloudServicesCommands::PLAYFAB_SDK_VERSION = TEXT("1.164.240405");

const FString UnrealMCPCloudServicesCommands::EOS_CONFIG_SECTION = TEXT("EpicOnlineServices");
const FString UnrealMCPCloudServicesCommands::EOS_SDK_VERSION = TEXT("1.16.3");

const FString UnrealMCPCloudServicesCommands::STEAM_CONFIG_SECTION = TEXT("Steam");
const FString UnrealMCPCloudServicesCommands::STEAMWORKS_SDK_VERSION = TEXT("1.58a");

const FString UnrealMCPCloudServicesCommands::DEDICATED_SERVER_CONFIG_SECTION = TEXT("DedicatedServers");
const FString UnrealMCPCloudServicesCommands::MATCHMAKING_CONFIG_SECTION = TEXT("Matchmaking");
const FString UnrealMCPCloudServicesCommands::LEADERBOARDS_CONFIG_SECTION = TEXT("Leaderboards");
const FString UnrealMCPCloudServicesCommands::CLOUD_STORAGE_CONFIG_SECTION = TEXT("CloudStorage");
const FString UnrealMCPCloudServicesCommands::CDN_CONFIG_SECTION = TEXT("CDN");

// Server types
const FString UnrealMCPCloudServicesCommands::SERVER_TYPE_AWS_EC2 = TEXT("aws_ec2");
const FString UnrealMCPCloudServicesCommands::SERVER_TYPE_AZURE_VM = TEXT("azure_vm");
const FString UnrealMCPCloudServicesCommands::SERVER_TYPE_GCP_COMPUTE = TEXT("gcp_compute");
const FString UnrealMCPCloudServicesCommands::SERVER_TYPE_CUSTOM = TEXT("custom");

// Matchmaking providers
const FString UnrealMCPCloudServicesCommands::MATCHMAKING_PROVIDER_EOS = TEXT("eos");
const FString UnrealMCPCloudServicesCommands::MATCHMAKING_PROVIDER_STEAM = TEXT("steam");
const FString UnrealMCPCloudServicesCommands::MATCHMAKING_PROVIDER_PLAYFAB = TEXT("playfab");
const FString UnrealMCPCloudServicesCommands::MATCHMAKING_PROVIDER_CUSTOM = TEXT("custom");

// Storage providers
const FString UnrealMCPCloudServicesCommands::STORAGE_PROVIDER_AWS_S3 = TEXT("aws_s3");
const FString UnrealMCPCloudServicesCommands::STORAGE_PROVIDER_AZURE_BLOB = TEXT("azure_blob");
const FString UnrealMCPCloudServicesCommands::STORAGE_PROVIDER_GCP_STORAGE = TEXT("gcp_storage");
const FString UnrealMCPCloudServicesCommands::STORAGE_PROVIDER_FIREBASE_STORAGE = TEXT("firebase_storage");

// === AWS Integration ===

FString UnrealMCPCloudServicesCommands::HandleSetupAWSIntegration(const FString& JsonString)
{
    UE_LOG(LogTemp, Log, TEXT("UnrealMCPCloudServicesCommands: Setting up AWS integration"));

    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return CreateJsonResponse(false, TEXT("Invalid JSON parameters"));
    }

    if (!ValidateAWSConfig(JsonObject))
    {
        return CreateJsonResponse(false, TEXT("Invalid AWS configuration parameters"));
    }

    FString AccessKeyId = JsonObject->GetStringField(TEXT("access_key_id"));
    FString SecretAccessKey = JsonObject->GetStringField(TEXT("secret_access_key"));
    FString Region = JsonObject->GetStringField(TEXT("region"));
    bool bEnableS3 = JsonObject->GetBoolField(TEXT("enable_s3"));
    bool bEnableDynamoDB = JsonObject->GetBoolField(TEXT("enable_dynamodb"));
    bool bEnableLambda = JsonObject->GetBoolField(TEXT("enable_lambda"));

    // Get services array
    TArray<FString> Services;
    const TArray<TSharedPtr<FJsonValue>>* ServicesArray;
    if (JsonObject->TryGetArrayField(TEXT("services"), ServicesArray))
    {
        for (const auto& Value : *ServicesArray)
        {
            Services.Add(Value->AsString());
        }
    }

    bool bSuccess = true;
    FString ErrorMessage;

    // Initialize AWS SDK
    if (!InitializeAWS(AccessKeyId, SecretAccessKey, Region))
    {
        bSuccess = false;
        ErrorMessage += TEXT("Failed to initialize AWS SDK. ");
    }

    // Configure AWS services
    if (bSuccess)
    {
        if (!ConfigureAWSServices(Services, bEnableS3, bEnableDynamoDB, bEnableLambda))
        {
            ErrorMessage += TEXT("Failed to configure AWS services. ");
        }
    }

    // Save AWS configuration
    GConfig->SetString(*AWS_CONFIG_SECTION, TEXT("AccessKeyId"), *AccessKeyId, GEngineIni);
    GConfig->SetString(*AWS_CONFIG_SECTION, TEXT("SecretAccessKey"), *SecretAccessKey, GEngineIni);
    GConfig->SetString(*AWS_CONFIG_SECTION, TEXT("Region"), *Region, GEngineIni);
    GConfig->SetBool(*AWS_CONFIG_SECTION, TEXT("S3Enabled"), bEnableS3, GEngineIni);
    GConfig->SetBool(*AWS_CONFIG_SECTION, TEXT("DynamoDBEnabled"), bEnableDynamoDB, GEngineIni);
    GConfig->SetBool(*AWS_CONFIG_SECTION, TEXT("LambdaEnabled"), bEnableLambda, GEngineIni);
    GConfig->SetBool(*AWS_CONFIG_SECTION, TEXT("Enabled"), true, GEngineIni);
    GConfig->Flush(false, GEngineIni);

    LogCloudServicesOperation(TEXT("AWS Integration"), bSuccess, bSuccess ? TEXT("AWS integration setup successfully") : ErrorMessage);

    return CreateJsonResponse(bSuccess, bSuccess ? TEXT("AWS integration setup successfully") : ErrorMessage);
}

// === Azure Services ===

FString UnrealMCPCloudServicesCommands::HandleConfigureAzureServices(const FString& JsonString)
{
    UE_LOG(LogTemp, Log, TEXT("UnrealMCPCloudServicesCommands: Configuring Azure services"));

    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return CreateJsonResponse(false, TEXT("Invalid JSON parameters"));
    }

    if (!ValidateAzureConfig(JsonObject))
    {
        return CreateJsonResponse(false, TEXT("Invalid Azure configuration parameters"));
    }

    FString SubscriptionId = JsonObject->GetStringField(TEXT("subscription_id"));
    FString TenantId = JsonObject->GetStringField(TEXT("tenant_id"));
    FString ClientId = JsonObject->GetStringField(TEXT("client_id"));
    FString ClientSecret = JsonObject->GetStringField(TEXT("client_secret"));
    FString ResourceGroup = JsonObject->GetStringField(TEXT("resource_group"));
    bool bEnableStorage = JsonObject->GetBoolField(TEXT("enable_storage"));
    bool bEnableCosmosDB = JsonObject->GetBoolField(TEXT("enable_cosmos_db"));
    bool bEnableFunctions = JsonObject->GetBoolField(TEXT("enable_functions"));

    bool bSuccess = true;
    FString ErrorMessage;

    // Setup Azure authentication
    if (!SetupAzureAuth(SubscriptionId, TenantId, ClientId, ClientSecret))
    {
        bSuccess = false;
        ErrorMessage += TEXT("Failed to setup Azure authentication. ");
    }

    // Save Azure configuration
    GConfig->SetString(*AZURE_CONFIG_SECTION, TEXT("SubscriptionId"), *SubscriptionId, GEngineIni);
    GConfig->SetString(*AZURE_CONFIG_SECTION, TEXT("TenantId"), *TenantId, GEngineIni);
    GConfig->SetString(*AZURE_CONFIG_SECTION, TEXT("ClientId"), *ClientId, GEngineIni);
    GConfig->SetString(*AZURE_CONFIG_SECTION, TEXT("ClientSecret"), *ClientSecret, GEngineIni);
    GConfig->SetString(*AZURE_CONFIG_SECTION, TEXT("ResourceGroup"), *ResourceGroup, GEngineIni);
    GConfig->SetBool(*AZURE_CONFIG_SECTION, TEXT("StorageEnabled"), bEnableStorage, GEngineIni);
    GConfig->SetBool(*AZURE_CONFIG_SECTION, TEXT("CosmosDBEnabled"), bEnableCosmosDB, GEngineIni);
    GConfig->SetBool(*AZURE_CONFIG_SECTION, TEXT("FunctionsEnabled"), bEnableFunctions, GEngineIni);
    GConfig->SetBool(*AZURE_CONFIG_SECTION, TEXT("Enabled"), true, GEngineIni);
    GConfig->Flush(false, GEngineIni);

    LogCloudServicesOperation(TEXT("Azure Services"), bSuccess, bSuccess ? TEXT("Azure services configured successfully") : ErrorMessage);

    return CreateJsonResponse(bSuccess, bSuccess ? TEXT("Azure services configured successfully") : ErrorMessage);
}

// === Google Cloud Platform ===

FString UnrealMCPCloudServicesCommands::HandleSetupGoogleCloudPlatform(const FString& JsonString)
{
    UE_LOG(LogTemp, Log, TEXT("UnrealMCPCloudServicesCommands: Setting up Google Cloud Platform"));

    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return CreateJsonResponse(false, TEXT("Invalid JSON parameters"));
    }

    if (!ValidateGCPConfig(JsonObject))
    {
        return CreateJsonResponse(false, TEXT("Invalid GCP configuration parameters"));
    }

    FString ProjectId = JsonObject->GetStringField(TEXT("project_id"));
    FString ServiceAccountKey = JsonObject->GetStringField(TEXT("service_account_key"));
    FString Region = JsonObject->GetStringField(TEXT("region"));
    bool bEnableCloudStorage = JsonObject->GetBoolField(TEXT("enable_cloud_storage"));
    bool bEnableFirestore = JsonObject->GetBoolField(TEXT("enable_firestore"));
    bool bEnableCloudFunctions = JsonObject->GetBoolField(TEXT("enable_cloud_functions"));
    bool bEnablePubSub = JsonObject->GetBoolField(TEXT("enable_pub_sub"));

    bool bSuccess = true;
    FString ErrorMessage;

    // Configure GCP authentication
    if (!ConfigureGCPAuth(ProjectId, ServiceAccountKey, Region))
    {
        bSuccess = false;
        ErrorMessage += TEXT("Failed to configure GCP authentication. ");
    }

    // Save GCP configuration
    GConfig->SetString(*GCP_CONFIG_SECTION, TEXT("ProjectId"), *ProjectId, GEngineIni);
    GConfig->SetString(*GCP_CONFIG_SECTION, TEXT("ServiceAccountKey"), *ServiceAccountKey, GEngineIni);
    GConfig->SetString(*GCP_CONFIG_SECTION, TEXT("Region"), *Region, GEngineIni);
    GConfig->SetBool(*GCP_CONFIG_SECTION, TEXT("CloudStorageEnabled"), bEnableCloudStorage, GEngineIni);
    GConfig->SetBool(*GCP_CONFIG_SECTION, TEXT("FirestoreEnabled"), bEnableFirestore, GEngineIni);
    GConfig->SetBool(*GCP_CONFIG_SECTION, TEXT("CloudFunctionsEnabled"), bEnableCloudFunctions, GEngineIni);
    GConfig->SetBool(*GCP_CONFIG_SECTION, TEXT("PubSubEnabled"), bEnablePubSub, GEngineIni);
    GConfig->SetBool(*GCP_CONFIG_SECTION, TEXT("Enabled"), true, GEngineIni);
    GConfig->Flush(false, GEngineIni);

    LogCloudServicesOperation(TEXT("Google Cloud Platform"), bSuccess, bSuccess ? TEXT("Google Cloud Platform setup successfully") : ErrorMessage);

    return CreateJsonResponse(bSuccess, bSuccess ? TEXT("Google Cloud Platform setup successfully") : ErrorMessage);
}

// === Firebase Integration ===

FString UnrealMCPCloudServicesCommands::HandleConfigureFirebaseIntegration(const FString& JsonString)
{
    UE_LOG(LogTemp, Log, TEXT("UnrealMCPCloudServicesCommands: Configuring Firebase integration"));

    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return CreateJsonResponse(false, TEXT("Invalid JSON parameters"));
    }

    if (!ValidateFirebaseConfig(JsonObject))
    {
        return CreateJsonResponse(false, TEXT("Invalid Firebase configuration parameters"));
    }

    FString ProjectId = JsonObject->GetStringField(TEXT("project_id"));
    FString ApiKey = JsonObject->GetStringField(TEXT("api_key"));
    FString DatabaseUrl = JsonObject->GetStringField(TEXT("database_url"));
    FString StorageBucket = JsonObject->GetStringField(TEXT("storage_bucket"));
    bool bEnableAuth = JsonObject->GetBoolField(TEXT("enable_auth"));
    bool bEnableDatabase = JsonObject->GetBoolField(TEXT("enable_database"));
    bool bEnableStorage = JsonObject->GetBoolField(TEXT("enable_storage"));
    bool bEnableAnalytics = JsonObject->GetBoolField(TEXT("enable_analytics"));

    bool bSuccess = true;
    FString ErrorMessage;

    // Configure Firebase settings
    if (!ConfigureFirebaseSettings(ProjectId, ApiKey, DatabaseUrl, StorageBucket))
    {
        bSuccess = false;
        ErrorMessage += TEXT("Failed to configure Firebase settings. ");
    }

    // Save Firebase configuration
    GConfig->SetString(*FIREBASE_CONFIG_SECTION, TEXT("ProjectId"), *ProjectId, GEngineIni);
    GConfig->SetString(*FIREBASE_CONFIG_SECTION, TEXT("ApiKey"), *ApiKey, GEngineIni);
    GConfig->SetString(*FIREBASE_CONFIG_SECTION, TEXT("DatabaseUrl"), *DatabaseUrl, GEngineIni);
    GConfig->SetString(*FIREBASE_CONFIG_SECTION, TEXT("StorageBucket"), *StorageBucket, GEngineIni);
    GConfig->SetBool(*FIREBASE_CONFIG_SECTION, TEXT("AuthEnabled"), bEnableAuth, GEngineIni);
    GConfig->SetBool(*FIREBASE_CONFIG_SECTION, TEXT("DatabaseEnabled"), bEnableDatabase, GEngineIni);
    GConfig->SetBool(*FIREBASE_CONFIG_SECTION, TEXT("StorageEnabled"), bEnableStorage, GEngineIni);
    GConfig->SetBool(*FIREBASE_CONFIG_SECTION, TEXT("AnalyticsEnabled"), bEnableAnalytics, GEngineIni);
    GConfig->SetBool(*FIREBASE_CONFIG_SECTION, TEXT("Enabled"), true, GEngineIni);
    GConfig->Flush(false, GEngineIni);

    LogCloudServicesOperation(TEXT("Firebase Integration"), bSuccess, bSuccess ? TEXT("Firebase integration configured successfully") : ErrorMessage);

    return CreateJsonResponse(bSuccess, bSuccess ? TEXT("Firebase integration configured successfully") : ErrorMessage);
}

// === PlayFab Services ===

FString UnrealMCPCloudServicesCommands::HandleSetupPlayFabServices(const FString& JsonString)
{
    UE_LOG(LogTemp, Log, TEXT("UnrealMCPCloudServicesCommands: Setting up PlayFab services"));

    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return CreateJsonResponse(false, TEXT("Invalid JSON parameters"));
    }

    FString TitleId = JsonObject->GetStringField(TEXT("title_id"));
    FString SecretKey = JsonObject->GetStringField(TEXT("secret_key"));
    FString DeveloperKey = JsonObject->GetStringField(TEXT("developer_key"));
    bool bEnableMultiplayer = JsonObject->GetBoolField(TEXT("enable_multiplayer"));
    bool bEnableEconomy = JsonObject->GetBoolField(TEXT("enable_economy"));
    bool bEnableLeaderboards = JsonObject->GetBoolField(TEXT("enable_leaderboards"));
    bool bEnableCloudScript = JsonObject->GetBoolField(TEXT("enable_cloud_script"));

    bool bSuccess = true;
    FString ErrorMessage;

    // Setup PlayFab configuration
    if (!SetupPlayFabConfig(TitleId, SecretKey, DeveloperKey))
    {
        bSuccess = false;
        ErrorMessage += TEXT("Failed to setup PlayFab configuration. ");
    }

    // Save PlayFab configuration
    GConfig->SetString(*PLAYFAB_CONFIG_SECTION, TEXT("TitleId"), *TitleId, GEngineIni);
    GConfig->SetString(*PLAYFAB_CONFIG_SECTION, TEXT("SecretKey"), *SecretKey, GEngineIni);
    GConfig->SetString(*PLAYFAB_CONFIG_SECTION, TEXT("DeveloperKey"), *DeveloperKey, GEngineIni);
    GConfig->SetBool(*PLAYFAB_CONFIG_SECTION, TEXT("MultiplayerEnabled"), bEnableMultiplayer, GEngineIni);
    GConfig->SetBool(*PLAYFAB_CONFIG_SECTION, TEXT("EconomyEnabled"), bEnableEconomy, GEngineIni);
    GConfig->SetBool(*PLAYFAB_CONFIG_SECTION, TEXT("LeaderboardsEnabled"), bEnableLeaderboards, GEngineIni);
    GConfig->SetBool(*PLAYFAB_CONFIG_SECTION, TEXT("CloudScriptEnabled"), bEnableCloudScript, GEngineIni);
    GConfig->SetBool(*PLAYFAB_CONFIG_SECTION, TEXT("Enabled"), true, GEngineIni);
    GConfig->Flush(false, GEngineIni);

    LogCloudServicesOperation(TEXT("PlayFab Services"), bSuccess, bSuccess ? TEXT("PlayFab services setup successfully") : ErrorMessage);

    return CreateJsonResponse(bSuccess, bSuccess ? TEXT("PlayFab services setup successfully") : ErrorMessage);
}

// === Epic Online Services ===

FString UnrealMCPCloudServicesCommands::HandleConfigureEpicOnlineServices(const FString& JsonString)
{
    UE_LOG(LogTemp, Log, TEXT("UnrealMCPCloudServicesCommands: Configuring Epic Online Services"));

    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return CreateJsonResponse(false, TEXT("Invalid JSON parameters"));
    }

    FString ProductId = JsonObject->GetStringField(TEXT("product_id"));
    FString SandboxId = JsonObject->GetStringField(TEXT("sandbox_id"));
    FString DeploymentId = JsonObject->GetStringField(TEXT("deployment_id"));
    FString ClientId = JsonObject->GetStringField(TEXT("client_id"));
    FString ClientSecret = JsonObject->GetStringField(TEXT("client_secret"));
    bool bEnableAuth = JsonObject->GetBoolField(TEXT("enable_auth"));
    bool bEnableFriends = JsonObject->GetBoolField(TEXT("enable_friends"));
    bool bEnableAchievements = JsonObject->GetBoolField(TEXT("enable_achievements"));
    bool bEnableStats = JsonObject->GetBoolField(TEXT("enable_stats"));

    bool bSuccess = true;
    FString ErrorMessage;

    // Configure EOS settings
    if (!ConfigureEOSSettings(ProductId, SandboxId, DeploymentId, ClientId, ClientSecret))
    {
        bSuccess = false;
        ErrorMessage += TEXT("Failed to configure EOS settings. ");
    }

    // Save EOS configuration
    GConfig->SetString(*EOS_CONFIG_SECTION, TEXT("ProductId"), *ProductId, GEngineIni);
    GConfig->SetString(*EOS_CONFIG_SECTION, TEXT("SandboxId"), *SandboxId, GEngineIni);
    GConfig->SetString(*EOS_CONFIG_SECTION, TEXT("DeploymentId"), *DeploymentId, GEngineIni);
    GConfig->SetString(*EOS_CONFIG_SECTION, TEXT("ClientId"), *ClientId, GEngineIni);
    GConfig->SetString(*EOS_CONFIG_SECTION, TEXT("ClientSecret"), *ClientSecret, GEngineIni);
    GConfig->SetBool(*EOS_CONFIG_SECTION, TEXT("AuthEnabled"), bEnableAuth, GEngineIni);
    GConfig->SetBool(*EOS_CONFIG_SECTION, TEXT("FriendsEnabled"), bEnableFriends, GEngineIni);
    GConfig->SetBool(*EOS_CONFIG_SECTION, TEXT("AchievementsEnabled"), bEnableAchievements, GEngineIni);
    GConfig->SetBool(*EOS_CONFIG_SECTION, TEXT("StatsEnabled"), bEnableStats, GEngineIni);
    GConfig->SetBool(*EOS_CONFIG_SECTION, TEXT("Enabled"), true, GEngineIni);
    GConfig->Flush(false, GEngineIni);

    LogCloudServicesOperation(TEXT("Epic Online Services"), bSuccess, bSuccess ? TEXT("Epic Online Services configured successfully") : ErrorMessage);

    return CreateJsonResponse(bSuccess, bSuccess ? TEXT("Epic Online Services configured successfully") : ErrorMessage);
}

// === Steam Integration ===

FString UnrealMCPCloudServicesCommands::HandleSetupSteamIntegration(const FString& JsonString)
{
    UE_LOG(LogTemp, Log, TEXT("UnrealMCPCloudServicesCommands: Setting up Steam integration"));

    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return CreateJsonResponse(false, TEXT("Invalid JSON parameters"));
    }

    FString AppId = JsonObject->GetStringField(TEXT("app_id"));
    FString ApiKey = JsonObject->GetStringField(TEXT("api_key"));
    bool bEnableAchievements = JsonObject->GetBoolField(TEXT("enable_achievements"));
    bool bEnableLeaderboards = JsonObject->GetBoolField(TEXT("enable_leaderboards"));
    bool bEnableStats = JsonObject->GetBoolField(TEXT("enable_stats"));
    bool bEnableWorkshop = JsonObject->GetBoolField(TEXT("enable_workshop"));
    bool bEnableMultiplayer = JsonObject->GetBoolField(TEXT("enable_multiplayer"));

    bool bSuccess = true;
    FString ErrorMessage;

    // Setup Steam configuration
    if (!SetupSteamConfig(AppId, ApiKey))
    {
        bSuccess = false;
        ErrorMessage += TEXT("Failed to setup Steam configuration. ");
    }

    // Save Steam configuration
    GConfig->SetString(*STEAM_CONFIG_SECTION, TEXT("AppId"), *AppId, GEngineIni);
    GConfig->SetString(*STEAM_CONFIG_SECTION, TEXT("ApiKey"), *ApiKey, GEngineIni);
    GConfig->SetBool(*STEAM_CONFIG_SECTION, TEXT("AchievementsEnabled"), bEnableAchievements, GEngineIni);
    GConfig->SetBool(*STEAM_CONFIG_SECTION, TEXT("LeaderboardsEnabled"), bEnableLeaderboards, GEngineIni);
    GConfig->SetBool(*STEAM_CONFIG_SECTION, TEXT("StatsEnabled"), bEnableStats, GEngineIni);
    GConfig->SetBool(*STEAM_CONFIG_SECTION, TEXT("WorkshopEnabled"), bEnableWorkshop, GEngineIni);
    GConfig->SetBool(*STEAM_CONFIG_SECTION, TEXT("MultiplayerEnabled"), bEnableMultiplayer, GEngineIni);
    GConfig->SetBool(*STEAM_CONFIG_SECTION, TEXT("Enabled"), true, GEngineIni);
    GConfig->Flush(false, GEngineIni);

    LogCloudServicesOperation(TEXT("Steam Integration"), bSuccess, bSuccess ? TEXT("Steam integration setup successfully") : ErrorMessage);

    return CreateJsonResponse(bSuccess, bSuccess ? TEXT("Steam integration setup successfully") : ErrorMessage);
}

// === Dedicated Servers ===

FString UnrealMCPCloudServicesCommands::HandleConfigureDedicatedServers(const FString& JsonString)
{
    UE_LOG(LogTemp, Log, TEXT("UnrealMCPCloudServicesCommands: Configuring dedicated servers"));

    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return CreateJsonResponse(false, TEXT("Invalid JSON parameters"));
    }

    FString ServerRegion = JsonObject->GetStringField(TEXT("server_region"));
    FString InstanceType = JsonObject->GetStringField(TEXT("instance_type"));
    int32 MaxPlayers = JsonObject->GetIntegerField(TEXT("max_players"));
    int32 MinInstances = JsonObject->GetIntegerField(TEXT("min_instances"));
    int32 MaxInstances = JsonObject->GetIntegerField(TEXT("max_instances"));
    bool bAutoScaling = JsonObject->GetBoolField(TEXT("auto_scaling"));
    bool bLoadBalancing = JsonObject->GetBoolField(TEXT("load_balancing"));

    bool bSuccess = true;
    FString ErrorMessage;

    // Configure dedicated server settings
    if (!ConfigureDedicatedServerSettings(TEXT("aws_ec2"), InstanceType, ServerRegion, MaxPlayers))
    {
        bSuccess = false;
        ErrorMessage += TEXT("Failed to configure dedicated server settings. ");
    }

    // Save dedicated server configuration
    GConfig->SetString(*DEDICATED_SERVER_CONFIG_SECTION, TEXT("ServerRegion"), *ServerRegion, GEngineIni);
    GConfig->SetString(*DEDICATED_SERVER_CONFIG_SECTION, TEXT("InstanceType"), *InstanceType, GEngineIni);
    GConfig->SetInt(*DEDICATED_SERVER_CONFIG_SECTION, TEXT("MaxPlayers"), MaxPlayers, GEngineIni);
    GConfig->SetInt(*DEDICATED_SERVER_CONFIG_SECTION, TEXT("MinInstances"), MinInstances, GEngineIni);
    GConfig->SetInt(*DEDICATED_SERVER_CONFIG_SECTION, TEXT("MaxInstances"), MaxInstances, GEngineIni);
    GConfig->SetBool(*DEDICATED_SERVER_CONFIG_SECTION, TEXT("AutoScaling"), bAutoScaling, GEngineIni);
    GConfig->SetBool(*DEDICATED_SERVER_CONFIG_SECTION, TEXT("LoadBalancing"), bLoadBalancing, GEngineIni);
    GConfig->SetBool(*DEDICATED_SERVER_CONFIG_SECTION, TEXT("Enabled"), true, GEngineIni);
    GConfig->Flush(false, GEngineIni);

    LogCloudServicesOperation(TEXT("Dedicated Servers"), bSuccess, bSuccess ? TEXT("Dedicated servers configured successfully") : ErrorMessage);

    return CreateJsonResponse(bSuccess, bSuccess ? TEXT("Dedicated servers configured successfully") : ErrorMessage);
}

// === Matchmaking Service ===

FString UnrealMCPCloudServicesCommands::HandleSetupMatchmakingService(const FString& JsonString)
{
    UE_LOG(LogTemp, Log, TEXT("UnrealMCPCloudServicesCommands: Setting up matchmaking service"));

    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return CreateJsonResponse(false, TEXT("Invalid JSON parameters"));
    }

    FString MatchmakingQueue = JsonObject->GetStringField(TEXT("matchmaking_queue"));
    FString GameMode = JsonObject->GetStringField(TEXT("game_mode"));
    int32 PlayersPerMatch = JsonObject->GetIntegerField(TEXT("players_per_match"));
    int32 MatchmakingTimeout = JsonObject->GetIntegerField(TEXT("matchmaking_timeout"));
    bool bSkillBasedMatching = JsonObject->GetBoolField(TEXT("skill_based_matching"));
    bool bRegionBasedMatching = JsonObject->GetBoolField(TEXT("region_based_matching"));

    bool bSuccess = true;
    FString ErrorMessage;

    // Setup matchmaking configuration
    if (!SetupMatchmakingConfig(TEXT("Default"), PlayersPerMatch, bSkillBasedMatching, bRegionBasedMatching))
    {
        bSuccess = false;
        ErrorMessage += TEXT("Failed to setup matchmaking configuration. ");
    }

    // Save matchmaking configuration
    GConfig->SetString(*MATCHMAKING_CONFIG_SECTION, TEXT("MatchmakingQueue"), *MatchmakingQueue, GEngineIni);
    GConfig->SetString(*MATCHMAKING_CONFIG_SECTION, TEXT("GameMode"), *GameMode, GEngineIni);
    GConfig->SetInt(*MATCHMAKING_CONFIG_SECTION, TEXT("PlayersPerMatch"), PlayersPerMatch, GEngineIni);
    GConfig->SetInt(*MATCHMAKING_CONFIG_SECTION, TEXT("MatchmakingTimeout"), MatchmakingTimeout, GEngineIni);
    GConfig->SetBool(*MATCHMAKING_CONFIG_SECTION, TEXT("SkillBasedMatching"), bSkillBasedMatching, GEngineIni);
    GConfig->SetBool(*MATCHMAKING_CONFIG_SECTION, TEXT("RegionBasedMatching"), bRegionBasedMatching, GEngineIni);
    GConfig->SetBool(*MATCHMAKING_CONFIG_SECTION, TEXT("Enabled"), true, GEngineIni);
    GConfig->Flush(false, GEngineIni);

    LogCloudServicesOperation(TEXT("Matchmaking Service"), bSuccess, bSuccess ? TEXT("Matchmaking service setup successfully") : ErrorMessage);

    return CreateJsonResponse(bSuccess, bSuccess ? TEXT("Matchmaking service setup successfully") : ErrorMessage);
}

// === Leaderboards System ===

FString UnrealMCPCloudServicesCommands::HandleConfigureLeaderboardsSystem(const FString& JsonString)
{
    UE_LOG(LogTemp, Log, TEXT("UnrealMCPCloudServicesCommands: Configuring leaderboards system"));

    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return CreateJsonResponse(false, TEXT("Invalid JSON parameters"));
    }

    FString LeaderboardName = JsonObject->GetStringField(TEXT("leaderboard_name"));
    FString SortOrder = JsonObject->GetStringField(TEXT("sort_order"));
    FString ScoreFormat = JsonObject->GetStringField(TEXT("score_format"));
    int32 MaxEntries = JsonObject->GetIntegerField(TEXT("max_entries"));
    bool bGlobalLeaderboard = JsonObject->GetBoolField(TEXT("global_leaderboard"));
    bool bFriendsLeaderboard = JsonObject->GetBoolField(TEXT("friends_leaderboard"));
    bool bSeasonalReset = JsonObject->GetBoolField(TEXT("seasonal_reset"));

    bool bSuccess = true;
    FString ErrorMessage;

    // Configure leaderboards settings
    if (!ConfigureLeaderboardsSettings(TEXT("Default"), bGlobalLeaderboard, bFriendsLeaderboard, TEXT("Weekly")))
    {
        bSuccess = false;
        ErrorMessage += TEXT("Failed to configure leaderboards settings. ");
    }

    // Save leaderboards configuration
    GConfig->SetString(*LEADERBOARDS_CONFIG_SECTION, TEXT("LeaderboardName"), *LeaderboardName, GEngineIni);
    GConfig->SetString(*LEADERBOARDS_CONFIG_SECTION, TEXT("SortOrder"), *SortOrder, GEngineIni);
    GConfig->SetString(*LEADERBOARDS_CONFIG_SECTION, TEXT("ScoreFormat"), *ScoreFormat, GEngineIni);
    GConfig->SetInt(*LEADERBOARDS_CONFIG_SECTION, TEXT("MaxEntries"), MaxEntries, GEngineIni);
    GConfig->SetBool(*LEADERBOARDS_CONFIG_SECTION, TEXT("GlobalLeaderboard"), bGlobalLeaderboard, GEngineIni);
    GConfig->SetBool(*LEADERBOARDS_CONFIG_SECTION, TEXT("FriendsLeaderboard"), bFriendsLeaderboard, GEngineIni);
    GConfig->SetBool(*LEADERBOARDS_CONFIG_SECTION, TEXT("SeasonalReset"), bSeasonalReset, GEngineIni);
    GConfig->SetBool(*LEADERBOARDS_CONFIG_SECTION, TEXT("Enabled"), true, GEngineIni);
    GConfig->Flush(false, GEngineIni);

    LogCloudServicesOperation(TEXT("Leaderboards System"), bSuccess, bSuccess ? TEXT("Leaderboards system configured successfully") : ErrorMessage);

    return CreateJsonResponse(bSuccess, bSuccess ? TEXT("Leaderboards system configured successfully") : ErrorMessage);
}

// === Cloud Storage System ===

FString UnrealMCPCloudServicesCommands::HandleSetupCloudStorageSystem(const FString& JsonString)
{
    UE_LOG(LogTemp, Log, TEXT("UnrealMCPCloudServicesCommands: Setting up cloud storage system"));

    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return CreateJsonResponse(false, TEXT("Invalid JSON parameters"));
    }

    FString StorageProvider = JsonObject->GetStringField(TEXT("storage_provider"));
    FString BucketName = JsonObject->GetStringField(TEXT("bucket_name"));
    FString Region = JsonObject->GetStringField(TEXT("region"));
    FString AccessKey = JsonObject->GetStringField(TEXT("access_key"));
    FString SecretKey = JsonObject->GetStringField(TEXT("secret_key"));
    bool bEncryption = JsonObject->GetBoolField(TEXT("encryption"));
    bool bVersioning = JsonObject->GetBoolField(TEXT("versioning"));
    bool bBackup = JsonObject->GetBoolField(TEXT("backup"));
    bool bCDN = JsonObject->GetBoolField(TEXT("cdn"));

    bool bSuccess = true;
    FString ErrorMessage;

    // Setup cloud storage configuration
    if (!SetupCloudStorageConfig(StorageProvider, BucketName, bCDN, bVersioning, bEncryption))
    {
        bSuccess = false;
        ErrorMessage += TEXT("Failed to setup cloud storage configuration. ");
    }

    // Save cloud storage configuration
    GConfig->SetString(*CLOUD_STORAGE_CONFIG_SECTION, TEXT("StorageProvider"), *StorageProvider, GEngineIni);
    GConfig->SetString(*CLOUD_STORAGE_CONFIG_SECTION, TEXT("BucketName"), *BucketName, GEngineIni);
    GConfig->SetString(*CLOUD_STORAGE_CONFIG_SECTION, TEXT("Region"), *Region, GEngineIni);
    GConfig->SetString(*CLOUD_STORAGE_CONFIG_SECTION, TEXT("AccessKey"), *AccessKey, GEngineIni);
    GConfig->SetString(*CLOUD_STORAGE_CONFIG_SECTION, TEXT("SecretKey"), *SecretKey, GEngineIni);
    GConfig->SetBool(*CLOUD_STORAGE_CONFIG_SECTION, TEXT("Encryption"), bEncryption, GEngineIni);
    GConfig->SetBool(*CLOUD_STORAGE_CONFIG_SECTION, TEXT("Versioning"), bVersioning, GEngineIni);
    GConfig->SetBool(*CLOUD_STORAGE_CONFIG_SECTION, TEXT("Backup"), bBackup, GEngineIni);
    GConfig->SetBool(*CLOUD_STORAGE_CONFIG_SECTION, TEXT("Enabled"), true, GEngineIni);
    GConfig->Flush(false, GEngineIni);

    LogCloudServicesOperation(TEXT("Cloud Storage System"), bSuccess, bSuccess ? TEXT("Cloud storage system setup successfully") : ErrorMessage);

    return CreateJsonResponse(bSuccess, bSuccess ? TEXT("Cloud storage system setup successfully") : ErrorMessage);
}

// === CDN Distribution ===

FString UnrealMCPCloudServicesCommands::HandleConfigureCDNDistribution(const FString& JsonString)
{
    UE_LOG(LogTemp, Log, TEXT("UnrealMCPCloudServicesCommands: Configuring CDN distribution"));

    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return CreateJsonResponse(false, TEXT("Invalid JSON parameters"));
    }

    FString CDNProvider = JsonObject->GetStringField(TEXT("cdn_provider"));
    FString DistributionId = JsonObject->GetStringField(TEXT("distribution_id"));
    FString OriginDomain = JsonObject->GetStringField(TEXT("origin_domain"));
    FString CacheBehavior = JsonObject->GetStringField(TEXT("cache_behavior"));
    TArray<FString> EdgeLocations;
    const TArray<TSharedPtr<FJsonValue>>* EdgeLocationsArray;
    if (JsonObject->TryGetArrayField(TEXT("edge_locations"), EdgeLocationsArray))
    {
        for (const auto& Location : *EdgeLocationsArray)
        {
            EdgeLocations.Add(Location->AsString());
        }
    }
    bool bCompression = JsonObject->GetBoolField(TEXT("compression"));
    bool bHTTPS = JsonObject->GetBoolField(TEXT("https"));

    bool bSuccess = true;
    FString ErrorMessage;

    // Configure CDN settings
    if (!ConfigureCDNSettings(CDNProvider, DistributionId, OriginDomain, CacheBehavior))
    {
        bSuccess = false;
        ErrorMessage += TEXT("Failed to configure CDN settings. ");
    }

    // Save CDN configuration
    GConfig->SetString(*CDN_CONFIG_SECTION, TEXT("CDNProvider"), *CDNProvider, GEngineIni);
    GConfig->SetString(*CDN_CONFIG_SECTION, TEXT("DistributionId"), *DistributionId, GEngineIni);
    GConfig->SetString(*CDN_CONFIG_SECTION, TEXT("OriginDomain"), *OriginDomain, GEngineIni);
    GConfig->SetString(*CDN_CONFIG_SECTION, TEXT("CacheBehavior"), *CacheBehavior, GEngineIni);
    GConfig->SetBool(*CDN_CONFIG_SECTION, TEXT("Compression"), bCompression, GEngineIni);
    GConfig->SetBool(*CDN_CONFIG_SECTION, TEXT("HTTPS"), bHTTPS, GEngineIni);
    GConfig->SetBool(*CDN_CONFIG_SECTION, TEXT("Enabled"), true, GEngineIni);
    GConfig->Flush(false, GEngineIni);

    LogCloudServicesOperation(TEXT("CDN Distribution"), bSuccess, bSuccess ? TEXT("CDN distribution configured successfully") : ErrorMessage);

    return CreateJsonResponse(bSuccess, bSuccess ? TEXT("CDN distribution configured successfully") : ErrorMessage);
}

// === Helper Functions ===

bool UnrealMCPCloudServicesCommands::ValidateAWSConfig(const TSharedPtr<FJsonObject>& JsonObject)
{
    if (!JsonObject.IsValid())
    {
        return false;
    }

    // Check required AWS fields
    if (!JsonObject->HasField(TEXT("access_key_id")) || JsonObject->GetStringField(TEXT("access_key_id")).IsEmpty())
    {
        UE_LOG(LogTemp, Error, TEXT("AWS configuration missing access_key_id"));
        return false;
    }

    if (!JsonObject->HasField(TEXT("secret_access_key")) || JsonObject->GetStringField(TEXT("secret_access_key")).IsEmpty())
    {
        UE_LOG(LogTemp, Error, TEXT("AWS configuration missing secret_access_key"));
        return false;
    }

    return true;
}

bool UnrealMCPCloudServicesCommands::ValidateAzureConfig(const TSharedPtr<FJsonObject>& JsonObject)
{
    if (!JsonObject.IsValid())
    {
        return false;
    }

    // Check required Azure fields
    if (!JsonObject->HasField(TEXT("subscription_id")) || JsonObject->GetStringField(TEXT("subscription_id")).IsEmpty())
    {
        UE_LOG(LogTemp, Error, TEXT("Azure configuration missing subscription_id"));
        return false;
    }

    if (!JsonObject->HasField(TEXT("tenant_id")) || JsonObject->GetStringField(TEXT("tenant_id")).IsEmpty())
    {
        UE_LOG(LogTemp, Error, TEXT("Azure configuration missing tenant_id"));
        return false;
    }

    return true;
}

bool UnrealMCPCloudServicesCommands::ValidateGCPConfig(const TSharedPtr<FJsonObject>& JsonObject)
{
    if (!JsonObject.IsValid())
    {
        return false;
    }

    // Check required GCP fields
    if (!JsonObject->HasField(TEXT("project_id")) || JsonObject->GetStringField(TEXT("project_id")).IsEmpty())
    {
        UE_LOG(LogTemp, Error, TEXT("GCP configuration missing project_id"));
        return false;
    }

    if (!JsonObject->HasField(TEXT("service_account_key")) || JsonObject->GetStringField(TEXT("service_account_key")).IsEmpty())
    {
        UE_LOG(LogTemp, Error, TEXT("GCP configuration missing service_account_key"));
        return false;
    }

    return true;
}

bool UnrealMCPCloudServicesCommands::ValidateFirebaseConfig(const TSharedPtr<FJsonObject>& JsonObject)
{
    if (!JsonObject.IsValid())
    {
        return false;
    }

    // Check required Firebase fields
    if (!JsonObject->HasField(TEXT("project_id")) || JsonObject->GetStringField(TEXT("project_id")).IsEmpty())
    {
        UE_LOG(LogTemp, Error, TEXT("Firebase configuration missing project_id"));
        return false;
    }

    if (!JsonObject->HasField(TEXT("api_key")) || JsonObject->GetStringField(TEXT("api_key")).IsEmpty())
    {
        UE_LOG(LogTemp, Error, TEXT("Firebase configuration missing api_key"));
        return false;
    }

    return true;
}

bool UnrealMCPCloudServicesCommands::InitializeAWS(const FString& AccessKeyId, const FString& SecretAccessKey, const FString& Region)
{
    UE_LOG(LogTemp, Log, TEXT("Initializing AWS SDK with region: %s"), *Region);

    // Configure AWS settings
    GConfig->SetString(*AWS_CONFIG_SECTION, TEXT("AccessKeyId"), *AccessKeyId, GEngineIni);
    GConfig->SetString(*AWS_CONFIG_SECTION, TEXT("SecretAccessKey"), *SecretAccessKey, GEngineIni);
    GConfig->SetString(*AWS_CONFIG_SECTION, TEXT("Region"), *Region, GEngineIni);

    return true;
}

bool UnrealMCPCloudServicesCommands::ConfigureAWSServices(const TArray<FString>& Services, bool bS3, bool bDynamoDB, bool bLambda)
{
    UE_LOG(LogTemp, Log, TEXT("Configuring AWS services"));

    GConfig->SetBool(*AWS_CONFIG_SECTION, TEXT("S3Enabled"), bS3, GEngineIni);
    GConfig->SetBool(*AWS_CONFIG_SECTION, TEXT("DynamoDBEnabled"), bDynamoDB, GEngineIni);
    GConfig->SetBool(*AWS_CONFIG_SECTION, TEXT("LambdaEnabled"), bLambda, GEngineIni);

    // Save enabled services
    for (int32 i = 0; i < Services.Num(); ++i)
    {
        FString ServiceKey = FString::Printf(TEXT("Service%d"), i + 1);
        GConfig->SetString(*AWS_CONFIG_SECTION, *ServiceKey, *Services[i], GEngineIni);
    }

    return true;
}

bool UnrealMCPCloudServicesCommands::SetupAzureAuth(const FString& SubscriptionId, const FString& TenantId,
                                                   const FString& ClientId, const FString& ClientSecret)
{
    UE_LOG(LogTemp, Log, TEXT("Setting up Azure authentication"));

    GConfig->SetString(*AZURE_CONFIG_SECTION, TEXT("SubscriptionId"), *SubscriptionId, GEngineIni);
    GConfig->SetString(*AZURE_CONFIG_SECTION, TEXT("TenantId"), *TenantId, GEngineIni);
    GConfig->SetString(*AZURE_CONFIG_SECTION, TEXT("ClientId"), *ClientId, GEngineIni);
    GConfig->SetString(*AZURE_CONFIG_SECTION, TEXT("ClientSecret"), *ClientSecret, GEngineIni);

    return true;
}

bool UnrealMCPCloudServicesCommands::ConfigureGCPAuth(const FString& ProjectId, const FString& ServiceAccountKey, const FString& Region)
{
    UE_LOG(LogTemp, Log, TEXT("Configuring GCP authentication for project: %s"), *ProjectId);

    GConfig->SetString(*GCP_CONFIG_SECTION, TEXT("ProjectId"), *ProjectId, GEngineIni);
    GConfig->SetString(*GCP_CONFIG_SECTION, TEXT("ServiceAccountKey"), *ServiceAccountKey, GEngineIni);
    GConfig->SetString(*GCP_CONFIG_SECTION, TEXT("Region"), *Region, GEngineIni);

    return true;
}

bool UnrealMCPCloudServicesCommands::ConfigureFirebaseSettings(const FString& ProjectId, const FString& ApiKey, const FString& DatabaseUrl, const FString& StorageBucket)
{
    UE_LOG(LogTemp, Log, TEXT("Configuring Firebase settings for project: %s"), *ProjectId);

    GConfig->SetString(*FIREBASE_CONFIG_SECTION, TEXT("ProjectId"), *ProjectId, GEngineIni);
    GConfig->SetString(*FIREBASE_CONFIG_SECTION, TEXT("ApiKey"), *ApiKey, GEngineIni);
    GConfig->SetString(*FIREBASE_CONFIG_SECTION, TEXT("DatabaseUrl"), *DatabaseUrl, GEngineIni);
    GConfig->SetString(*FIREBASE_CONFIG_SECTION, TEXT("StorageBucket"), *StorageBucket, GEngineIni);

    return true;
}

bool UnrealMCPCloudServicesCommands::SetupPlayFabConfig(const FString& TitleId, const FString& SecretKey, const FString& DeveloperKey)
{
    UE_LOG(LogTemp, Log, TEXT("Setting up PlayFab configuration for title: %s"), *TitleId);

    GConfig->SetString(*PLAYFAB_CONFIG_SECTION, TEXT("TitleId"), *TitleId, GEngineIni);
    GConfig->SetString(*PLAYFAB_CONFIG_SECTION, TEXT("SecretKey"), *SecretKey, GEngineIni);
    GConfig->SetString(*PLAYFAB_CONFIG_SECTION, TEXT("DeveloperKey"), *DeveloperKey, GEngineIni);

    return true;
}

bool UnrealMCPCloudServicesCommands::SetupSteamConfig(const FString& AppId, const FString& ApiKey)
{
    UE_LOG(LogTemp, Log, TEXT("Setting up Steam configuration for app: %s"), *AppId);

    GConfig->SetString(*STEAM_CONFIG_SECTION, TEXT("AppId"), *AppId, GEngineIni);
    GConfig->SetString(*STEAM_CONFIG_SECTION, TEXT("ApiKey"), *ApiKey, GEngineIni);

    return true;
}

bool UnrealMCPCloudServicesCommands::SetupCloudStorageConfig(const FString& Provider, const FString& BucketName, bool bCDN, bool bVersioning, bool bEncryption)
{
    UE_LOG(LogTemp, Log, TEXT("Setting up cloud storage configuration for provider: %s"), *Provider);

    GConfig->SetString(*CLOUD_STORAGE_CONFIG_SECTION, TEXT("Provider"), *Provider, GEngineIni);
    GConfig->SetString(*CLOUD_STORAGE_CONFIG_SECTION, TEXT("BucketName"), *BucketName, GEngineIni);
    GConfig->SetBool(*CLOUD_STORAGE_CONFIG_SECTION, TEXT("CDNEnabled"), bCDN, GEngineIni);
    GConfig->SetBool(*CLOUD_STORAGE_CONFIG_SECTION, TEXT("VersioningEnabled"), bVersioning, GEngineIni);
    GConfig->SetBool(*CLOUD_STORAGE_CONFIG_SECTION, TEXT("EncryptionEnabled"), bEncryption, GEngineIni);

    return true;
}

bool UnrealMCPCloudServicesCommands::ConfigureCDNSettings(const FString& CDNProvider, const FString& DistributionId, const FString& OriginDomain, const FString& CacheBehavior)
{
    UE_LOG(LogTemp, Log, TEXT("Configuring CDN settings for provider: %s"), *CDNProvider);

    GConfig->SetString(*CDN_CONFIG_SECTION, TEXT("CDNProvider"), *CDNProvider, GEngineIni);
    GConfig->SetString(*CDN_CONFIG_SECTION, TEXT("DistributionId"), *DistributionId, GEngineIni);
    GConfig->SetString(*CDN_CONFIG_SECTION, TEXT("OriginDomain"), *OriginDomain, GEngineIni);
    GConfig->SetString(*CDN_CONFIG_SECTION, TEXT("CacheBehavior"), *CacheBehavior, GEngineIni);

    return true;
}

bool UnrealMCPCloudServicesCommands::ConfigureEOSSettings(const FString& ProductId, const FString& SandboxId, const FString& DeploymentId, const FString& ClientId, const FString& ClientSecret)
{
    UE_LOG(LogTemp, Log, TEXT("Configuring Epic Online Services settings for product: %s"), *ProductId);

    GConfig->SetString(*EOS_CONFIG_SECTION, TEXT("ProductId"), *ProductId, GEngineIni);
    GConfig->SetString(*EOS_CONFIG_SECTION, TEXT("SandboxId"), *SandboxId, GEngineIni);
    GConfig->SetString(*EOS_CONFIG_SECTION, TEXT("DeploymentId"), *DeploymentId, GEngineIni);
    GConfig->SetString(*EOS_CONFIG_SECTION, TEXT("ClientId"), *ClientId, GEngineIni);
    GConfig->SetString(*EOS_CONFIG_SECTION, TEXT("ClientSecret"), *ClientSecret, GEngineIni);
    GConfig->SetString(*EOS_CONFIG_SECTION, TEXT("SDKVersion"), *EOS_SDK_VERSION, GEngineIni);

    return true;
}

bool UnrealMCPCloudServicesCommands::ConfigureDedicatedServerSettings(const FString& ServerType, const FString& InstanceType, const FString& Region, int32 MaxPlayers)
{
    UE_LOG(LogTemp, Log, TEXT("Configuring dedicated server settings - Type: %s, Instance: %s, Region: %s"), *ServerType, *InstanceType, *Region);

    GConfig->SetString(*DEDICATED_SERVER_CONFIG_SECTION, TEXT("ServerType"), *ServerType, GEngineIni);
    GConfig->SetString(*DEDICATED_SERVER_CONFIG_SECTION, TEXT("InstanceType"), *InstanceType, GEngineIni);
    GConfig->SetString(*DEDICATED_SERVER_CONFIG_SECTION, TEXT("Region"), *Region, GEngineIni);
    GConfig->SetInt(*DEDICATED_SERVER_CONFIG_SECTION, TEXT("MaxPlayers"), MaxPlayers, GEngineIni);
    GConfig->SetBool(*DEDICATED_SERVER_CONFIG_SECTION, TEXT("Enabled"), true, GEngineIni);

    return true;
}

bool UnrealMCPCloudServicesCommands::SetupMatchmakingConfig(const FString& ServiceProvider, int32 MaxPlayersPerMatch, bool bSkillBasedMatching, bool bRegionBasedMatching)
{
    UE_LOG(LogTemp, Log, TEXT("Setting up matchmaking configuration - Provider: %s, Max Players: %d"), *ServiceProvider, MaxPlayersPerMatch);

    GConfig->SetString(*MATCHMAKING_CONFIG_SECTION, TEXT("ServiceProvider"), *ServiceProvider, GEngineIni);
    GConfig->SetInt(*MATCHMAKING_CONFIG_SECTION, TEXT("MaxPlayersPerMatch"), MaxPlayersPerMatch, GEngineIni);
    GConfig->SetBool(*MATCHMAKING_CONFIG_SECTION, TEXT("SkillBasedMatching"), bSkillBasedMatching, GEngineIni);
    GConfig->SetBool(*MATCHMAKING_CONFIG_SECTION, TEXT("RegionBasedMatching"), bRegionBasedMatching, GEngineIni);
    GConfig->SetBool(*MATCHMAKING_CONFIG_SECTION, TEXT("Enabled"), true, GEngineIni);

    return true;
}

bool UnrealMCPCloudServicesCommands::ConfigureLeaderboardsSettings(const FString& Provider, bool bGlobalLeaderboards, bool bFriendLeaderboards, const FString& ResetSchedule)
{
    UE_LOG(LogTemp, Log, TEXT("Configuring leaderboards settings - Provider: %s, Reset Schedule: %s"), *Provider, *ResetSchedule);

    GConfig->SetString(*LEADERBOARDS_CONFIG_SECTION, TEXT("Provider"), *Provider, GEngineIni);
    GConfig->SetBool(*LEADERBOARDS_CONFIG_SECTION, TEXT("GlobalLeaderboards"), bGlobalLeaderboards, GEngineIni);
    GConfig->SetBool(*LEADERBOARDS_CONFIG_SECTION, TEXT("FriendLeaderboards"), bFriendLeaderboards, GEngineIni);
    GConfig->SetString(*LEADERBOARDS_CONFIG_SECTION, TEXT("ResetSchedule"), *ResetSchedule, GEngineIni);
    GConfig->SetBool(*LEADERBOARDS_CONFIG_SECTION, TEXT("Enabled"), true, GEngineIni);

    return true;
}

FString UnrealMCPCloudServicesCommands::CreateJsonResponse(bool bSuccess, const FString& Message, const TSharedPtr<FJsonObject>& Data)
{
    TSharedPtr<FJsonObject> ResponseJson = MakeShareable(new FJsonObject);
    ResponseJson->SetBoolField(TEXT("success"), bSuccess);
    ResponseJson->SetStringField(TEXT("message"), Message);

    if (Data.IsValid())
    {
        ResponseJson->SetObjectField(TEXT("data"), Data);
    }

    FString ResponseString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&ResponseString);
    FJsonSerializer::Serialize(ResponseJson.ToSharedRef(), Writer);

    return ResponseString;
}

void UnrealMCPCloudServicesCommands::LogCloudServicesOperation(const FString& Operation, bool bSuccess, const FString& Message)
{
    if (bSuccess)
    {
        UE_LOG(LogTemp, Log, TEXT("Cloud Services - %s: %s"), *Operation, *Message);
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("Cloud Services - %s: %s"), *Operation, *Message);
    }
}
