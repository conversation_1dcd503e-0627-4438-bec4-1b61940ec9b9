{"Version": "1.2", "Data": {"Source": "c:\\game\\auracron\\plugins\\unrealmcp\\source\\unrealmcp\\private\\commands\\unrealmcpnetworkingcommands.cpp", "ProvidedModule": "", "PCH": "c:\\game\\auracron\\intermediate\\build\\win64\\x64\\auracroneditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.valexpapi.cpp20.h.pch", "Includes": ["c:\\game\\auracron\\plugins\\unrealmcp\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\unrealmcp\\definitions.unrealmcp.h", "c:\\game\\auracron\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpnetworkingcommands.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\gamemodebase.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\serverstatreplicator.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\serverstatreplicator.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gamemodebase.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\spectatorpawn.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\defaultpawn.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\defaultpawn.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\spectatorpawn.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\net\\unrealnetwork.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\net\\core\\public\\net\\core\\propertyconditions\\propertyconditions.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\net\\replayout.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\containers\\staticbitarray.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\net\\core\\public\\net\\core\\misc\\guidreferences.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\net\\core\\public\\net\\core\\nettoken\\nettokenexportcontext.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\net\\core\\public\\net\\core\\pushmodel\\pushmodel.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\net\\core\\public\\net\\core\\propertyconditions\\repchangedpropertytracker.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\net\\datareplication.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\replicationgraph\\source\\public\\replicationgraph.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\replicationgraph\\source\\public\\replicationgraphtypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\replicationgraph\\intermediate\\build\\win64\\unrealeditor\\inc\\replicationgraph\\uht\\replicationgraphtypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\replicationgraph\\intermediate\\build\\win64\\unrealeditor\\inc\\replicationgraph\\uht\\replicationgraph.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\iris\\core\\public\\iris\\replicationsystem\\replicationsystem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\iris\\core\\public\\iris\\irisconfig.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\iris\\core\\public\\iris\\replicationsystem\\netrefhandle.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\iris\\core\\public\\iris\\replicationsystem\\netobjectgrouphandle.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\experimental\\iris\\core\\public\\iris\\replicationsystem\\replicationsystemtypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\net\\core\\public\\net\\core\\nethandle\\nethandle.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\iriscore\\uht\\replicationsystem.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\online\\onlinesubsystem\\source\\public\\onlinesubsystem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\online\\onlinesubsystem\\source\\public\\onlinesubsystemmodule.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\online\\onlinesubsystem\\source\\public\\onlinesubsystemtypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\online\\onlinesubsystem\\source\\public\\onlinesubsystemnames.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\online\\onlinesubsystem\\source\\public\\onlinedelegatemacros.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\online\\onlinesubsystem\\source\\public\\onlinesessionsettings.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\online\\onlinesubsystem\\source\\public\\onlinekeyvaluepair.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\online\\onlinesubsystem\\source\\public\\interfaces\\onlinesessioninterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\online\\onlinesubsystem\\source\\public\\interfaces\\onlinesessiondelegates.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\gameplaystatics.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\kismetsystemlibrary.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\propertyaccessutil.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\kismetsystemlibrary.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\dialoguetypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\dialoguetypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\gameplaystaticstypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gameplaystaticstypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gameplaystatics.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\net\\networkprofiler.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\online\\onlinesubsystemutils\\source\\onlinesubsystemutils\\public\\onlinesubsystemutils.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\online\\onlinesubsystem\\source\\public\\interfaces\\onlineexternaluiinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\online\\onlinesubsystem\\source\\public\\interfaces\\onlinemessageinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\online\\onlinesubsystem\\source\\public\\onlineerror.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\online\\onlinesubsystemutils\\source\\onlinesubsystemutils\\public\\onlinesubsystemutilsmodule.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\online\\onlinesubsystem\\source\\public\\interfaces\\onlinefriendsinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\online\\onlinesubsystem\\source\\public\\interfaces\\onlineidentityinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\online\\onlinesubsystem\\source\\public\\interfaces\\onlinetitlefileinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\online\\onlinesubsystem\\source\\public\\interfaces\\onlineusercloudinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\online\\onlinesubsystem\\source\\public\\interfaces\\voiceinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\online\\onlinesubsystem\\source\\public\\onlinesubsystempackage.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\signalprocessing\\public\\dsp\\multithreadedpatching.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\playerstate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\playerstate.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\classes\\k2node_event.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\blueprintgraph\\classes\\k2node_eventnodeinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprintgraph\\uht\\k2node_eventnodeinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\kismetcompiler\\public\\kismetcompilermisc.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\kismetcompiler\\public\\bpterminal.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\kismetcompiler\\public\\blueprintcompiledstatement.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\blueprintgraph\\uht\\k2node_event.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\unrealed\\public\\kismet2\\blueprinteditorutils.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\classviewer\\public\\classviewermodule.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\unrealed\\public\\kismet2\\kismeteditorutilities.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}