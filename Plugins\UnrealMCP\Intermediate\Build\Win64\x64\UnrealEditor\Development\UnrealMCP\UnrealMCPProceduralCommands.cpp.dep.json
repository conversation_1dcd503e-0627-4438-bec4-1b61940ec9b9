{"Version": "1.2", "Data": {"Source": "c:\\game\\auracron\\plugins\\unrealmcp\\source\\unrealmcp\\private\\commands\\unrealmcpproceduralcommands.cpp", "ProvidedModule": "", "PCH": "c:\\game\\auracron\\intermediate\\build\\win64\\x64\\auracroneditor\\development\\unrealed\\sharedpch.unrealed.project.valapi.valexpapi.cpp20.h.pch", "Includes": ["c:\\game\\auracron\\plugins\\unrealmcp\\intermediate\\build\\win64\\x64\\unrealeditor\\development\\unrealmcp\\definitions.unrealmcp.h", "c:\\game\\auracron\\plugins\\unrealmcp\\source\\unrealmcp\\public\\commands\\unrealmcpproceduralcommands.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\gameplaystatics.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\kismetsystemlibrary.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\uobject\\propertyaccessutil.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\kismetsystemlibrary.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\sound\\dialoguetypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\dialoguetypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\kismet\\gameplaystaticstypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gameplaystaticstypes.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gameplaystatics.generated.h", "c:\\game\\auracron\\plugins\\unrealmcp\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealmcp\\uht\\unrealmcpproceduralcommands.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\structutils\\propertybag.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\structutils\\structview.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\structutils\\instancedstruct.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\structutils\\structutils.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\coreuobject\\uht\\instancedstruct.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\structutils\\sharedstruct.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\coreuobject\\public\\structutils\\structutilstypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\coreuobject\\uht\\sharedstruct.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\coreuobject\\uht\\propertybag.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgcomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgcommon.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgcommon.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcggraphexecutioninspection.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgdata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgcrc.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgcrc.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\data\\pcgdataptrwrapper.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgdataptrwrapper.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\metadata\\pcgattributepropertyselector.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgpoint.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\helpers\\pcgpointhelpers.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgpoint.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgattributepropertyselector.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\metadata\\pcgmetadatacommon.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgmetadatacommon.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgdata.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgnode.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgnode.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\graph\\pcgstackcontext.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\utils\\pcgextracapture.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgstackcontext.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcggraphexecutionstateinterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgsettings.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgdebug.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgdebug.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgelement.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgpin.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgpin.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\elements\\pcgactorselector.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgactorselector.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\utils\\pcgpreconfiguration.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\metadata\\pcgmetadataattributetraits.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\misc\\defaultvaluehelper.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgmetadataattributetraits.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgpreconfiguration.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\tests\\determinism\\pcgdeterminismsettings.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgdeterminismsettings.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\algo\\allof.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgsettings.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcggraphexecutionstateinterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\grid\\pcggriddescriptor.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\datalayerasset.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\datalayer\\datalayertype.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\datalayertype.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\datalayerasset.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\hlod\\hlodlayer.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\meshmerge\\meshmergingsettings.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\meshmergingsettings.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\meshmerge\\meshproxysettings.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\meshproxysettings.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\meshmerge\\meshapproximationsettings.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\meshapproximationsettings.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\worldpartition\\hlod\\hlodbuilder.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\hlodbuilder.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\hlodlayer.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcggriddescriptor.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgcomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcggraph.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\editor\\pcggraphcustomization.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcggraphcustomization.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\helpers\\pcggraphparameterextension.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\editor\\pcggraphcomment.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\editor\\unrealed\\public\\edgraphnode_comment.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\unrealed\\uht\\edgraphnode_comment.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcggraphcomment.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\computeframework\\source\\computeframework\\public\\computeframework\\computegraphinstance.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\runtime\\computeframework\\intermediate\\build\\win64\\unrealeditor\\inc\\computeframework\\uht\\computegraphinstance.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcggraph.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgsubsystem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\grid\\pcgcomponentoctree.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\utils\\pcgnodevisuallogs.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgsubsystem.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\source\\pcg\\public\\pcgworldactor.h", "c:\\program files\\epic games\\ue_5.6\\engine\\plugins\\pcg\\intermediate\\build\\win64\\unrealeditor\\inc\\pcg\\uht\\pcgworldactor.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\spherecomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\components\\shapecomponent.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\shapecomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\spherecomponent.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\navigationsystembase.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\ai\\navigationmodifier.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navlinkdefinition.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navagentselector.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navagentselector.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navlinkdefinition.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navigationdataresolution.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navigationdataresolution.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navigationsystembase.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\navigationsystem\\public\\navigationsystem.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navigationdirtyelement.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navigationinvokerpriority.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navigationinvokerpriority.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\navigationsystem\\public\\navigationsystemtypes.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\navigationsystem\\public\\navigationdata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navigationdatainterface.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navigationdatainterface.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\navigationsystem\\uht\\navigationdata.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\navigationsystem\\public\\navigationoctree.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navigationelement.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navigationrelevantdata.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navigationdirtyarea.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\navigationsystemconfig.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\navigationsystemconfig.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\navigationsystem\\public\\navigationoctreecontroller.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\navigationsystem\\public\\navigationdirtyareascontroller.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\core\\public\\math\\movingwindowaveragefast.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\ai\\navigation\\navigationbounds.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\navigationsystem\\uht\\navigationsystem.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\navigationsystem\\public\\navmesh\\navmeshboundsvolume.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\navigationsystem\\uht\\navmeshboundsvolume.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\gamemodebase.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\engine\\serverstatreplicator.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\serverstatreplicator.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\gamemodebase.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\classes\\gameframework\\playerstate.h", "c:\\program files\\epic games\\ue_5.6\\engine\\intermediate\\build\\win64\\unrealeditor\\inc\\engine\\uht\\playerstate.generated.h", "c:\\program files\\epic games\\ue_5.6\\engine\\source\\runtime\\engine\\public\\engineutils.h"], "ImportedModules": [], "ImportedHeaderUnits": []}}