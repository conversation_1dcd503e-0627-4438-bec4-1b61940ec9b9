#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"

// Forward declarations
class UNavigationPath;
class AAIController;
class UBTNode;

/**
 * Classe responsável por gerenciar comandos relacionados ao Sistema de IA Adaptativa.
 * Inclui pipeline de machine learning, spawn adaptativo, eventos especiais,
 * comportamentos adaptativos, comunicação entre NPCs e profiling de jogador.
 */
class UNREALMCP_API FUnrealMCPAICommands
{
public:
    FUnrealMCPAICommands();
    ~FUnrealMCPAICommands();

    /**
     * Manipula comandos relacionados ao sistema de IA adaptativa.
     * @param Command O comando a ser executado
     * @param CommandData Os dados do comando em formato JSON
     * @return Resposta do comando em formato JSON
     */
    TSharedPtr<FJsonObject> HandleCommand(const FString& Command, const TSharedPtr<FJsonObject>& CommandData);

private:
    // ============================================================================
    // Manipuladores de Comandos Principais
    // ============================================================================
    
    /**
     * Cria pipeline de machine learning para IA adaptativa.
     */
    TSharedPtr<FJsonObject> HandleCreateAILearningPipeline(const TSharedPtr<FJsonObject>& CommandData);
    
    /**
     * Configura comportamentos adaptativos para NPCs.
     */
    TSharedPtr<FJsonObject> HandleConfigureAdaptiveBehavior(const TSharedPtr<FJsonObject>& CommandData);
    
    /**
     * Configura sistema de spawn dinâmico baseado em IA.
     */
    TSharedPtr<FJsonObject> HandleSetupDynamicSpawnSystem(const TSharedPtr<FJsonObject>& CommandData);
    
    /**
     * Cria árvore de decisão para IA adaptativa.
     */
    TSharedPtr<FJsonObject> HandleCreateAIDecisionTree(const TSharedPtr<FJsonObject>& CommandData);
    
    /**
     * Configura eventos especiais adaptativos.
     */
    TSharedPtr<FJsonObject> HandleConfigureSpecialEvents(const TSharedPtr<FJsonObject>& CommandData);
    
    /**
     * Configura sistema de comunicação entre NPCs.
     */
    TSharedPtr<FJsonObject> HandleSetupAICommunicationSystem(const TSharedPtr<FJsonObject>& CommandData);
    
    /**
     * Configura sistema de profiling do jogador para adaptação da IA.
     */
    TSharedPtr<FJsonObject> HandleConfigurePlayerProfiling(const TSharedPtr<FJsonObject>& CommandData);
    
    /**
     * Configura sistema de memória para NPCs.
     */
    TSharedPtr<FJsonObject> HandleSetupAIMemorySystem(const TSharedPtr<FJsonObject>& CommandData);
    
    /**
     * Otimiza performance do sistema de IA.
     */
    TSharedPtr<FJsonObject> HandleOptimizeAIPerformance(const TSharedPtr<FJsonObject>& CommandData);
    
    /**
     * Ativa ferramentas de debug para o sistema de IA.
     */
    TSharedPtr<FJsonObject> HandleDebugAISystem(const TSharedPtr<FJsonObject>& CommandData);
    
    /**
     * Valida a configuração do sistema de IA.
     */
    TSharedPtr<FJsonObject> HandleValidateAISetup(const TSharedPtr<FJsonObject>& CommandData);
    
    /**
     * Obtém status do sistema de IA adaptativa.
     */
    TSharedPtr<FJsonObject> HandleGetAISystemStatus(const TSharedPtr<FJsonObject>& CommandData);

    // ============================================================================
    // Funções Auxiliares
    // ============================================================================
    
    /**
     * Converte string para tipo de aprendizado.
     */
    FString ConvertLearningType(const FString& LearningType);
    
    /**
     * Converte string para tipo de comportamento.
     */
    FString ConvertBehaviorType(const FString& BehaviorType);
    
    /**
     * Converte string para tipo de evento.
     */
    FString ConvertEventType(const FString& EventType);
    
    /**
     * Converte string para tipo de memória.
     */
    FString ConvertMemoryType(const FString& MemoryType);
    
    /**
     * Valida configuração de modelo de ML.
     */
    bool ValidateModelConfig(const TSharedPtr<FJsonObject>& ModelConfig);
    
    /**
     * Valida regras de adaptação.
     */
    bool ValidateAdaptationRules(const TArray<TSharedPtr<FJsonValue>>& Rules);
    
    /**
     * Valida configuração de spawn.
     */
    bool ValidateSpawnConfig(const TSharedPtr<FJsonObject>& SpawnConfig);
    
    /**
     * Valida nós de árvore de decisão.
     */
    bool ValidateDecisionNodes(const TArray<TSharedPtr<FJsonValue>>& Nodes);
    
    /**
     * Detecta ciclos em árvore de decisão (função auxiliar).
     */
    bool HasCycle(const FString& NodeId, const TMap<FString, TArray<FString>>& Connections, TSet<FString>& Visited, TSet<FString>& RecursionStack);
    
    /**
     * Valida configuração de eventos.
     */
    bool ValidateEventConfig(const TSharedPtr<FJsonObject>& EventConfig);
    
    /**
     * Valida métricas de profiling.
     */
    bool ValidateProfilingMetrics(const TArray<TSharedPtr<FJsonValue>>& Metrics);
    
    /**
     * Valida configuração de memória.
     */
    bool ValidateMemoryConfig(const TSharedPtr<FJsonObject>& MemoryConfig);
    
    /**
     * Valida configurações de otimização.
     */
    bool ValidateOptimizationSettings(const TSharedPtr<FJsonObject>& Settings);
    
    /**
     * Cria resposta de sucesso com dados.
     */
    TSharedPtr<FJsonObject> CreateSuccessResponse(const FString& Message, const TSharedPtr<FJsonObject>& Data = nullptr);
    
    /**
     * Cria resposta de erro.
     */
    TSharedPtr<FJsonObject> CreateErrorResponse(const FString& Error);
    
    /**
     * Cria pipeline básico de ML.
     */
    TSharedPtr<FJsonObject> CreateBasicMLPipeline(const FString& LayerName, const FString& LearningType, const TSharedPtr<FJsonObject>& ModelConfig);
    
    /**
     * Cria configuração básica de comportamento adaptativo.
     */
    TSharedPtr<FJsonObject> CreateBasicBehaviorConfiguration(const FString& LayerName, const FString& BehaviorType, const TArray<TSharedPtr<FJsonValue>>& Rules);
    
    /**
     * Simula configuração de spawn dinâmico.
     */
    TSharedPtr<FJsonObject> SimulateSpawnSystemSetup(const FString& LayerName, const TSharedPtr<FJsonObject>& SpawnConfig);
    
    /**
     * Simula criação de árvore de decisão.
     */
    TSharedPtr<FJsonObject> SimulateDecisionTreeCreation(const FString& LayerName, const FString& TreeName, const TArray<TSharedPtr<FJsonValue>>& Nodes);
    
    /**
     * Simula configuração de eventos especiais.
     */
    TSharedPtr<FJsonObject> SimulateEventConfiguration(const FString& LayerName, const TSharedPtr<FJsonObject>& EventConfig);
    
    /**
     * Simula configuração de comunicação.
     */
    TSharedPtr<FJsonObject> SimulateCommunicationSetup(const FString& LayerName, float Range, const TArray<TSharedPtr<FJsonValue>>& MessageTypes);
    
    /**
     * Simula configuração de profiling.
     */
    TSharedPtr<FJsonObject> SimulateProfilingSetup(const FString& LayerName, const TArray<TSharedPtr<FJsonValue>>& Metrics);
    
    /**
     * Simula configuração de memória.
     */
    TSharedPtr<FJsonObject> SimulateMemorySystemSetup(const FString& LayerName, const TSharedPtr<FJsonObject>& MemoryConfig);
    
    /**
     * Atualiza métricas de performance.
     */
    void UpdateRealPerformanceMetrics(TSharedPtr<FJsonObject>& ResponseData);
    
    /**
     * Atualiza métricas de performance para otimização.
     */
    void UpdatePerformanceMetrics(const TSharedPtr<FJsonObject>& OptimizationData);
    
    /**
     * Salva estado do sistema.
     */
    void SaveSystemState(const FString& LayerName, const FString& SystemType, const TSharedPtr<FJsonObject>& State);
    
    // ============================================================================
    // Métodos Auxiliares para Validação e Métricas Reais
    // ============================================================================
    
    /**
     * Valida um teste de IA específico.
     */
    bool ValidateAITest(const FString& TestType, const FString& LayerName);
    
    /**
     * Calcula pontuação de um teste baseado no tipo.
     */
    float CalculateTestScore(const FString& TestType, bool bTestPassed);
    
    /**
     * Obtém uso real de CPU.
     */
    float GetRealCPUUsage();
    
    /**
     * Obtém uso real de memória.
     */
    float GetRealMemoryUsage();
    
    /**
     * Obtém tempo real de frame.
     */
    float GetRealFrameTime();
    
    /**
     * Obtém tempo real de processamento de IA.
     */
    float GetRealAIProcessingTime();

    // ============================================================================
    // Real Behavior Tree Implementation Functions
    // ============================================================================

    /**
     * Creates a real Behavior Tree using UE 5.6 APIs instead of simulation.
     */
    TSharedPtr<FJsonObject> CreateRealBehaviorTree(const FString& LayerName, const FString& TreeName, const TArray<TSharedPtr<FJsonValue>>& DecisionNodes, bool bLearningEnabled);

    // ============================================================================
    // Real Blackboard System Implementation Functions
    // ============================================================================

    /**
     * Gets real count of AI entities with active behavior trees.
     */
    int32 GetRealAIEntitiesCount();

    /**
     * Gets real learning progress from blackboard components.
     */
    float GetRealLearningProgress(const FString& LayerName);

    /**
     * Gets real AI performance score based on FPS and AI load.
     */
    float GetRealAIPerformanceScore();

    // ============================================================================
    // Real AI Perception System Implementation Functions
    // ============================================================================

    /**
     * Gets real AI adaptation rate based on perception activity.
     */
    float GetRealAIAdaptationRate(const FString& LayerName);

    /**
     * Gets real AI perception failure score based on perception performance.
     */
    float GetRealAIPerceptionFailureScore(const FString& TestName);

    /**
     * Gets real AI perception success score based on perception effectiveness.
     */
    float GetRealAIPerceptionSuccessScore(const FString& TestName);

    // ============================================================================
    // Real Navigation System Implementation Functions
    // ============================================================================

    /**
     * Gets real navigation path for an AI controller.
     */
    UNavigationPath* GetRealNavigationPath(AAIController* AIController);

    /**
     * Analyzes navigation system performance across all AI actors.
     */
    TSharedPtr<FJsonObject> AnalyzeNavigationPerformance();

    // ============================================================================
    // Real AI Learning System Implementation Functions
    // ============================================================================

    /**
     * Calculates real AI learning metrics based on behavior tree, perception, and navigation activity.
     */
    TSharedPtr<FJsonObject> CalculateRealAILearningMetrics();

    // ============================================================================
    // Real AI Performance Metrics Implementation Functions
    // ============================================================================

    /**
     * Collects real behavior tree execution statistics including timing, success rates, and node counts.
     */
    TSharedPtr<FJsonObject> CollectRealBehaviorTreeExecutionStats();

    /**
     * Counts the total number of nodes in a behavior tree recursively.
     */
    int32 CountBehaviorTreeNodes(UBTNode* Node);

    /**
     * Collects real pathfinding performance statistics including success rates and timing.
     */
    TSharedPtr<FJsonObject> CollectRealPathfindingPerformanceStats();

    /**
     * Collects real decision timing statistics across all AI decision-making systems.
     */
    TSharedPtr<FJsonObject> CollectRealDecisionTimingStats();

    // ============================================================================
    // Real AI Debugging System Implementation Functions
    // ============================================================================

    /**
     * Collects comprehensive AI debugging information for a specific layer.
     */
    TSharedPtr<FJsonObject> CollectRealAIDebugInformation(const FString& LayerName, bool bVisualizationEnabled);

    /**
     * Collects detailed behavior tree debugging information including execution states and node details.
     */
    TSharedPtr<FJsonObject> CollectBehaviorTreeDebugInfo();

    /**
     * Gets detailed debugging information for a specific behavior tree node.
     */
    TSharedPtr<FJsonObject> GetBehaviorTreeNodeDebugInfo(UBTNode* Node);

    /**
     * Collects detailed blackboard debugging information including key values and states.
     */
    TSharedPtr<FJsonObject> CollectBlackboardDebugInfo();

    /**
     * Collects detailed perception debugging information including sight and hearing targets.
     */
    TSharedPtr<FJsonObject> CollectPerceptionDebugInfo();

    /**
     * Collects detailed navigation debugging information including path analysis and movement states.
     */
    TSharedPtr<FJsonObject> CollectNavigationDebugInfo();
};