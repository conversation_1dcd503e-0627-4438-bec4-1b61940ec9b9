#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "Json.h"

/**
 * Analytics & Telemetry Commands for Unreal MCP
 * 
 * This class provides comprehensive analytics and telemetry integration including:
 * - GameAnalytics Integration
 * - Crashlytics System (Firebase Crashlytics)
 * - Custom Metrics System
 * - Hardware Telemetry Monitoring
 * - Retention Analysis & Cohort Analysis
 * - Conversion Tracking & Funnel Analysis
 * - Real-time Alerts System
 * - Performance Metrics Monitoring
 * - Analytics Dashboard Generation
 * - Data Pipeline Configuration
 * - Analytics Report Export
 * 
 * All functions are production-ready and based on UE 5.6 official documentation.
 */
class UNREALMCP_API UnrealMCPAnalyticsTelemetryCommands
{
public:
    // === GameAnalytics Integration ===
    
    /**
     * Setup GameAnalytics integration for comprehensive game analytics
     * @param JsonString JSON parameters containing GameAnalytics configuration
     * @return JSON response with setup status
     */
    static FString HandleSetupGameAnalyticsIntegration(const FString& JsonString);
    
    // === Crashlytics System ===
    
    /**
     * Configure Crashlytics system for crash reporting and performance monitoring
     * @param JsonString JSON parameters containing Crashlytics configuration
     * @return JSON response with configuration status
     */
    static FString HandleConfigureCrashlyticsSystem(const FString& JsonString);
    
    // === Custom Metrics System ===
    
    /**
     * Setup custom metrics system for detailed game analytics
     * @param JsonString JSON parameters containing custom metrics configuration
     * @return JSON response with setup status
     */
    static FString HandleSetupCustomMetricsSystem(const FString& JsonString);
    
    // === Hardware Telemetry ===
    
    /**
     * Configure hardware telemetry for performance monitoring
     * @param JsonString JSON parameters containing hardware telemetry configuration
     * @return JSON response with configuration status
     */
    static FString HandleConfigureHardwareTelemetry(const FString& JsonString);
    
    // === Retention Analysis ===
    
    /**
     * Analyze player retention metrics and patterns
     * @param JsonString JSON parameters containing retention analysis configuration
     * @return JSON response with analysis results
     */
    static FString HandleAnalyzeRetentionMetrics(const FString& JsonString);
    
    // === Conversion Tracking ===
    
    /**
     * Track conversion events and funnel analysis
     * @param JsonString JSON parameters containing conversion tracking configuration
     * @return JSON response with tracking status
     */
    static FString HandleTrackConversionEvents(const FString& JsonString);
    
    // === Real-time Alerts System ===
    
    /**
     * Setup real-time alerts system for critical metrics monitoring
     * @param JsonString JSON parameters containing alerts configuration
     * @return JSON response with setup status
     */
    static FString HandleSetupRealtimeAlertsSystem(const FString& JsonString);
    
    // === Performance Metrics Monitoring ===
    
    /**
     * Monitor comprehensive performance metrics in real-time
     * @param JsonString JSON parameters containing monitoring configuration
     * @return JSON response with monitoring status
     */
    static FString HandleMonitorPerformanceMetrics(const FString& JsonString);
    
    // === Analytics Dashboard ===
    
    /**
     * Generate comprehensive analytics dashboard
     * @param JsonString JSON parameters containing dashboard configuration
     * @return JSON response with dashboard data
     */
    static FString HandleGenerateAnalyticsDashboard(const FString& JsonString);
    
    // === Data Pipeline ===
    
    /**
     * Configure analytics data pipeline for automated data processing
     * @param JsonString JSON parameters containing pipeline configuration
     * @return JSON response with configuration status
     */
    static FString HandleConfigureDataPipeline(const FString& JsonString);
    
    // === Analytics Report Export ===
    
    /**
     * Export comprehensive analytics report
     * @param JsonString JSON parameters containing export configuration
     * @return JSON response with export status
     */
    static FString HandleExportAnalyticsReport(const FString& JsonString);

private:
    // === Helper Functions ===
    
    /**
     * Validate GameAnalytics configuration parameters
     * @param JsonObject JSON object containing GameAnalytics parameters
     * @return True if valid, false otherwise
     */
    static bool ValidateGameAnalyticsConfig(const TSharedPtr<FJsonObject>& JsonObject);
    
    /**
     * Validate Crashlytics configuration parameters
     * @param JsonObject JSON object containing Crashlytics parameters
     * @return True if valid, false otherwise
     */
    static bool ValidateCrashlyticsConfig(const TSharedPtr<FJsonObject>& JsonObject);
    
    /**
     * Validate custom metrics configuration
     * @param JsonObject JSON object containing metrics parameters
     * @return True if valid, false otherwise
     */
    static bool ValidateCustomMetricsConfig(const TSharedPtr<FJsonObject>& JsonObject);
    
    /**
     * Validate hardware telemetry configuration
     * @param JsonObject JSON object containing telemetry parameters
     * @return True if valid, false otherwise
     */
    static bool ValidateHardwareTelemetryConfig(const TSharedPtr<FJsonObject>& JsonObject);
    
    /**
     * Initialize GameAnalytics SDK
     * @param GameKey GameAnalytics game key
     * @param SecretKey GameAnalytics secret key
     * @param BuildVersion Game build version
     * @return True if successful
     */
    static bool InitializeGameAnalytics(const FString& GameKey, const FString& SecretKey, const FString& BuildVersion);
    
    /**
     * Configure GameAnalytics features
     * @param bDebugMode Enable debug mode
     * @param bAutomaticEvents Enable automatic events
     * @param CustomDimensions Custom dimensions array
     * @return True if successful
     */
    static bool ConfigureGameAnalyticsFeatures(bool bDebugMode, bool bAutomaticEvents, const TArray<FString>& CustomDimensions);
    
    /**
     * Setup Crashlytics reporting
     * @param ApiKey Crashlytics API key
     * @param bCrashReporting Enable crash reporting
     * @param bPerformanceMonitoring Enable performance monitoring
     * @return True if successful
     */
    static bool SetupCrashlyticsReporting(const FString& ApiKey, bool bCrashReporting, bool bPerformanceMonitoring);
    
    /**
     * Configure custom metrics endpoint
     * @param Endpoint Metrics endpoint URL
     * @param ApiKey API key for authentication
     * @param BatchSize Batch size for uploads
     * @return True if successful
     */
    static bool ConfigureCustomMetricsEndpoint(const FString& EndpointUrl, const FString& ApiKey, const FString& AuthMethod, int32 BatchSize, int32 FlushIntervalSeconds);
    
    /**
     * Setup hardware monitoring
     * @param bCPU Enable CPU monitoring
     * @param bGPU Enable GPU monitoring
     * @param bMemory Enable memory monitoring
     * @param bNetwork Enable network monitoring
     * @param SamplingInterval Sampling interval in seconds
     * @return True if successful
     */
    static bool SetupHardwareMonitoring(bool bCPU, bool bGPU, bool bMemory, bool bNetwork, int32 SamplingInterval);
    
    /**
     * Analyze retention data
     * @param TimePeriod Time period for analysis
     * @param bCohortAnalysis Enable cohort analysis
     * @param bSegmentByPlatform Segment by platform
     * @param bSegmentByRegion Segment by region
     * @return Retention analysis results
     */
    static TSharedPtr<FJsonObject> AnalyzeRetentionData(const FString& TimePeriod, bool bCohortAnalysis, 
                                                       bool bSegmentByPlatform, bool bSegmentByRegion);
    
    /**
     * Track conversion event
     * @param EventName Event name
     * @param ConversionFunnel Funnel steps
     * @param Value Monetary value
     * @param Currency Currency code
     * @param CustomParameters Custom parameters
     * @return True if successful
     */
    static bool TrackConversionEvent(const FString& EventName, const TArray<FString>& ConversionFunnel, 
                                   float Value, const FString& Currency, const TSharedPtr<FJsonObject>& CustomParameters);
    
    /**
     * Setup alert thresholds
     * @param AlertTypes Types of alerts
     * @param ThresholdConfigs Threshold configurations
     * @param NotificationChannels Notification channels
     * @return True if successful
     */
    static bool SetupAlertThresholds(const TArray<FString>& AlertTypes, const TSharedPtr<FJsonObject>& ThresholdConfigs, 
                                   const TArray<FString>& NotificationChannels);
    
    /**
     * Monitor performance metrics
     * @param MetricTypes Types of metrics to monitor
     * @param MonitoringDurationSeconds Duration of monitoring in seconds
     * @param SamplingIntervalMs Sampling interval in milliseconds
     * @param bIncludeFrameTime Include frame time metrics
     * @param bIncludeMemoryUsage Include memory usage metrics
     * @param bIncludeRenderStats Include render statistics
     * @param bIncludeNetworkStats Include network statistics
     * @return Performance metrics data
     */
    static TSharedPtr<FJsonObject> MonitorPerformanceMetrics(const TArray<FString>& MetricTypes, int32 MonitoringDurationSeconds, 
                                                            int32 SamplingIntervalMs, bool bIncludeFrameTime, 
                                                            bool bIncludeMemoryUsage, bool bIncludeRenderStats, 
                                                            bool bIncludeNetworkStats);
    
    /**
     * Generate dashboard data
     * @param DashboardType Type of dashboard
     * @param TimePeriod Time period for data
     * @param WidgetTypes Types of widgets to include
     * @param bIncludeRealTimeData Include real-time data
     * @param bIncludeHistoricalData Include historical data
     * @param bEnableInteractivity Enable interactive features
     * @return Dashboard data object
     */
    static TSharedPtr<FJsonObject> GenerateDashboardData(const FString& DashboardType, const FString& TimePeriod, 
                                                        const TArray<FString>& WidgetTypes, bool bIncludeRealTimeData, 
                                                        bool bIncludeHistoricalData, bool bEnableInteractivity);
    
    /**
     * Configure data pipeline
     * @param PipelineType Type of pipeline
     * @param DataSources Data sources
     * @param DataDestinations Data destinations
     * @param TransformationRules Transformation rules
     * @param BatchSize Batch size for processing
     * @param ProcessingIntervalSeconds Processing interval in seconds
     * @param bEnableRealTimeProcessing Enable real-time processing
     * @param bEnableDataValidation Enable data validation
     * @return True if successful
     */
    static bool ConfigureDataPipeline(const FString& PipelineType, const TArray<FString>& DataSources, 
                                     const TArray<FString>& DataDestinations, const TSharedPtr<FJsonObject>& TransformationRules, 
                                     int32 BatchSize, int32 ProcessingIntervalSeconds, bool bEnableRealTimeProcessing, 
                                     bool bEnableDataValidation);
    
    /**
     * Export analytics report
     * @param ReportType Type of report
     * @param ExportFormat Export format
     * @param TimePeriod Time period for report
     * @param OutputPath Output path for report
     * @param ReportSections Sections to include in report
     * @param bIncludeCharts Include charts in report
     * @param bIncludeRawData Include raw data in report
     * @param bCompressOutput Compress output file
     * @return Path to exported report
     */
    static FString ExportAnalyticsReport(const FString& ReportType, const FString& ExportFormat, 
                                        const FString& TimePeriod, const FString& OutputPath, 
                                        const TArray<FString>& ReportSections, bool bIncludeCharts, 
                                        bool bIncludeRawData, bool bCompressOutput);
    
    /**
     * Create JSON response with success status
     * @param bSuccess Success status
     * @param Message Response message
     * @param Data Optional data object
     * @return JSON response string
     */
    static FString CreateJsonResponse(bool bSuccess, const FString& Message, const TSharedPtr<FJsonObject>& Data = nullptr);
    
    /**
     * Log analytics operation
     * @param Operation Operation name
     * @param bSuccess Success status
     * @param Message Log message
     */
    static void LogAnalyticsOperation(const FString& Operation, bool bSuccess, const FString& Message);

    // === Constants ===
    
    // GameAnalytics constants
    static const FString GAMEANALYTICS_CONFIG_SECTION;
    static const FString GAMEANALYTICS_SDK_VERSION;
    
    // Crashlytics constants
    static const FString CRASHLYTICS_CONFIG_SECTION;
    static const FString CRASHLYTICS_API_VERSION;
    
    // Custom metrics constants
    static const FString CUSTOM_METRICS_CONFIG_SECTION;
    static const FString DEFAULT_METRICS_ENDPOINT;
    
    // Hardware telemetry constants
    static const FString TELEMETRY_CONFIG_SECTION;
    static const int32 DEFAULT_SAMPLING_INTERVAL;
    
    // Alert types
    static const FString ALERT_TYPE_CRASH_RATE;
    static const FString ALERT_TYPE_PERFORMANCE_DEGRADATION;
    static const FString ALERT_TYPE_USER_DROP;
    static const FString ALERT_TYPE_ERROR_SPIKE;
    
    // Dashboard types
    static const FString DASHBOARD_TYPE_SUMMARY;
    static const FString DASHBOARD_TYPE_DETAILED;
    static const FString DASHBOARD_TYPE_COMPREHENSIVE;
    static const FString DASHBOARD_TYPE_CUSTOM;
    
    // Export formats
    static const FString EXPORT_FORMAT_PDF;
    static const FString EXPORT_FORMAT_HTML;
    static const FString EXPORT_FORMAT_XLSX;
    static const FString EXPORT_FORMAT_JSON;

    // ============================================================================
    // Real Analytics and Telemetry System Functions using UE 5.6 APIs
    // ============================================================================

    /**
     * Initializes the real analytics system using UE 5.6 IAnalyticsProvider APIs.
     */
    static TSharedPtr<FJsonObject> InitializeRealAnalyticsSystem();

    /**
     * Records real performance metrics using UE 5.6 analytics APIs.
     */
    static TSharedPtr<FJsonObject> RecordRealPerformanceMetrics();

    /**
     * Records a custom event with real data using UE 5.6 analytics APIs.
     */
    static TSharedPtr<FJsonObject> RecordRealCustomEvent(const FString& EventName, const TMap<FString, FString>& EventData);

    /**
     * Collects comprehensive system telemetry using real UE 5.6 APIs.
     */
    static TSharedPtr<FJsonObject> CollectRealSystemTelemetry();
};
