"""
Networking Tools for Unreal Engine MCP Server

This module provides comprehensive Networking tools that are 100% compatible with the 
C++ implementations in UnrealMCPNetworkingCommands.cpp. Based on UE 5.6 official documentation.
"""

import logging
import json
from typing import Dict, List, Any, Optional
from mcp.server.fastmcp import FastMCP

# Set up logging
logger = logging.getLogger("UnrealMCP")

def register_networking_tools(mcp: FastMCP):
    """Register Networking tools with the MCP server - 100% compatible with C++ implementations."""

    @mcp.tool()
    def create_multiplayer_session(
        session_name: str,
        max_players: int = 4,
        is_lan: bool = False,
        is_dedicated: bool = False,
        allow_join_in_progress: bool = True,
        use_presence: bool = True
    ) -> Dict[str, Any]:
        """
        Create a new multiplayer session.
        Compatible with HandleCreateMultiplayerSession in C++.
        
        Args:
            session_name: Name for the multiplayer session
            max_players: Maximum number of players allowed
            is_lan: Whether this is a LAN session
            is_dedicated: Whether this is a dedicated server session
            allow_join_in_progress: Allow players to join in progress
            use_presence: Use presence for session discovery
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "session_name": session_name,
                "max_players": max_players,
                "is_lan": is_lan,
                "is_dedicated": is_dedicated,
                "allow_join_in_progress": allow_join_in_progress,
                "use_presence": use_presence
            }

            response = unreal.send_command("create_multiplayer_session", params)
            return response or {"success": True, "message": "Multiplayer session created successfully"}

        except Exception as e:
            logger.error(f"Error creating multiplayer session: {e}")
            return {"success": False, "message": f"Error creating multiplayer session: {e}"}

    @mcp.tool()
    def setup_actor_replication(
        actor_class_path: str,
        replicate_movement: bool = True,
        replicate_properties: List[str] = None,
        net_cull_distance: float = 15000.0,
        net_update_frequency: float = 100.0
    ) -> Dict[str, Any]:
        """
        Setup actor replication for multiplayer.
        Compatible with HandleSetupActorReplication in C++.
        
        Args:
            actor_class_path: Path to the actor class
            replicate_movement: Enable movement replication
            replicate_properties: List of properties to replicate
            net_cull_distance: Network culling distance
            net_update_frequency: Network update frequency
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "actor_class_path": actor_class_path,
                "replicate_movement": replicate_movement,
                "replicate_properties": replicate_properties or [],
                "net_cull_distance": net_cull_distance,
                "net_update_frequency": net_update_frequency
            }

            response = unreal.send_command("setup_actor_replication", params)
            return response or {"success": True, "message": "Actor replication setup successfully"}

        except Exception as e:
            logger.error(f"Error setting up actor replication: {e}")
            return {"success": False, "message": f"Error setting up actor replication: {e}"}

    @mcp.tool()
    def create_rpc_function(
        function_name: str,
        rpc_type: str = "Server",
        is_reliable: bool = True,
        parameters: List[Dict[str, str]] = None,
        target_class_path: str = ""
    ) -> Dict[str, Any]:
        """
        Create a Remote Procedure Call (RPC) function.
        Compatible with HandleCreateRPCFunction in C++.
        
        Args:
            function_name: Name of the RPC function
            rpc_type: Type of RPC (Server, Client, NetMulticast)
            is_reliable: Whether the RPC is reliable
            parameters: List of parameters with type and name
            target_class_path: Path to the target class
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "function_name": function_name,
                "rpc_type": rpc_type,
                "is_reliable": is_reliable,
                "parameters": parameters or [],
                "target_class_path": target_class_path
            }

            response = unreal.send_command("create_rpc_function", params)
            return response or {"success": True, "message": "RPC function created successfully"}

        except Exception as e:
            logger.error(f"Error creating RPC function: {e}")
            return {"success": False, "message": f"Error creating RPC function: {e}"}

    @mcp.tool()
    def configure_network_settings(
        tick_rate: int = 60,
        max_internet_client_rate: int = 25000,
        max_client_rate: int = 25000,
        enable_network_profiler: bool = False,
        network_emulation_settings: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Configure global network settings.
        Compatible with HandleConfigureNetworkSettings in C++.
        
        Args:
            tick_rate: Server tick rate
            max_internet_client_rate: Maximum internet client rate
            max_client_rate: Maximum client rate
            enable_network_profiler: Enable network profiler
            network_emulation_settings: Network emulation settings
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "tick_rate": tick_rate,
                "max_internet_client_rate": max_internet_client_rate,
                "max_client_rate": max_client_rate,
                "enable_network_profiler": enable_network_profiler,
                "network_emulation_settings": network_emulation_settings or {}
            }

            response = unreal.send_command("configure_network_settings", params)
            return response or {"success": True, "message": "Network settings configured successfully"}

        except Exception as e:
            logger.error(f"Error configuring network settings: {e}")
            return {"success": False, "message": f"Error configuring network settings: {e}"}

    @mcp.tool()
    def setup_game_mode_networking(
        game_mode_class_path: str,
        player_controller_class_path: str = "",
        pawn_class_path: str = "",
        spectator_class_path: str = "",
        enable_seamless_travel: bool = True
    ) -> Dict[str, Any]:
        """
        Setup Game Mode for networking.
        Compatible with HandleSetupGameModeNetworking in C++.
        
        Args:
            game_mode_class_path: Path to the Game Mode class
            player_controller_class_path: Path to the Player Controller class
            pawn_class_path: Path to the Pawn class
            spectator_class_path: Path to the Spectator class
            enable_seamless_travel: Enable seamless travel
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "game_mode_class_path": game_mode_class_path,
                "player_controller_class_path": player_controller_class_path,
                "pawn_class_path": pawn_class_path,
                "spectator_class_path": spectator_class_path,
                "enable_seamless_travel": enable_seamless_travel
            }

            response = unreal.send_command("setup_game_mode_networking", params)
            return response or {"success": True, "message": "Game Mode networking setup successfully"}

        except Exception as e:
            logger.error(f"Error setting up Game Mode networking: {e}")
            return {"success": False, "message": f"Error setting up Game Mode networking: {e}"}

    @mcp.tool()
    def manage_player_connections(
        action: str = "list",
        player_id: str = "",
        kick_reason: str = "",
        ban_duration: int = 0
    ) -> Dict[str, Any]:
        """
        Manage player connections (list, kick, ban).
        Compatible with HandleManagePlayerConnections in C++.
        
        Args:
            action: Action to perform (list, kick, ban, unban)
            player_id: Player ID for kick/ban actions
            kick_reason: Reason for kicking player
            ban_duration: Ban duration in minutes (0 = permanent)
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "action": action,
                "player_id": player_id,
                "kick_reason": kick_reason,
                "ban_duration": ban_duration
            }

            response = unreal.send_command("manage_player_connections", params)
            return response or {"success": True, "message": f"Player connection {action} completed successfully"}

        except Exception as e:
            logger.error(f"Error managing player connections: {e}")
            return {"success": False, "message": f"Error managing player connections: {e}"}

    @mcp.tool()
    def configure_replication_graph(
        enable_replication_graph: bool = True,
        spatial_bias: float = 1.0,
        connection_max_actors_per_frame: int = 100,
        enable_iris_replication: bool = False,
        custom_node_classes: List[str] = None
    ) -> Dict[str, Any]:
        """
        Configure Replication Graph for optimized networking.
        Compatible with HandleConfigureReplicationGraph in C++.

        Args:
            enable_replication_graph: Enable Replication Graph system
            spatial_bias: Spatial bias for relevancy calculations
            connection_max_actors_per_frame: Max actors per frame per connection
            enable_iris_replication: Enable Iris replication system
            custom_node_classes: List of custom replication node classes
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "enable_replication_graph": enable_replication_graph,
                "spatial_bias": spatial_bias,
                "connection_max_actors_per_frame": connection_max_actors_per_frame,
                "enable_iris_replication": enable_iris_replication,
                "custom_node_classes": custom_node_classes or []
            }

            response = unreal.send_command("configure_replication_graph", params)
            return response or {"success": True, "message": "Replication Graph configured successfully"}

        except Exception as e:
            logger.error(f"Error configuring Replication Graph: {e}")
            return {"success": False, "message": f"Error configuring Replication Graph: {e}"}

    @mcp.tool()
    def setup_network_debugging(
        enable_network_logging: bool = True,
        log_level: str = "Warning",
        enable_packet_loss_simulation: bool = False,
        packet_loss_percentage: float = 0.0,
        latency_simulation_ms: int = 0
    ) -> Dict[str, Any]:
        """
        Setup network debugging and simulation tools.
        Compatible with HandleSetupNetworkDebugging in C++.

        Args:
            enable_network_logging: Enable network logging
            log_level: Logging level (Verbose, Log, Warning, Error)
            enable_packet_loss_simulation: Enable packet loss simulation
            packet_loss_percentage: Packet loss percentage (0.0-100.0)
            latency_simulation_ms: Latency simulation in milliseconds
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "enable_network_logging": enable_network_logging,
                "log_level": log_level,
                "enable_packet_loss_simulation": enable_packet_loss_simulation,
                "packet_loss_percentage": packet_loss_percentage,
                "latency_simulation_ms": latency_simulation_ms
            }

            response = unreal.send_command("setup_network_debugging", params)
            return response or {"success": True, "message": "Network debugging setup successfully"}

        except Exception as e:
            logger.error(f"Error setting up network debugging: {e}")
            return {"success": False, "message": f"Error setting up network debugging: {e}"}

    @mcp.tool()
    def analyze_network_performance(
        analysis_duration: float = 60.0,
        include_bandwidth_stats: bool = True,
        include_latency_stats: bool = True,
        include_packet_loss_stats: bool = True,
        generate_report: bool = True
    ) -> Dict[str, Any]:
        """
        Analyze network performance and generate metrics.
        Compatible with HandleAnalyzeNetworkPerformance in C++.

        Args:
            analysis_duration: Duration of analysis in seconds
            include_bandwidth_stats: Include bandwidth statistics
            include_latency_stats: Include latency statistics
            include_packet_loss_stats: Include packet loss statistics
            generate_report: Generate detailed performance report
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "analysis_duration": analysis_duration,
                "include_bandwidth_stats": include_bandwidth_stats,
                "include_latency_stats": include_latency_stats,
                "include_packet_loss_stats": include_packet_loss_stats,
                "generate_report": generate_report
            }

            response = unreal.send_command("analyze_network_performance", params)
            return response or {"success": True, "message": "Network performance analysis completed successfully"}

        except Exception as e:
            logger.error(f"Error analyzing network performance: {e}")
            return {"success": False, "message": f"Error analyzing network performance: {e}"}
