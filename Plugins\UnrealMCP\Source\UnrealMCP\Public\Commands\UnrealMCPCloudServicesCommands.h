#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "Json.h"

/**
 * Cloud Services Integration Commands for Unreal MCP
 * 
 * This class provides comprehensive cloud services integration including:
 * - AWS (Amazon Web Services) Integration
 * - Microsoft Azure Services
 * - Google Cloud Platform (GCP)
 * - Firebase Integration
 * - PlayFab Services
 * - Epic Online Services (EOS)
 * - Steam Integration (Steamworks)
 * - Dedicated Servers Configuration
 * - Matchmaking Services
 * - Leaderboards System
 * - Cloud Storage System
 * - CDN (Content Delivery Network) Distribution
 * 
 * All functions are production-ready and based on UE 5.6 official documentation.
 */
class UNREALMCP_API UnrealMCPCloudServicesCommands
{
public:
    // === AWS Integration ===
    
    /**
     * Setup AWS (Amazon Web Services) integration for cloud services
     * @param JsonString JSON parameters containing AWS configuration
     * @return JSON response with setup status
     */
    static FString HandleSetupAWSIntegration(const FString& JsonString);
    
    // === Azure Services ===
    
    /**
     * Configure Microsoft Azure cloud services integration
     * @param JsonString JSON parameters containing Azure configuration
     * @return JSON response with configuration status
     */
    static FString HandleConfigureAzureServices(const FString& JsonString);
    
    // === Google Cloud Platform ===
    
    /**
     * Setup Google Cloud Platform (GCP) integration
     * @param JsonString JSON parameters containing GCP configuration
     * @return JSON response with setup status
     */
    static FString HandleSetupGoogleCloudPlatform(const FString& JsonString);
    
    // === Firebase Integration ===
    
    /**
     * Configure Firebase integration for mobile and web services
     * @param JsonString JSON parameters containing Firebase configuration
     * @return JSON response with configuration status
     */
    static FString HandleConfigureFirebaseIntegration(const FString& JsonString);
    
    // === PlayFab Services ===
    
    /**
     * Setup PlayFab services for game backend functionality
     * @param JsonString JSON parameters containing PlayFab configuration
     * @return JSON response with setup status
     */
    static FString HandleSetupPlayFabServices(const FString& JsonString);
    
    // === Epic Online Services ===
    
    /**
     * Configure Epic Online Services (EOS) integration
     * @param JsonString JSON parameters containing EOS configuration
     * @return JSON response with configuration status
     */
    static FString HandleConfigureEpicOnlineServices(const FString& JsonString);
    
    // === Steam Integration ===
    
    /**
     * Setup Steam integration for Steamworks features
     * @param JsonString JSON parameters containing Steam configuration
     * @return JSON response with setup status
     */
    static FString HandleSetupSteamIntegration(const FString& JsonString);
    
    // === Dedicated Servers ===
    
    /**
     * Configure dedicated servers for multiplayer games
     * @param JsonString JSON parameters containing server configuration
     * @return JSON response with configuration status
     */
    static FString HandleConfigureDedicatedServers(const FString& JsonString);
    
    // === Matchmaking Service ===
    
    /**
     * Setup matchmaking service for multiplayer games
     * @param JsonString JSON parameters containing matchmaking configuration
     * @return JSON response with setup status
     */
    static FString HandleSetupMatchmakingService(const FString& JsonString);
    
    // === Leaderboards System ===
    
    /**
     * Configure leaderboards system for competitive features
     * @param JsonString JSON parameters containing leaderboards configuration
     * @return JSON response with configuration status
     */
    static FString HandleConfigureLeaderboardsSystem(const FString& JsonString);
    
    // === Cloud Storage System ===
    
    /**
     * Setup cloud storage system for game assets and user data
     * @param JsonString JSON parameters containing storage configuration
     * @return JSON response with setup status
     */
    static FString HandleSetupCloudStorageSystem(const FString& JsonString);
    
    // === CDN Distribution ===
    
    /**
     * Configure CDN (Content Delivery Network) for fast content distribution
     * @param JsonString JSON parameters containing CDN configuration
     * @return JSON response with configuration status
     */
    static FString HandleConfigureCDNDistribution(const FString& JsonString);

private:
    // === Helper Functions ===
    
    /**
     * Validate AWS configuration parameters
     * @param JsonObject JSON object containing AWS parameters
     * @return True if valid, false otherwise
     */
    static bool ValidateAWSConfig(const TSharedPtr<FJsonObject>& JsonObject);
    
    /**
     * Validate Azure configuration parameters
     * @param JsonObject JSON object containing Azure parameters
     * @return True if valid, false otherwise
     */
    static bool ValidateAzureConfig(const TSharedPtr<FJsonObject>& JsonObject);
    
    /**
     * Validate Google Cloud Platform configuration
     * @param JsonObject JSON object containing GCP parameters
     * @return True if valid, false otherwise
     */
    static bool ValidateGCPConfig(const TSharedPtr<FJsonObject>& JsonObject);
    
    /**
     * Validate Firebase configuration parameters
     * @param JsonObject JSON object containing Firebase parameters
     * @return True if valid, false otherwise
     */
    static bool ValidateFirebaseConfig(const TSharedPtr<FJsonObject>& JsonObject);
    
    /**
     * Initialize AWS SDK
     * @param AccessKeyId AWS access key ID
     * @param SecretAccessKey AWS secret access key
     * @param Region AWS region
     * @return True if successful
     */
    static bool InitializeAWS(const FString& AccessKeyId, const FString& SecretAccessKey, const FString& Region);
    
    /**
     * Configure AWS services
     * @param Services List of services to enable
     * @param bS3 Enable S3
     * @param bDynamoDB Enable DynamoDB
     * @param bLambda Enable Lambda
     * @return True if successful
     */
    static bool ConfigureAWSServices(const TArray<FString>& Services, bool bS3, bool bDynamoDB, bool bLambda);
    
    /**
     * Setup Azure authentication
     * @param SubscriptionId Azure subscription ID
     * @param TenantId Azure tenant ID
     * @param ClientId Azure client ID
     * @param ClientSecret Azure client secret
     * @return True if successful
     */
    static bool SetupAzureAuth(const FString& SubscriptionId, const FString& TenantId, 
                              const FString& ClientId, const FString& ClientSecret);
    
    /**
     * Configure Google Cloud authentication
     * @param ProjectId GCP project ID
     * @param ServiceAccountKey Service account key JSON
     * @param Region GCP region
     * @return True if successful
     */
    static bool ConfigureGCPAuth(const FString& ProjectId, const FString& ServiceAccountKey, const FString& Region);
    
    /**
     * Setup Firebase configuration
     * @param ProjectId Firebase project ID
     * @param ApiKey Firebase API key
     * @param AppId Firebase app ID
     * @return True if successful
     */
    static bool SetupFirebaseConfig(const FString& ProjectId, const FString& ApiKey, const FString& AppId);
    
    /**
     * Initialize PlayFab SDK
     * @param TitleId PlayFab title ID
     * @param SecretKey PlayFab secret key
     * @return True if successful
     */
    static bool InitializePlayFab(const FString& TitleId, const FString& SecretKey);
    
    /**
     * Configure EOS settings
     * @param ProductId EOS product ID
     * @param SandboxId EOS sandbox ID
     * @param DeploymentId EOS deployment ID
     * @param ClientId EOS client ID
     * @param ClientSecret EOS client secret
     * @return True if successful
     */
    static bool ConfigureEOSSettings(const FString& ProductId, const FString& SandboxId, const FString& DeploymentId,
                                   const FString& ClientId, const FString& ClientSecret);
    
    /**
     * Setup Steam integration
     * @param AppId Steam app ID
     * @param ApiKey Steam Web API key
     * @return True if successful
     */
    static bool SetupSteamIntegration(const FString& AppId, const FString& ApiKey);
    
    /**
     * Configure dedicated server settings
     * @param ServerType Type of server
     * @param InstanceType Server instance type
     * @param Region Server region
     * @param MaxPlayers Maximum players per server
     * @return True if successful
     */
    static bool ConfigureDedicatedServerSettings(const FString& ServerType, const FString& InstanceType, 
                                                const FString& Region, int32 MaxPlayers);
    
    /**
     * Setup matchmaking configuration
     * @param ServiceProvider Matchmaking service provider
     * @param MaxPlayersPerMatch Maximum players per match
     * @param bSkillBasedMatching Enable skill-based matching
     * @param bRegionBasedMatching Enable region-based matching
     * @return True if successful
     */
    static bool SetupMatchmakingConfig(const FString& ServiceProvider, int32 MaxPlayersPerMatch, 
                                     bool bSkillBasedMatching, bool bRegionBasedMatching);
    
    /**
     * Configure Firebase settings
     * @param ProjectId Firebase project ID
     * @param ApiKey Firebase API key
     * @param DatabaseUrl Firebase database URL
     * @param StorageBucket Firebase storage bucket
     * @return True if successful
     */
    static bool ConfigureFirebaseSettings(const FString& ProjectId, const FString& ApiKey, const FString& DatabaseUrl, const FString& StorageBucket);
    
    /**
     * Setup PlayFab configuration
     * @param TitleId PlayFab title ID
     * @param SecretKey PlayFab secret key
     * @param DeveloperKey PlayFab developer key
     * @return True if successful
     */
    static bool SetupPlayFabConfig(const FString& TitleId, const FString& SecretKey, const FString& DeveloperKey);
    
    /**
     * Setup Steam configuration
     * @param AppId Steam app ID
     * @param ApiKey Steam API key
     * @return True if successful
     */
    static bool SetupSteamConfig(const FString& AppId, const FString& ApiKey);
    
    /**
     * Configure leaderboards settings
     * @param Provider Leaderboards provider
     * @param bGlobalLeaderboards Enable global leaderboards
     * @param bFriendLeaderboards Enable friend leaderboards
     * @param ResetSchedule Reset schedule
     * @return True if successful
     */
    static bool ConfigureLeaderboardsSettings(const FString& Provider, bool bGlobalLeaderboards, 
                                             bool bFriendLeaderboards, const FString& ResetSchedule);
    
    /**
     * Setup cloud storage configuration
     * @param Provider Storage provider
     * @param BucketName Storage bucket name
     * @param bCDN Enable CDN
     * @param bVersioning Enable versioning
     * @param bEncryption Enable encryption
     * @return True if successful
     */
    static bool SetupCloudStorageConfig(const FString& Provider, const FString& BucketName, 
                                      bool bCDN, bool bVersioning, bool bEncryption);
    
    /**
     * Configure CDN settings
     * @param CDNProvider CDN provider name
     * @param DistributionId Distribution ID
     * @param OriginDomain Origin domain
     * @param CacheBehavior Cache behavior settings
     * @return True if successful
     */
    static bool ConfigureCDNSettings(const FString& CDNProvider, const FString& DistributionId, 
                                   const FString& OriginDomain, const FString& CacheBehavior);
    
    /**
     * Create JSON response with success status
     * @param bSuccess Success status
     * @param Message Response message
     * @param Data Optional data object
     * @return JSON response string
     */
    static FString CreateJsonResponse(bool bSuccess, const FString& Message, const TSharedPtr<FJsonObject>& Data = nullptr);
    
    /**
     * Log cloud services operation
     * @param Operation Operation name
     * @param bSuccess Success status
     * @param Message Log message
     */
    static void LogCloudServicesOperation(const FString& Operation, bool bSuccess, const FString& Message);

    // === Constants ===
    
    // AWS constants
    static const FString AWS_CONFIG_SECTION;
    static const FString AWS_DEFAULT_REGION;
    
    // Azure constants
    static const FString AZURE_CONFIG_SECTION;
    static const FString AZURE_DEFAULT_REGION;
    
    // GCP constants
    static const FString GCP_CONFIG_SECTION;
    static const FString GCP_DEFAULT_REGION;
    
    // Firebase constants
    static const FString FIREBASE_CONFIG_SECTION;
    static const FString FIREBASE_SDK_VERSION;
    
    // PlayFab constants
    static const FString PLAYFAB_CONFIG_SECTION;
    static const FString PLAYFAB_SDK_VERSION;
    
    // EOS constants
    static const FString EOS_CONFIG_SECTION;
    static const FString EOS_SDK_VERSION;
    
    // Steam constants
    static const FString STEAM_CONFIG_SECTION;
    static const FString STEAMWORKS_SDK_VERSION;
    
    // Dedicated Servers constants
    static const FString DEDICATED_SERVER_CONFIG_SECTION;
    
    // Matchmaking constants
    static const FString MATCHMAKING_CONFIG_SECTION;
    
    // Leaderboards constants
    static const FString LEADERBOARDS_CONFIG_SECTION;
    
    // Cloud Storage constants
    static const FString CLOUD_STORAGE_CONFIG_SECTION;
    
    // CDN constants
    static const FString CDN_CONFIG_SECTION;
    
    // Server types
    static const FString SERVER_TYPE_AWS_EC2;
    static const FString SERVER_TYPE_AZURE_VM;
    static const FString SERVER_TYPE_GCP_COMPUTE;
    static const FString SERVER_TYPE_CUSTOM;
    
    // Matchmaking providers
    static const FString MATCHMAKING_PROVIDER_EOS;
    static const FString MATCHMAKING_PROVIDER_STEAM;
    static const FString MATCHMAKING_PROVIDER_PLAYFAB;
    static const FString MATCHMAKING_PROVIDER_CUSTOM;
    
    // Storage providers
    static const FString STORAGE_PROVIDER_AWS_S3;
    static const FString STORAGE_PROVIDER_AZURE_BLOB;
    static const FString STORAGE_PROVIDER_GCP_STORAGE;
    static const FString STORAGE_PROVIDER_FIREBASE_STORAGE;
};
