// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "Sound/SoundWave.h"
#include "Sound/SoundCue.h"
#include "Sound/SoundAttenuation.h"
#include "Sound/SoundSubmix.h"
#include "Sound/AmbientSound.h"
#include "Sound/AudioVolume.h"
#include "Components/AudioComponent.h"
#include "AudioDevice.h"
#include "AudioMixerDevice.h"
#include "MetasoundSource.h"
#include "MetasoundGenerator.h"
#include "MetasoundParameterTransmitter.h"
#include "Engine/World.h"
#include "UObject/ConstructorHelpers.h"
#include "Kismet/GameplayStatics.h"
#include "AudioMixerBlueprintLibrary.h"
#include "Subsystems/AudioEngineSubsystem.h"
#include "DSP/Dsp.h"
#include "AudioAnalytics.h"

/**
 * UnrealMCPAudioSystemCommands
 * 
 * Handles all Audio System related commands for the MCP server.
 * Provides comprehensive audio creation, configuration, and management tools
 * compatible with Unreal Engine 5.6 audio systems including MetaSounds.
 * 
 * Features:
 * - MetaSound source creation and management
 * - Sound Cue creation and configuration
 * - 3D audio spatialization setup
 * - Audio submix routing and effects
 * - Ambient sound actor management
 * - Audio volume configuration
 * - Procedural audio generation
 * - Audio occlusion and obstruction
 * - Audio streaming optimization
 * - Performance analysis and profiling
 * - Cross-platform audio support
 */
class UNREALMCP_API UnrealMCPAudioSystemCommands
{
public:
    UnrealMCPAudioSystemCommands();
    ~UnrealMCPAudioSystemCommands();

    // Core Audio Asset Creation
    static FString HandleCreateMetaSoundSource(const FString& JsonParams);
    static FString HandleCreateSoundCue(const FString& JsonParams);
    static FString HandleConfigureAudioSpatialization(const FString& JsonParams);
    static FString HandleSetupAudioSubmix(const FString& JsonParams);

    // Audio Actor and Volume Management
    static FString HandleCreateAmbientSoundActor(const FString& JsonParams);
    static FString HandleConfigureAudioVolume(const FString& JsonParams);

    // Advanced Audio Features
    static FString HandleSetupProceduralAudio(const FString& JsonParams);
    static FString HandleConfigureAudioOcclusion(const FString& JsonParams);
    static FString HandleManageAudioStreaming(const FString& JsonParams);
    static FString HandleAnalyzeAudioPerformance(const FString& JsonParams);

private:
    // Helper Functions for MetaSound Creation
    static UMetaSoundSource* CreateMetaSoundAsset(const FString& AssetName, const FString& PackagePath);
    static bool ConfigureMetaSoundOutput(UMetaSoundSource* MetaSound, const FString& OutputFormat, 
        int32 SampleRate, bool bEnableSpatialization);
    static bool SetMetaSoundAutoPlay(UMetaSoundSource* MetaSound, bool bAutoPlay);
    static bool AddMetaSoundParameters(UMetaSoundSource* MetaSound, const TMap<FString, float>& Parameters);
    
    // Helper Functions for Sound Cue Creation
    static USoundCue* CreateSoundCueAsset(const FString& AssetName, const FString& PackagePath);
    static bool AddSoundWavesToCue(USoundCue* SoundCue, const TArray<FString>& SoundWavePaths);
    static bool ConfigureSoundCueRandomization(USoundCue* SoundCue, bool bEnableRandomization);
    static bool ConfigureSoundCueLooping(USoundCue* SoundCue, bool bEnableLooping);
    static bool SetSoundCueVolumeAndPitch(USoundCue* SoundCue, float VolumeMultiplier, float PitchMultiplier);
    
    // Helper Functions for Audio Spatialization
    static USoundAttenuation* CreateSoundAttenuationAsset(const FString& AssetName, const FString& PackagePath);
    static bool ConfigureAttenuationShape(USoundAttenuation* Attenuation, const FString& Shape, 
        float FalloffDistance, float MaxDistance);
    static bool ConfigureSpatialization(USoundAttenuation* Attenuation, const FString& Method);
    static bool EnableAudioOcclusion(USoundAttenuation* Attenuation, bool bEnable);
    static bool ApplyAttenuationToAudioAsset(const FString& AudioAssetPath, USoundAttenuation* Attenuation);
    
    // Helper Functions for Audio Submix
    static USoundSubmix* CreateAudioSubmixAsset(const FString& AssetName, const FString& PackagePath);
    static bool SetSubmixParent(USoundSubmix* Submix, const FString& ParentSubmixName);
    static bool ConfigureSubmixReverb(USoundSubmix* Submix, bool bEnable, const FString& ReverbType);
    static bool ConfigureSubmixEQ(USoundSubmix* Submix, bool bEnable);
    static bool ConfigureSubmixDynamics(USoundSubmix* Submix, bool bEnable);
    
    // Helper Functions for Ambient Sound Actor
    static AAmbientSound* CreateAmbientSoundInLevel(UWorld* World, const FString& ActorName);
    static bool ConfigureAmbientSoundAsset(AAmbientSound* AmbientSound, const FString& SoundAssetPath);
    static bool SetAmbientSoundLocation(AAmbientSound* AmbientSound, const FVector& Location);
    static bool ConfigureAmbientSoundProperties(AAmbientSound* AmbientSound, bool bAutoActivate, 
        bool bEnableSpatialization, float VolumeMultiplier);
    
    // Helper Functions for Audio Volume
    static AAudioVolume* CreateAudioVolumeInLevel(UWorld* World, const FString& VolumeName);
    static bool SetAudioVolumeBounds(AAudioVolume* AudioVolume, const FVector& Bounds);
    static bool ConfigureAudioVolumeReverb(AAudioVolume* AudioVolume, const TMap<FString, float>& ReverbSettings);
    static bool ConfigureAudioVolumeAmbientZone(AAudioVolume* AudioVolume, const TMap<FString, float>& AmbientSettings);
    static bool SetAudioVolumePriority(AAudioVolume* AudioVolume, int32 Priority);
    
    // Helper Functions for Procedural Audio
    static bool SetupMetaSoundParameters(UMetaSoundSource* MetaSound, const TMap<FString, float>& Parameters);
    static bool EnableRealTimeParameterControl(UMetaSoundSource* MetaSound, bool bEnable);
    static bool ConfigureMetaSoundInterfaces(UMetaSoundSource* MetaSound, const TArray<FString>& Interfaces);
    static bool SetMetaSoundOutputChannels(UMetaSoundSource* MetaSound, int32 Channels);
    
    // Helper Functions for Audio Occlusion
    static bool ConfigureGlobalAudioOcclusion(bool bEnable, const FString& Method, float RefreshInterval);
    static bool ConfigureAudioObstruction(bool bEnable, float MaxDistance);
    static bool SetOcclusionRefreshRate(float RefreshInterval);
    static bool ValidateOcclusionSettings(const FString& Method, float RefreshInterval, float MaxDistance);
    
    // Helper Functions for Audio Streaming
    static bool ConfigureAudioStreamingSettings(bool bEnable, int32 ChunkSize, int32 MaxStreams);
    static bool SetStreamCacheSize(int32 CacheSizeMB);
    static bool EnableAudioCompression(bool bEnable);
    static bool ValidateStreamingSettings(int32 ChunkSize, int32 MaxStreams, int32 CacheSize);
    
    // Helper Functions for Performance Analysis
    static TSharedPtr<FJsonObject> CollectAudioCPUStats(float Duration);
    static TSharedPtr<FJsonObject> CollectAudioMemoryStats(float Duration);
    static TSharedPtr<FJsonObject> CollectAudioStreamingStats(float Duration);
    static TSharedPtr<FJsonObject> GenerateAudioPerformanceReport(const TSharedPtr<FJsonObject>& Stats);
    static bool SaveAudioPerformanceReport(const TSharedPtr<FJsonObject>& Report, const FString& Filename);
    
    // Utility Functions
    static UWorld* GetCurrentWorld();
    static FAudioDevice* GetAudioDevice();
    static FString GetAudioAssetPackagePath(const FString& AssetName);
    static bool IsValidAudioAssetPath(const FString& AssetPath);
    static USoundBase* LoadSoundAsset(const FString& AssetPath);
    static bool SaveAudioAsset(UObject* Asset, const FString& PackagePath);
    
    // JSON Parsing Helpers
    static TSharedPtr<FJsonObject> ParseJsonParams(const FString& JsonParams);
    static FString CreateJsonResponse(bool bSuccess, const FString& Message,
        const TMap<FString, FString>& AdditionalData = TMap<FString, FString>());
    static FString CreateJsonResponse(bool bSuccess, const FString& Message,
        const TSharedPtr<FJsonObject>& DataObject);

    // ============================================================================
    // Real Audio Performance Analysis Functions
    // ============================================================================

    /**
     * Collects real audio performance metrics using UE 5.6 APIs.
     */
    static TSharedPtr<FJsonObject> CollectRealAudioPerformanceMetrics();

    // ============================================================================
    // Real MetaSound Integration Functions
    // ============================================================================

    /**
     * Creates real MetaSound source with parameters using UE 5.6 APIs.
     */
    static TSharedPtr<FJsonObject> CreateRealMetaSoundSource(const FString& AssetName, const TMap<FString, float>& Parameters);

    /**
     * Sets real MetaSound parameters using parameter interface.
     */
    static bool SetRealMetaSoundParameters(UMetaSoundSource* MetaSoundSource, const TMap<FString, float>& Parameters);

    /**
     * Gets MetaSound runtime information and complexity analysis.
     */
    static TSharedPtr<FJsonObject> GetMetaSoundRuntimeInfo(UMetaSoundSource* MetaSoundSource);

    /**
     * Calculates MetaSound complexity score based on graph analysis.
     */
    static int32 CalculateMetaSoundComplexity(const FMetasoundFrontendDocument& Document);

    // ============================================================================
    // Real Audio Streaming Analysis Functions
    // ============================================================================

    /**
     * Analyzes real audio streaming performance and optimization.
     */
    static TSharedPtr<FJsonObject> AnalyzeRealAudioStreaming();
    
    // Validation Functions
    static bool ValidateOutputFormat(const FString& OutputFormat);
    static bool ValidateSampleRate(int32 SampleRate);
    static bool ValidateSpatializationMethod(const FString& Method);
    static bool ValidateAttenuationShape(const FString& Shape);
    static bool ValidateReverbType(const FString& ReverbType);
    static bool ValidateOcclusionMethod(const FString& Method);
    static bool ValidateVolumeMultiplier(float Volume);
    static bool ValidatePitchMultiplier(float Pitch);
    
    // Constants for Audio System
    static const TArray<FString> SupportedOutputFormats;
    static const TArray<int32> SupportedSampleRates;
    static const TArray<FString> SupportedSpatializationMethods;
    static const TArray<FString> SupportedAttenuationShapes;
    static const TArray<FString> SupportedReverbTypes;
    static const TArray<FString> SupportedOcclusionMethods;
    
    // Audio System Constants
    static const float DefaultFalloffDistance;
    static const float DefaultMaxDistance;
    static const float DefaultVolumeMultiplier;
    static const float DefaultPitchMultiplier;
    static const int32 DefaultSampleRate;
    static const int32 DefaultOutputChannels;
    static const float DefaultOcclusionRefreshInterval;
    static const int32 DefaultStreamingChunkSize;
    static const int32 DefaultMaxConcurrentStreams;
    static const int32 DefaultStreamCacheSize;
};
