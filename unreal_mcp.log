2025-08-24 12:58:25,467 - mcp.server.lowlevel.server - DEBUG - [server.py:146] - Initializing server 'UnrealMCP'
2025-08-24 12:58:25,467 - mcp.server.lowlevel.server - DEBUG - [server.py:372] - Registering handler for ListToolsRequest
2025-08-24 12:58:25,468 - mcp.server.lowlevel.server - DEBUG - [server.py:390] - Registering handler for CallToolRequest
2025-08-24 12:58:25,468 - mcp.server.lowlevel.server - DEBUG - [server.py:249] - Registering handler for ListResourcesRequest
2025-08-24 12:58:25,468 - mcp.server.lowlevel.server - DEBUG - [server.py:277] - Registering handler for ReadResourceRequest
2025-08-24 12:58:25,468 - mcp.server.lowlevel.server - DEBUG - [server.py:221] - Registering handler for PromptListRequest
2025-08-24 12:58:25,468 - mcp.server.lowlevel.server - DEBUG - [server.py:236] - Registering handler for GetPromptRequest
2025-08-24 12:58:25,468 - mcp.server.lowlevel.server - DEBUG - [server.py:262] - Registering handler for ListResourceTemplatesRequest
2025-08-24 12:58:25,539 - UnrealMCP - INFO - [editor_tools.py:426] - Editor tools registered successfully
2025-08-24 12:58:25,546 - UnrealMCP - INFO - [blueprint_tools.py:420] - Blueprint tools registered successfully
2025-08-24 12:58:25,552 - UnrealMCP - INFO - [node_tools.py:430] - Blueprint node tools registered successfully
2025-08-24 12:58:25,552 - UnrealMCP - INFO - [node_tools.py:435] - All node tools registered successfully
2025-08-24 12:58:25,553 - UnrealMCP - INFO - [project_tools.py:64] - Project tools registered successfully
2025-08-24 12:58:25,559 - UnrealMCP - INFO - [umg_tools.py:333] - UMG tools registered successfully
2025-08-24 12:58:25,565 - UnrealMCP - INFO - [collision_tools.py:339] - Collision tools registered successfully
2025-08-24 12:58:25,571 - UnrealMCP - INFO - [network_tools.py:407] - Network tools registered successfully
2025-08-24 12:59:23,013 - mcp.server.lowlevel.server - DEBUG - [server.py:146] - Initializing server 'UnrealMCP'
2025-08-24 12:59:23,013 - mcp.server.lowlevel.server - DEBUG - [server.py:372] - Registering handler for ListToolsRequest
2025-08-24 12:59:23,013 - mcp.server.lowlevel.server - DEBUG - [server.py:390] - Registering handler for CallToolRequest
2025-08-24 12:59:23,013 - mcp.server.lowlevel.server - DEBUG - [server.py:249] - Registering handler for ListResourcesRequest
2025-08-24 12:59:23,013 - mcp.server.lowlevel.server - DEBUG - [server.py:277] - Registering handler for ReadResourceRequest
2025-08-24 12:59:23,013 - mcp.server.lowlevel.server - DEBUG - [server.py:221] - Registering handler for PromptListRequest
2025-08-24 12:59:23,013 - mcp.server.lowlevel.server - DEBUG - [server.py:236] - Registering handler for GetPromptRequest
2025-08-24 12:59:23,013 - mcp.server.lowlevel.server - DEBUG - [server.py:262] - Registering handler for ListResourceTemplatesRequest
2025-08-24 12:59:23,041 - UnrealMCP - INFO - [editor_tools.py:426] - Editor tools registered successfully
2025-08-24 12:59:23,047 - UnrealMCP - INFO - [blueprint_tools.py:420] - Blueprint tools registered successfully
2025-08-24 12:59:23,054 - UnrealMCP - INFO - [node_tools.py:430] - Blueprint node tools registered successfully
2025-08-24 12:59:23,054 - UnrealMCP - INFO - [node_tools.py:435] - All node tools registered successfully
2025-08-24 12:59:23,055 - UnrealMCP - INFO - [project_tools.py:64] - Project tools registered successfully
2025-08-24 12:59:23,060 - UnrealMCP - INFO - [umg_tools.py:333] - UMG tools registered successfully
2025-08-24 12:59:23,066 - UnrealMCP - INFO - [collision_tools.py:339] - Collision tools registered successfully
2025-08-24 12:59:23,072 - UnrealMCP - INFO - [network_tools.py:410] - Network tools registered successfully
2025-08-24 21:35:54,029 - mcp.server.lowlevel.server - DEBUG - [server.py:146] - Initializing server 'UnrealMCP'
2025-08-24 21:35:54,030 - mcp.server.lowlevel.server - DEBUG - [server.py:372] - Registering handler for ListToolsRequest
2025-08-24 21:35:54,030 - mcp.server.lowlevel.server - DEBUG - [server.py:390] - Registering handler for CallToolRequest
2025-08-24 21:35:54,030 - mcp.server.lowlevel.server - DEBUG - [server.py:249] - Registering handler for ListResourcesRequest
2025-08-24 21:35:54,030 - mcp.server.lowlevel.server - DEBUG - [server.py:277] - Registering handler for ReadResourceRequest
2025-08-24 21:35:54,030 - mcp.server.lowlevel.server - DEBUG - [server.py:221] - Registering handler for PromptListRequest
2025-08-24 21:35:54,030 - mcp.server.lowlevel.server - DEBUG - [server.py:236] - Registering handler for GetPromptRequest
2025-08-24 21:35:54,030 - mcp.server.lowlevel.server - DEBUG - [server.py:262] - Registering handler for ListResourceTemplatesRequest
2025-08-24 21:35:54,048 - UnrealMCP - INFO - [editor_tools.py:426] - Editor tools registered successfully
2025-08-24 21:35:54,053 - UnrealMCP - INFO - [blueprint_tools.py:420] - Blueprint tools registered successfully
2025-08-24 21:35:54,058 - UnrealMCP - INFO - [node_tools.py:430] - Blueprint node tools registered successfully
2025-08-24 21:35:54,058 - UnrealMCP - INFO - [node_tools.py:435] - All node tools registered successfully
2025-08-24 21:35:54,059 - UnrealMCP - INFO - [project_tools.py:64] - Project tools registered successfully
2025-08-24 21:35:54,064 - UnrealMCP - INFO - [umg_tools.py:333] - UMG tools registered successfully
2025-08-24 21:35:54,070 - UnrealMCP - INFO - [collision_tools.py:474] - Collision tools registered successfully
2025-08-24 21:35:54,075 - UnrealMCP - INFO - [network_tools.py:410] - Network tools registered successfully
2025-08-24 21:35:54,167 - UnrealMCP - INFO - [pathfinding_tools.py:741] - Pathfinding tools registered successfully
2025-08-24 21:35:54,180 - UnrealMCP - INFO - [vision_tools.py:521] - Vision tools registered successfully
2025-08-24 21:35:54,186 - UnrealMCP - INFO - [world_partition_tools.py:595] - World Partition tools registered successfully
2025-08-24 21:35:54,197 - UnrealMCP - INFO - [realm_transition_tools.py:971] - Realm transition tools registered successfully
2025-08-24 21:35:54,197 - UnrealMCP - INFO - [unreal_mcp_server.py:1062] - Starting MCP server with stdio transport
2025-08-24 21:35:54,202 - asyncio - DEBUG - [proactor_events.py:633] - Using proactor: IocpProactor
2025-08-24 21:36:10,040 - mcp.server.lowlevel.server - DEBUG - [server.py:486] - Received message: 1 validation error for JSONRPCMessage
  Invalid JSON: EOF while parsing a value at line 2 column 0 [type=json_invalid, input_value='\n', input_type=str]
    For further information visit https://errors.pydantic.dev/2.11/v/json_invalid
