// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/FileHelper.h"
#include "Misc/Paths.h"
#include "Misc/DateTime.h"
#include "Templates/SharedPointer.h"
#include "Containers/UnrealString.h"
#include "Containers/Array.h"
#include "Containers/Map.h"

/**
 * Classe para gerenciar comandos do Sistema de Rede Multiplayer
 * Implementa funcionalidades para replicação multicamada, predição de cliente e sincronização
 */
class UNREALMCP_API FUnrealMCPNetworkCommands
{
public:
    FUnrealMCPNetworkCommands();
    ~FUnrealMCPNetworkCommands();

    // Método principal para processar comandos
    TSharedPtr<FJsonObject> HandleCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params);
    
    // Métodos principais para comandos de rede
    TSharedPtr<FJsonObject> HandleCreateMultilayerNetworkSystem(const TSharedPtr<FJsonObject>& RequestData);
    TSharedPtr<FJsonObject> HandleConfigureObjectReplication(const TSharedPtr<FJsonObject>& RequestData);
    TSharedPtr<FJsonObject> HandleSetupClientPrediction(const TSharedPtr<FJsonObject>& RequestData);
    TSharedPtr<FJsonObject> HandleConfigureNetworkSynchronization(const TSharedPtr<FJsonObject>& RequestData);
    TSharedPtr<FJsonObject> HandleSetupLagCompensation(const TSharedPtr<FJsonObject>& RequestData);
    TSharedPtr<FJsonObject> HandleConfigureBandwidthOptimization(const TSharedPtr<FJsonObject>& RequestData);
    TSharedPtr<FJsonObject> HandleDebugNetworkPerformance(const TSharedPtr<FJsonObject>& RequestData);
    TSharedPtr<FJsonObject> HandleValidateNetworkSetup(const TSharedPtr<FJsonObject>& RequestData);
    TSharedPtr<FJsonObject> HandleGetNetworkSystemStatus(const TSharedPtr<FJsonObject>& RequestData);

private:
    // Funções auxiliares para conversão de tipos
    FString ConvertReplicationModeToString(int32 Mode);
    FString ConvertNetworkRoleToString(int32 Role);
    FString ConvertPredictionTypeToString(int32 Type);
    int32 ConvertStringToReplicationMode(const FString& ModeString);
    int32 ConvertStringToNetworkRole(const FString& RoleString);
    int32 ConvertStringToPredictionType(const FString& TypeString);

    // Funções auxiliares para validação
    bool ValidateNetworkLayerConfig(const TSharedPtr<FJsonObject>& Config);
    bool ValidateReplicationConfig(const TSharedPtr<FJsonObject>& Config);
    bool ValidatePredictionConfig(const TSharedPtr<FJsonObject>& Config);
    bool ValidateSynchronizationConfig(const TSharedPtr<FJsonObject>& Config);
    bool ValidateBandwidthSettings(const TSharedPtr<FJsonObject>& Settings);

    // Funções auxiliares para criação de respostas
    TSharedPtr<FJsonObject> CreateSuccessResponse(const FString& Message, const TSharedPtr<FJsonObject>& Data);
    TSharedPtr<FJsonObject> CreateErrorResponse(const FString& Message, const FString& ErrorCode);
    TSharedPtr<FJsonObject> CreateNetworkLayerData(const TSharedPtr<FJsonObject>& Config, int32 LayerIndex);
    TSharedPtr<FJsonObject> CreateReplicationData(const TSharedPtr<FJsonObject>& Config);
    TSharedPtr<FJsonObject> CreatePredictionData(const TSharedPtr<FJsonObject>& Config);
    TSharedPtr<FJsonObject> CreateSynchronizationData(const TSharedPtr<FJsonObject>& Config);

    // Funções de simulação para desenvolvimento
    TSharedPtr<FJsonObject> SimulateNetworkSystemCreation(const TArray<TSharedPtr<FJsonObject>>& LayerConfigs, const TSharedPtr<FJsonObject>& GlobalSettings);
    TSharedPtr<FJsonObject> SimulateReplicationConfiguration(const FString& LayerName, const TArray<TSharedPtr<FJsonObject>>& ReplicationConfigs);
    TSharedPtr<FJsonObject> SimulatePredictionSetup(const FString& LayerName, const TArray<TSharedPtr<FJsonObject>>& PredictionConfigs);
    TSharedPtr<FJsonObject> SimulateSynchronizationConfiguration(const FString& LayerName, const TArray<TSharedPtr<FJsonObject>>& SyncConfigs);
    TSharedPtr<FJsonObject> SimulateLagCompensationSetup(const FString& LayerName, const TSharedPtr<FJsonObject>& CompensationSettings);
    TSharedPtr<FJsonObject> SimulateBandwidthOptimization(const FString& LayerName, const TSharedPtr<FJsonObject>& OptimizationSettings);
    TSharedPtr<FJsonObject> SimulateNetworkDebug(const FString& LayerName, const TSharedPtr<FJsonObject>& DebugOptions);
    TSharedPtr<FJsonObject> SimulateNetworkValidation(const FString& LayerName);
    TSharedPtr<FJsonObject> SimulateNetworkStatus(const FString& LayerName);

    // Funções auxiliares para métricas e otimização
    TSharedPtr<FJsonObject> GenerateRealNetworkMetrics();
    TSharedPtr<FJsonObject> GenerateRealReplicationMetrics();
    TSharedPtr<FJsonObject> GenerateRealPredictionMetrics();
    TSharedPtr<FJsonObject> GenerateRealPerformanceMetrics();
    TArray<TSharedPtr<FJsonObject>> GenerateOptimizationSuggestions(const TArray<TSharedPtr<FJsonObject>>& Configs);
    TArray<TSharedPtr<FJsonObject>> GeneratePerformanceIssues();
    TSharedPtr<FJsonObject> GenerateValidationResults(const FString& LayerName);

    // Funções auxiliares para cálculos
    float CalculateBandwidthUsage(const TArray<TSharedPtr<FJsonObject>>& Configs);
    float CalculateLatencyImpact(const TSharedPtr<FJsonObject>& Config);
    float CalculateMemoryUsage(const TSharedPtr<FJsonObject>& Config);
    float CalculateCPUUsage(const TSharedPtr<FJsonObject>& Config);

    // Funções para persistência de estado
    void SaveNetworkState(const FString& LayerName, const TSharedPtr<FJsonObject>& StateData);
    TSharedPtr<FJsonObject> LoadNetworkState(const FString& LayerName);
    void ClearNetworkState(const FString& LayerName);
    void UpdatePerformanceMetrics(const FString& LayerName, const TSharedPtr<FJsonObject>& Metrics);
    TSharedPtr<FJsonObject> GetPerformanceMetrics(const FString& LayerName);

    // ============================================================================
    // Real Network Performance Metrics Implementation Functions
    // ============================================================================

    /**
     * Collects real network performance metrics using UNetDriver and UNetConnection APIs.
     */
    TSharedPtr<FJsonObject> CollectRealNetworkPerformanceMetrics();

    /**
     * Collects real packet loss metrics from all network connections.
     */
    TSharedPtr<FJsonObject> CollectRealPacketLossMetrics();

    /**
     * Collects real replication performance metrics including actor analysis and efficiency.
     */
    TSharedPtr<FJsonObject> CollectRealReplicationPerformanceMetrics();

    /**
     * Collects real network rollback system metrics and capabilities.
     */
    TSharedPtr<FJsonObject> CollectRealNetworkRollbackMetrics();

    /**
     * Generates comprehensive network analytics with bottleneck detection and recommendations.
     */
    TSharedPtr<FJsonObject> GenerateComprehensiveNetworkAnalytics();

    /**
     * Analyzes network bottlenecks from performance metrics.
     */
    TArray<TSharedPtr<FJsonValue>> AnalyzeNetworkBottlenecks(
        const TSharedPtr<FJsonObject>& PerformanceMetrics,
        const TSharedPtr<FJsonObject>& PacketLossMetrics,
        const TSharedPtr<FJsonObject>& ReplicationMetrics);

    /**
     * Generates network optimization recommendations based on current metrics.
     */
    TArray<TSharedPtr<FJsonValue>> GenerateNetworkOptimizationRecommendations(
        const TSharedPtr<FJsonObject>& PerformanceMetrics,
        const TSharedPtr<FJsonObject>& PacketLossMetrics,
        const TSharedPtr<FJsonObject>& ReplicationMetrics);

    /**
     * Calculates overall network health score from 0-100.
     */
    float CalculateOverallNetworkHealthScore(
        const TSharedPtr<FJsonObject>& PerformanceMetrics,
        const TSharedPtr<FJsonObject>& PacketLossMetrics,
        const TSharedPtr<FJsonObject>& ReplicationMetrics);

    // ============================================================================
    // Real Network System Status Implementation Functions
    // ============================================================================

    /**
     * Gets real network system status using UE 5.6 network driver APIs.
     */
    TSharedPtr<FJsonObject> GetRealNetworkSystemStatus();

    // Variáveis de estado
    TMap<FString, TSharedPtr<FJsonObject>> NetworkSystems;
    TMap<FString, TSharedPtr<FJsonObject>> LayerConfigurations;
    TMap<FString, TSharedPtr<FJsonObject>> PerformanceMetrics;
    FString LastSystemId;
    FDateTime LastUpdateTime;
};