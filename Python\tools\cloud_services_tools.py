"""
Cloud Services Integration Tools for Unreal Engine MCP Server

This module provides comprehensive Cloud Services Integration tools that are 100% compatible with the 
C++ implementations in UnrealMCPCloudServicesCommands.cpp. Based on UE 5.6 official documentation.
"""

import logging
import json
from typing import Dict, List, Any, Optional
from mcp.server.fastmcp import FastMCP

# Set up logging
logger = logging.getLogger("UnrealMCP")

def register_cloud_services_tools(mcp: FastMCP):
    """Register Cloud Services Integration tools with the MCP server - 100% compatible with C++ implementations."""

    @mcp.tool()
    def setup_aws_integration(
        access_key_id: str,
        secret_access_key: str,
        region: str = "us-east-1",
        services: List[str] = None,
        enable_s3: bool = True,
        enable_dynamodb: bool = True,
        enable_lambda: bool = False
    ) -> Dict[str, Any]:
        """
        Setup AWS (Amazon Web Services) integration for cloud services.
        Compatible with HandleSetupAWSIntegration in C++.
        
        Args:
            access_key_id: AWS access key ID
            secret_access_key: AWS secret access key
            region: AWS region
            services: List of AWS services to enable
            enable_s3: Enable S3 storage service
            enable_dynamodb: Enable DynamoDB database service
            enable_lambda: Enable Lambda serverless functions
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "access_key_id": access_key_id,
                "secret_access_key": secret_access_key,
                "region": region,
                "services": services or ["s3", "dynamodb"],
                "enable_s3": enable_s3,
                "enable_dynamodb": enable_dynamodb,
                "enable_lambda": enable_lambda
            }

            response = unreal.send_command("setup_aws_integration", params)
            return response or {"success": True, "message": "AWS integration setup successfully"}

        except Exception as e:
            logger.error(f"Error setting up AWS integration: {e}")
            return {"success": False, "message": f"Error setting up AWS integration: {e}"}

    @mcp.tool()
    def configure_azure_services(
        subscription_id: str,
        tenant_id: str,
        client_id: str,
        client_secret: str,
        resource_group: str = "",
        enable_storage: bool = True,
        enable_cosmos_db: bool = True,
        enable_functions: bool = False
    ) -> Dict[str, Any]:
        """
        Configure Microsoft Azure cloud services integration.
        Compatible with HandleConfigureAzureServices in C++.
        
        Args:
            subscription_id: Azure subscription ID
            tenant_id: Azure tenant ID
            client_id: Azure client ID
            client_secret: Azure client secret
            resource_group: Azure resource group name
            enable_storage: Enable Azure Storage
            enable_cosmos_db: Enable Azure Cosmos DB
            enable_functions: Enable Azure Functions
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "subscription_id": subscription_id,
                "tenant_id": tenant_id,
                "client_id": client_id,
                "client_secret": client_secret,
                "resource_group": resource_group,
                "enable_storage": enable_storage,
                "enable_cosmos_db": enable_cosmos_db,
                "enable_functions": enable_functions
            }

            response = unreal.send_command("configure_azure_services", params)
            return response or {"success": True, "message": "Azure services configured successfully"}

        except Exception as e:
            logger.error(f"Error configuring Azure services: {e}")
            return {"success": False, "message": f"Error configuring Azure services: {e}"}

    @mcp.tool()
    def setup_google_cloud_platform(
        project_id: str,
        service_account_key: str,
        region: str = "us-central1",
        enable_cloud_storage: bool = True,
        enable_firestore: bool = True,
        enable_cloud_functions: bool = False,
        enable_pub_sub: bool = False
    ) -> Dict[str, Any]:
        """
        Setup Google Cloud Platform (GCP) integration.
        Compatible with HandleSetupGoogleCloudPlatform in C++.
        
        Args:
            project_id: GCP project ID
            service_account_key: Service account key JSON
            region: GCP region
            enable_cloud_storage: Enable Cloud Storage
            enable_firestore: Enable Firestore database
            enable_cloud_functions: Enable Cloud Functions
            enable_pub_sub: Enable Pub/Sub messaging
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "project_id": project_id,
                "service_account_key": service_account_key,
                "region": region,
                "enable_cloud_storage": enable_cloud_storage,
                "enable_firestore": enable_firestore,
                "enable_cloud_functions": enable_cloud_functions,
                "enable_pub_sub": enable_pub_sub
            }

            response = unreal.send_command("setup_google_cloud_platform", params)
            return response or {"success": True, "message": "Google Cloud Platform setup successfully"}

        except Exception as e:
            logger.error(f"Error setting up Google Cloud Platform: {e}")
            return {"success": False, "message": f"Error setting up Google Cloud Platform: {e}"}

    @mcp.tool()
    def configure_firebase_integration(
        project_id: str,
        api_key: str,
        app_id: str,
        messaging_sender_id: str = "",
        enable_analytics: bool = True,
        enable_crashlytics: bool = True,
        enable_realtime_database: bool = True,
        enable_cloud_messaging: bool = True
    ) -> Dict[str, Any]:
        """
        Configure Firebase integration for mobile and web services.
        Compatible with HandleConfigureFirebaseIntegration in C++.
        
        Args:
            project_id: Firebase project ID
            api_key: Firebase API key
            app_id: Firebase app ID
            messaging_sender_id: Firebase messaging sender ID
            enable_analytics: Enable Firebase Analytics
            enable_crashlytics: Enable Firebase Crashlytics
            enable_realtime_database: Enable Firebase Realtime Database
            enable_cloud_messaging: Enable Firebase Cloud Messaging
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "project_id": project_id,
                "api_key": api_key,
                "app_id": app_id,
                "messaging_sender_id": messaging_sender_id,
                "enable_analytics": enable_analytics,
                "enable_crashlytics": enable_crashlytics,
                "enable_realtime_database": enable_realtime_database,
                "enable_cloud_messaging": enable_cloud_messaging
            }

            response = unreal.send_command("configure_firebase_integration", params)
            return response or {"success": True, "message": "Firebase integration configured successfully"}

        except Exception as e:
            logger.error(f"Error configuring Firebase integration: {e}")
            return {"success": False, "message": f"Error configuring Firebase integration: {e}"}

    @mcp.tool()
    def setup_playfab_services(
        title_id: str,
        secret_key: str,
        enable_player_data: bool = True,
        enable_leaderboards: bool = True,
        enable_multiplayer: bool = True,
        enable_economy: bool = False,
        enable_push_notifications: bool = False
    ) -> Dict[str, Any]:
        """
        Setup PlayFab services for game backend functionality.
        Compatible with HandleSetupPlayFabServices in C++.
        
        Args:
            title_id: PlayFab title ID
            secret_key: PlayFab secret key
            enable_player_data: Enable player data management
            enable_leaderboards: Enable leaderboards
            enable_multiplayer: Enable multiplayer services
            enable_economy: Enable virtual economy
            enable_push_notifications: Enable push notifications
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "title_id": title_id,
                "secret_key": secret_key,
                "enable_player_data": enable_player_data,
                "enable_leaderboards": enable_leaderboards,
                "enable_multiplayer": enable_multiplayer,
                "enable_economy": enable_economy,
                "enable_push_notifications": enable_push_notifications
            }

            response = unreal.send_command("setup_playfab_services", params)
            return response or {"success": True, "message": "PlayFab services setup successfully"}

        except Exception as e:
            logger.error(f"Error setting up PlayFab services: {e}")
            return {"success": False, "message": f"Error setting up PlayFab services: {e}"}

    @mcp.tool()
    def configure_epic_online_services(
        product_id: str,
        sandbox_id: str,
        deployment_id: str,
        client_id: str,
        client_secret: str,
        enable_achievements: bool = True,
        enable_leaderboards: bool = True,
        enable_friends: bool = True,
        enable_voice_chat: bool = False
    ) -> Dict[str, Any]:
        """
        Configure Epic Online Services (EOS) integration.
        Compatible with HandleConfigureEpicOnlineServices in C++.
        
        Args:
            product_id: EOS product ID
            sandbox_id: EOS sandbox ID
            deployment_id: EOS deployment ID
            client_id: EOS client ID
            client_secret: EOS client secret
            enable_achievements: Enable achievements system
            enable_leaderboards: Enable leaderboards system
            enable_friends: Enable friends system
            enable_voice_chat: Enable voice chat
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "product_id": product_id,
                "sandbox_id": sandbox_id,
                "deployment_id": deployment_id,
                "client_id": client_id,
                "client_secret": client_secret,
                "enable_achievements": enable_achievements,
                "enable_leaderboards": enable_leaderboards,
                "enable_friends": enable_friends,
                "enable_voice_chat": enable_voice_chat
            }

            response = unreal.send_command("configure_epic_online_services", params)
            return response or {"success": True, "message": "Epic Online Services configured successfully"}

        except Exception as e:
            logger.error(f"Error configuring Epic Online Services: {e}")
            return {"success": False, "message": f"Error configuring Epic Online Services: {e}"}

    @mcp.tool()
    def setup_steam_integration(
        app_id: str,
        api_key: str = "",
        enable_achievements: bool = True,
        enable_leaderboards: bool = True,
        enable_cloud_saves: bool = True,
        enable_workshop: bool = False,
        enable_matchmaking: bool = True
    ) -> Dict[str, Any]:
        """
        Setup Steam integration for Steamworks features.
        Compatible with HandleSetupSteamIntegration in C++.

        Args:
            app_id: Steam app ID
            api_key: Steam Web API key
            enable_achievements: Enable Steam achievements
            enable_leaderboards: Enable Steam leaderboards
            enable_cloud_saves: Enable Steam Cloud saves
            enable_workshop: Enable Steam Workshop
            enable_matchmaking: Enable Steam matchmaking
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "app_id": app_id,
                "api_key": api_key,
                "enable_achievements": enable_achievements,
                "enable_leaderboards": enable_leaderboards,
                "enable_cloud_saves": enable_cloud_saves,
                "enable_workshop": enable_workshop,
                "enable_matchmaking": enable_matchmaking
            }

            response = unreal.send_command("setup_steam_integration", params)
            return response or {"success": True, "message": "Steam integration setup successfully"}

        except Exception as e:
            logger.error(f"Error setting up Steam integration: {e}")
            return {"success": False, "message": f"Error setting up Steam integration: {e}"}

    @mcp.tool()
    def configure_dedicated_servers(
        server_type: str = "aws_ec2",
        instance_type: str = "t3.medium",
        region: str = "us-east-1",
        max_players: int = 16,
        auto_scaling: bool = True,
        enable_monitoring: bool = True
    ) -> Dict[str, Any]:
        """
        Configure dedicated servers for multiplayer games.
        Compatible with HandleConfigureDedicatedServers in C++.

        Args:
            server_type: Type of server (aws_ec2, azure_vm, gcp_compute, custom)
            instance_type: Server instance type
            region: Server region
            max_players: Maximum players per server
            auto_scaling: Enable auto-scaling
            enable_monitoring: Enable server monitoring
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "server_type": server_type,
                "instance_type": instance_type,
                "region": region,
                "max_players": max_players,
                "auto_scaling": auto_scaling,
                "enable_monitoring": enable_monitoring
            }

            response = unreal.send_command("configure_dedicated_servers", params)
            return response or {"success": True, "message": "Dedicated servers configured successfully"}

        except Exception as e:
            logger.error(f"Error configuring dedicated servers: {e}")
            return {"success": False, "message": f"Error configuring dedicated servers: {e}"}

    @mcp.tool()
    def setup_matchmaking_service(
        service_provider: str = "eos",
        max_players_per_match: int = 16,
        skill_based_matching: bool = True,
        region_based_matching: bool = True,
        custom_rules: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        Setup matchmaking service for multiplayer games.
        Compatible with HandleSetupMatchmakingService in C++.

        Args:
            service_provider: Matchmaking service provider (eos, steam, playfab, custom)
            max_players_per_match: Maximum players per match
            skill_based_matching: Enable skill-based matching
            region_based_matching: Enable region-based matching
            custom_rules: Custom matchmaking rules
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "service_provider": service_provider,
                "max_players_per_match": max_players_per_match,
                "skill_based_matching": skill_based_matching,
                "region_based_matching": region_based_matching,
                "custom_rules": custom_rules or {}
            }

            response = unreal.send_command("setup_matchmaking_service", params)
            return response or {"success": True, "message": "Matchmaking service setup successfully"}

        except Exception as e:
            logger.error(f"Error setting up matchmaking service: {e}")
            return {"success": False, "message": f"Error setting up matchmaking service: {e}"}

    @mcp.tool()
    def configure_leaderboards_system(
        provider: str = "eos",
        leaderboard_configs: List[Dict[str, Any]] = None,
        enable_global_leaderboards: bool = True,
        enable_friend_leaderboards: bool = True,
        reset_schedule: str = "weekly"
    ) -> Dict[str, Any]:
        """
        Configure leaderboards system for competitive features.
        Compatible with HandleConfigureLeaderboardsSystem in C++.

        Args:
            provider: Leaderboards provider (eos, steam, playfab, google_play, game_center)
            leaderboard_configs: List of leaderboard configurations
            enable_global_leaderboards: Enable global leaderboards
            enable_friend_leaderboards: Enable friend leaderboards
            reset_schedule: Reset schedule (daily, weekly, monthly, never)
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "provider": provider,
                "leaderboard_configs": leaderboard_configs or [],
                "enable_global_leaderboards": enable_global_leaderboards,
                "enable_friend_leaderboards": enable_friend_leaderboards,
                "reset_schedule": reset_schedule
            }

            response = unreal.send_command("configure_leaderboards_system", params)
            return response or {"success": True, "message": "Leaderboards system configured successfully"}

        except Exception as e:
            logger.error(f"Error configuring leaderboards system: {e}")
            return {"success": False, "message": f"Error configuring leaderboards system: {e}"}

    @mcp.tool()
    def setup_cloud_storage_system(
        provider: str = "aws_s3",
        bucket_name: str = "",
        enable_cdn: bool = True,
        enable_versioning: bool = False,
        encryption_enabled: bool = True,
        access_control: str = "private"
    ) -> Dict[str, Any]:
        """
        Setup cloud storage system for game assets and user data.
        Compatible with HandleSetupCloudStorageSystem in C++.

        Args:
            provider: Storage provider (aws_s3, azure_blob, gcp_storage, firebase_storage)
            bucket_name: Storage bucket/container name
            enable_cdn: Enable CDN for faster content delivery
            enable_versioning: Enable object versioning
            encryption_enabled: Enable encryption at rest
            access_control: Access control level (private, public_read, public_read_write)
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "provider": provider,
                "bucket_name": bucket_name,
                "enable_cdn": enable_cdn,
                "enable_versioning": enable_versioning,
                "encryption_enabled": encryption_enabled,
                "access_control": access_control
            }

            response = unreal.send_command("setup_cloud_storage_system", params)
            return response or {"success": True, "message": "Cloud storage system setup successfully"}

        except Exception as e:
            logger.error(f"Error setting up cloud storage system: {e}")
            return {"success": False, "message": f"Error setting up cloud storage system: {e}"}

    @mcp.tool()
    def configure_cdn_distribution(
        provider: str = "aws_cloudfront",
        origin_domain: str = "",
        cache_behaviors: List[Dict[str, Any]] = None,
        enable_compression: bool = True,
        enable_http2: bool = True,
        ssl_certificate: str = "default"
    ) -> Dict[str, Any]:
        """
        Configure CDN (Content Delivery Network) for fast content distribution.
        Compatible with HandleConfigureCDNDistribution in C++.

        Args:
            provider: CDN provider (aws_cloudfront, azure_cdn, gcp_cdn, cloudflare)
            origin_domain: Origin domain for content
            cache_behaviors: List of cache behavior configurations
            enable_compression: Enable content compression
            enable_http2: Enable HTTP/2 support
            ssl_certificate: SSL certificate configuration
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "provider": provider,
                "origin_domain": origin_domain,
                "cache_behaviors": cache_behaviors or [],
                "enable_compression": enable_compression,
                "enable_http2": enable_http2,
                "ssl_certificate": ssl_certificate
            }

            response = unreal.send_command("configure_cdn_distribution", params)
            return response or {"success": True, "message": "CDN distribution configured successfully"}

        except Exception as e:
            logger.error(f"Error configuring CDN distribution: {e}")
            return {"success": False, "message": f"Error configuring CDN distribution: {e}"}
