// Copyright Epic Games, Inc. All Rights Reserved.
/*===========================================================================
	Generated code exported from UnrealHeaderTool.
	DO NOT modify this manually! Edit the corresponding .h files instead!
===========================================================================*/

#include "UObject/GeneratedCppIncludes.h"
#include "Commands/UnrealMCPProceduralCommands.h"

PRAGMA_DISABLE_DEPRECATION_WARNINGS

void EmptyLinkFunctionForGeneratedCodeUnrealMCPProceduralCommands() {}

// ********** Begin Cross Module References ********************************************************
UNREALMCP_API UScriptStruct* Z_Construct_UScriptStruct_FBiomeData();
UPackage* Z_Construct_UPackage__Script_UnrealMCP();
// ********** End Cross Module References **********************************************************

// ********** Begin ScriptStruct FBiomeData ********************************************************
static FStructRegistrationInfo Z_Registration_Info_UScriptStruct_FBiomeData;
class UScriptStruct* FBiomeData::StaticStruct()
{
	if (!Z_Registration_Info_UScriptStruct_FBiomeData.OuterSingleton)
	{
		Z_Registration_Info_UScriptStruct_FBiomeData.OuterSingleton = GetStaticStruct(Z_Construct_UScriptStruct_FBiomeData, (UObject*)Z_Construct_UPackage__Script_UnrealMCP(), TEXT("BiomeData"));
	}
	return Z_Registration_Info_UScriptStruct_FBiomeData.OuterSingleton;
}
struct Z_Construct_UScriptStruct_FBiomeData_Statics
{
#if WITH_METADATA
	static constexpr UECodeGen_Private::FMetaDataPairParam Struct_MetaDataParams[] = {
#if !UE_BUILD_SHIPPING
		{ "Comment", "// Biome data structure\n" },
#endif
		{ "ModuleRelativePath", "Public/Commands/UnrealMCPProceduralCommands.h" },
#if !UE_BUILD_SHIPPING
		{ "ToolTip", "Biome data structure" },
#endif
	};
#endif // WITH_METADATA
	static void* NewStructOps()
	{
		return (UScriptStruct::ICppStructOps*)new UScriptStruct::TCppStructOps<FBiomeData>();
	}
	static const UECodeGen_Private::FStructParams StructParams;
};
const UECodeGen_Private::FStructParams Z_Construct_UScriptStruct_FBiomeData_Statics::StructParams = {
	(UObject* (*)())Z_Construct_UPackage__Script_UnrealMCP,
	nullptr,
	&NewStructOps,
	"BiomeData",
	nullptr,
	0,
	sizeof(FBiomeData),
	alignof(FBiomeData),
	RF_Public|RF_Transient|RF_MarkAsNative,
	EStructFlags(0x00000001),
	METADATA_PARAMS(UE_ARRAY_COUNT(Z_Construct_UScriptStruct_FBiomeData_Statics::Struct_MetaDataParams), Z_Construct_UScriptStruct_FBiomeData_Statics::Struct_MetaDataParams)
};
UScriptStruct* Z_Construct_UScriptStruct_FBiomeData()
{
	if (!Z_Registration_Info_UScriptStruct_FBiomeData.InnerSingleton)
	{
		UECodeGen_Private::ConstructUScriptStruct(Z_Registration_Info_UScriptStruct_FBiomeData.InnerSingleton, Z_Construct_UScriptStruct_FBiomeData_Statics::StructParams);
	}
	return Z_Registration_Info_UScriptStruct_FBiomeData.InnerSingleton;
}
// ********** End ScriptStruct FBiomeData **********************************************************

// ********** Begin Registration *******************************************************************
struct Z_CompiledInDeferFile_FID_Game_Auracron_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPProceduralCommands_h__Script_UnrealMCP_Statics
{
	static constexpr FStructRegisterCompiledInInfo ScriptStructInfo[] = {
		{ FBiomeData::StaticStruct, Z_Construct_UScriptStruct_FBiomeData_Statics::NewStructOps, TEXT("BiomeData"), &Z_Registration_Info_UScriptStruct_FBiomeData, CONSTRUCT_RELOAD_VERSION_INFO(FStructReloadVersionInfo, sizeof(FBiomeData), 2273960162U) },
	};
};
static FRegisterCompiledInInfo Z_CompiledInDeferFile_FID_Game_Auracron_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPProceduralCommands_h__Script_UnrealMCP_2911636851(TEXT("/Script/UnrealMCP"),
	nullptr, 0,
	Z_CompiledInDeferFile_FID_Game_Auracron_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPProceduralCommands_h__Script_UnrealMCP_Statics::ScriptStructInfo, UE_ARRAY_COUNT(Z_CompiledInDeferFile_FID_Game_Auracron_Plugins_UnrealMCP_Source_UnrealMCP_Public_Commands_UnrealMCPProceduralCommands_h__Script_UnrealMCP_Statics::ScriptStructInfo),
	nullptr, 0);
// ********** End Registration *********************************************************************

PRAGMA_ENABLE_DEPRECATION_WARNINGS
