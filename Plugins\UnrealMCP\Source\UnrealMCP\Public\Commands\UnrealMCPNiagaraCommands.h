// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "NiagaraSystem.h"
#include "NiagaraEmitter.h"
#include "NiagaraComponent.h"
#include "NiagaraFunctionLibrary.h"
#include "NiagaraDataInterface.h"
#include "NiagaraParameterCollection.h"
#include "NiagaraScript.h"
#include "NiagaraModule.h"
#include "NiagaraRendererProperties.h"
#include "NiagaraSpriteRendererProperties.h"
#include "NiagaraMeshRendererProperties.h"
#include "NiagaraRibbonRendererProperties.h"
#include "NiagaraLightRendererProperties.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "UObject/SavePackage.h"
#include "Misc/PackageName.h"
#include "EditorAssetLibrary.h"
#include "NiagaraSystemFactoryNew.h"
#include "NiagaraEmitterFactoryNew.h"
#include "NiagaraNodeFunctionCall.h"

/**
 * UnrealMCPNiagaraCommands
 * 
 * Handles all Niagara Particle System related commands for the MCP server.
 * Provides comprehensive VFX creation, editing, and optimization tools
 * compatible with Unreal Engine 5.6 Niagara system.
 * 
 * Features:
 * - Niagara System and Emitter creation
 * - Module and parameter management
 * - GPU and CPU particle simulation
 * - Advanced effect types (Lights, Ribbons, Meshes, Beams)
 * - Performance optimization and analysis
 * - Real-time parameter manipulation
 * - Cross-platform VFX configuration
 * - Integration with Chaos Physics
 */
class UNREALMCP_API UnrealMCPNiagaraCommands
{
public:
    UnrealMCPNiagaraCommands();
    ~UnrealMCPNiagaraCommands();

    // Core Niagara System Management
    static FString HandleCreateNiagaraSystem(const FString& JsonParams);
    static FString HandleCreateNiagaraEmitter(const FString& JsonParams);
    static FString HandleAddEmitterToSystem(const FString& JsonParams);
    static FString HandleAddNiagaraModule(const FString& JsonParams);
    static FString HandleSetNiagaraParameter(const FString& JsonParams);
    static FString HandleSpawnNiagaraSystemAtLocation(const FString& JsonParams);

    // Advanced Effect Creation
    static FString HandleCreateParticleLightEffect(const FString& JsonParams);
    static FString HandleCreateGPUSpriteEffect(const FString& JsonParams);
    static FString HandleCreateRibbonEffect(const FString& JsonParams);
    static FString HandleCreateMeshParticleEffect(const FString& JsonParams);
    static FString HandleCreateBeamEffect(const FString& JsonParams);

    // Performance and Optimization
    static FString HandleOptimizeNiagaraPerformance(const FString& JsonParams);
    static FString HandleAnalyzeNiagaraComplexity(const FString& JsonParams);
    static FString HandleConfigureNiagaraLOD(const FString& JsonParams);
    static FString HandleSetupNiagaraCulling(const FString& JsonParams);

private:
    // Helper Functions for System Creation
    static UNiagaraSystem* CreateNiagaraSystemAsset(const FString& SystemName, const FString& PackagePath);
    static UNiagaraEmitter* CreateNiagaraEmitterAsset(const FString& EmitterName, 
        const FString& PackagePath, const FString& EmitterType);
    
    // Helper Functions for Emitter Management
    static bool AddEmitterToNiagaraSystem(UNiagaraSystem* System, UNiagaraEmitter* Emitter, 
        const FString& EmitterName);
    static UNiagaraScript* GetEmitterScript(UNiagaraEmitter* Emitter, ENiagaraScriptUsage ScriptUsage);
    
    // Helper Functions for Module Management
    static bool AddModuleToEmitter(UNiagaraEmitter* Emitter, const FString& ModuleType, 
        const FString& ModuleGroup, const TSharedPtr<FJsonObject>& Properties);
    static UNiagaraNodeFunctionCall* CreateModuleNode(UNiagaraScript* Script, 
        const FString& ModuleType);
    
    // Helper Functions for Parameter Management
    static bool SetSystemParameter(UNiagaraSystem* System, const FString& ParameterName, 
        const FString& ParameterType, const FString& ParameterValue);
    static bool SetEmitterParameter(UNiagaraEmitter* Emitter, const FString& ParameterName, 
        const FString& ParameterType, const FString& ParameterValue);
    static FNiagaraVariable CreateNiagaraVariable(const FString& ParameterName, 
        const FString& ParameterType);
    
    // Helper Functions for Spawning
    static UNiagaraComponent* SpawnNiagaraSystemInWorld(UNiagaraSystem* System, 
        const FVector& Location, const FRotator& Rotation, bool bAutoDestroy);
    
    // Helper Functions for Advanced Effects
    static UNiagaraSystem* CreateParticleLightSystem(const FString& SystemName, 
        const FString& PackagePath, const FLinearColor& LightColor, 
        float LightIntensity, float LightRadius);
    static UNiagaraSystem* CreateGPUSpriteSystem(const FString& SystemName, 
        const FString& PackagePath, int32 ParticleCount, UTexture2D* Texture);
    static UNiagaraSystem* CreateRibbonSystem(const FString& SystemName, 
        const FString& PackagePath, float RibbonWidth, int32 RibbonSegments);
    static UNiagaraSystem* CreateMeshParticleSystem(const FString& SystemName, 
        const FString& PackagePath, UStaticMesh* Mesh, int32 ParticleCount);
    static UNiagaraSystem* CreateBeamSystem(const FString& SystemName, 
        const FString& PackagePath, float BeamLength, float BeamWidth);
    
    // Helper Functions for Renderer Setup
    static UNiagaraSpriteRendererProperties* SetupSpriteRenderer(UNiagaraEmitter* Emitter);
    static UNiagaraMeshRendererProperties* SetupMeshRenderer(UNiagaraEmitter* Emitter, 
        UStaticMesh* Mesh);
    static UNiagaraRibbonRendererProperties* SetupRibbonRenderer(UNiagaraEmitter* Emitter);
    static UNiagaraLightRendererProperties* SetupLightRenderer(UNiagaraEmitter* Emitter);
    
    // Helper Functions for Performance Optimization
    static void OptimizeSystemForPlatform(UNiagaraSystem* System, const FString& Platform, 
        const FString& OptimizationLevel);
    static void SetupLODSettings(UNiagaraSystem* System, const FString& OptimizationLevel);
    static void ConfigureCullingSettings(UNiagaraSystem* System, const FString& OptimizationLevel);
    static void OptimizeGPUSimulation(UNiagaraEmitter* Emitter);
    
    // Helper Functions for Module Templates
    static void AddSpawnRateModule(UNiagaraEmitter* Emitter, float SpawnRate);
    static void AddInitialVelocityModule(UNiagaraEmitter* Emitter, const FVector& Velocity);
    static void AddInitialColorModule(UNiagaraEmitter* Emitter, const FLinearColor& Color);
    static void AddInitialSizeModule(UNiagaraEmitter* Emitter, const FVector2D& Size);
    static void AddLifetimeModule(UNiagaraEmitter* Emitter, float Lifetime);
    static void AddGravityModule(UNiagaraEmitter* Emitter, const FVector& Gravity);
    static void AddCurlNoiseModule(UNiagaraEmitter* Emitter, float NoiseStrength);
    
    // Utility Functions
    static FString NiagaraPathToPackageName(const FString& NiagaraPath);
    static UNiagaraSystem* LoadNiagaraSystemFromPath(const FString& SystemPath);
    static UNiagaraEmitter* LoadNiagaraEmitterFromPath(const FString& EmitterPath);
    static bool SaveNiagaraAsset(UObject* Asset, const FString& PackagePath);
    static FString GetNiagaraErrorMessage(const FString& ErrorCode);
    
    // JSON Parsing Helpers
    static TSharedPtr<FJsonObject> ParseJsonParams(const FString& JsonParams);
    static FString CreateJsonResponse(bool bSuccess, const FString& Message, 
        const TMap<FString, FString>& AdditionalData = TMap<FString, FString>());
    
    // Validation Functions
    static bool ValidateNiagaraPath(const FString& NiagaraPath);
    static bool ValidateEmitterType(const FString& EmitterType);
    static bool ValidateModuleType(const FString& ModuleType);
    static bool ValidateParameterType(const FString& ParameterType);
    static bool ValidateOptimizationLevel(const FString& OptimizationLevel);
    
    // Constants for Niagara System
    static const TArray<FString> SupportedEmitterTypes;
    static const TArray<FString> SupportedModuleTypes;
    static const TArray<FString> SupportedParameterTypes;
    static const TArray<FString> SupportedModuleGroups;
    static const TArray<FString> SupportedOptimizationLevels;
    static const TArray<FString> SupportedSimulationTargets;

    // ============================================================================
    // Real Niagara Performance Analysis Functions
    // ============================================================================

    /**
     * Collects real Niagara performance metrics using UE 5.6 APIs.
     */
    static TSharedPtr<FJsonObject> CollectRealNiagaraPerformanceMetrics();

    // Helper functions for Niagara analysis
    static TMap<FString, float> AnalyzeNiagaraSystemComplexity(UNiagaraSystem* NiagaraSystem);
    static float CalculateNiagaraSystemRenderCost(UNiagaraSystem* NiagaraSystem);
};
