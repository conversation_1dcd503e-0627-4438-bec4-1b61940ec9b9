"""
Platform-Specific Tools for Unreal Engine MCP Server

This module provides comprehensive Platform-Specific tools that are 100% compatible with the 
C++ implementations in UnrealMCPPlatformCommands.cpp. Based on UE 5.6 official documentation.
"""

import logging
import json
from typing import Dict, List, Any, Optional
from mcp.server.fastmcp import FastMCP

# Set up logging
logger = logging.getLogger("UnrealMCP")

def register_platform_tools(mcp: FastMCP):
    """Register Platform-Specific tools with the MCP server - 100% compatible with C++ implementations."""

    @mcp.tool()
    def configure_platform_settings(
        platform_name: str,
        quality_level: str = "High",
        material_quality: str = "High",
        texture_quality: str = "High",
        shadow_quality: str = "High",
        enable_vulkan: bool = False
    ) -> Dict[str, Any]:
        """
        Configure platform-specific settings.
        Compatible with HandleConfigurePlatformSettings in C++.
        
        Args:
            platform_name: Target platform (Windows, Mac, Linux, Android, iOS, PlayStation, Xbox, Switch)
            quality_level: Overall quality level (Low, Medium, High, Epic)
            material_quality: Material quality setting
            texture_quality: Texture quality setting
            shadow_quality: Shadow quality setting
            enable_vulkan: Enable Vulkan renderer (where supported)
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "platform_name": platform_name,
                "quality_level": quality_level,
                "material_quality": material_quality,
                "texture_quality": texture_quality,
                "shadow_quality": shadow_quality,
                "enable_vulkan": enable_vulkan
            }

            response = unreal.send_command("configure_platform_settings", params)
            return response or {"success": True, "message": "Platform settings configured successfully"}

        except Exception as e:
            logger.error(f"Error configuring platform settings: {e}")
            return {"success": False, "message": f"Error configuring platform settings: {e}"}

    @mcp.tool()
    def setup_mobile_platform(
        platform: str = "Android",
        sdk_path: str = "",
        min_sdk_version: int = 21,
        target_sdk_version: int = 34,
        enable_arm64: bool = True,
        enable_x86_64: bool = False
    ) -> Dict[str, Any]:
        """
        Setup mobile platform configuration.
        Compatible with HandleSetupMobilePlatform in C++.
        
        Args:
            platform: Mobile platform (Android, iOS)
            sdk_path: Path to platform SDK
            min_sdk_version: Minimum SDK version
            target_sdk_version: Target SDK version
            enable_arm64: Enable ARM64 architecture
            enable_x86_64: Enable x86_64 architecture
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "platform": platform,
                "sdk_path": sdk_path,
                "min_sdk_version": min_sdk_version,
                "target_sdk_version": target_sdk_version,
                "enable_arm64": enable_arm64,
                "enable_x86_64": enable_x86_64
            }

            response = unreal.send_command("setup_mobile_platform", params)
            return response or {"success": True, "message": "Mobile platform setup successfully"}

        except Exception as e:
            logger.error(f"Error setting up mobile platform: {e}")
            return {"success": False, "message": f"Error setting up mobile platform: {e}"}

    @mcp.tool()
    def configure_console_platform(
        console_type: str = "PlayStation5",
        dev_kit_path: str = "",
        enable_dev_mode: bool = True,
        performance_profile: str = "Balanced",
        enable_ray_tracing: bool = False
    ) -> Dict[str, Any]:
        """
        Configure console platform settings.
        Compatible with HandleConfigureConsolePlatform in C++.
        
        Args:
            console_type: Console type (PlayStation4, PlayStation5, Xbox, Switch)
            dev_kit_path: Path to development kit
            enable_dev_mode: Enable development mode
            performance_profile: Performance profile (Performance, Balanced, Quality)
            enable_ray_tracing: Enable ray tracing (where supported)
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "console_type": console_type,
                "dev_kit_path": dev_kit_path,
                "enable_dev_mode": enable_dev_mode,
                "performance_profile": performance_profile,
                "enable_ray_tracing": enable_ray_tracing
            }

            response = unreal.send_command("configure_console_platform", params)
            return response or {"success": True, "message": "Console platform configured successfully"}

        except Exception as e:
            logger.error(f"Error configuring console platform: {e}")
            return {"success": False, "message": f"Error configuring console platform: {e}"}

    @mcp.tool()
    def setup_cross_platform_features(
        enable_cross_play: bool = True,
        enable_cross_progression: bool = True,
        online_subsystem: str = "Steam",
        platform_services: List[str] = None,
        authentication_method: str = "Platform"
    ) -> Dict[str, Any]:
        """
        Setup cross-platform features and services.
        Compatible with HandleSetupCrossPlatformFeatures in C++.
        
        Args:
            enable_cross_play: Enable cross-platform play
            enable_cross_progression: Enable cross-platform progression
            online_subsystem: Online subsystem to use (Steam, EOS, Platform)
            platform_services: List of platform services to enable
            authentication_method: Authentication method (Platform, EOS, Custom)
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "enable_cross_play": enable_cross_play,
                "enable_cross_progression": enable_cross_progression,
                "online_subsystem": online_subsystem,
                "platform_services": platform_services or [],
                "authentication_method": authentication_method
            }

            response = unreal.send_command("setup_cross_platform_features", params)
            return response or {"success": True, "message": "Cross-platform features setup successfully"}

        except Exception as e:
            logger.error(f"Error setting up cross-platform features: {e}")
            return {"success": False, "message": f"Error setting up cross-platform features: {e}"}

    @mcp.tool()
    def optimize_platform_performance(
        target_platform: str,
        target_fps: int = 60,
        memory_budget_mb: int = 2048,
        enable_lod_system: bool = True,
        culling_distance: float = 10000.0,
        texture_streaming: bool = True
    ) -> Dict[str, Any]:
        """
        Optimize performance for specific platform.
        Compatible with HandleOptimizePlatformPerformance in C++.
        
        Args:
            target_platform: Target platform for optimization
            target_fps: Target frame rate
            memory_budget_mb: Memory budget in megabytes
            enable_lod_system: Enable Level of Detail system
            culling_distance: Object culling distance
            texture_streaming: Enable texture streaming
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "target_platform": target_platform,
                "target_fps": target_fps,
                "memory_budget_mb": memory_budget_mb,
                "enable_lod_system": enable_lod_system,
                "culling_distance": culling_distance,
                "texture_streaming": texture_streaming
            }

            response = unreal.send_command("optimize_platform_performance", params)
            return response or {"success": True, "message": "Platform performance optimized successfully"}

        except Exception as e:
            logger.error(f"Error optimizing platform performance: {e}")
            return {"success": False, "message": f"Error optimizing platform performance: {e}"}

    @mcp.tool()
    def configure_platform_input(
        platform: str,
        input_devices: List[str] = None,
        enable_touch: bool = False,
        enable_gamepad: bool = True,
        enable_keyboard_mouse: bool = True,
        custom_input_mappings: Dict[str, str] = None
    ) -> Dict[str, Any]:
        """
        Configure platform-specific input settings.
        Compatible with HandleConfigurePlatformInput in C++.
        
        Args:
            platform: Target platform
            input_devices: List of supported input devices
            enable_touch: Enable touch input
            enable_gamepad: Enable gamepad input
            enable_keyboard_mouse: Enable keyboard and mouse input
            custom_input_mappings: Custom input mappings
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "platform": platform,
                "input_devices": input_devices or [],
                "enable_touch": enable_touch,
                "enable_gamepad": enable_gamepad,
                "enable_keyboard_mouse": enable_keyboard_mouse,
                "custom_input_mappings": custom_input_mappings or {}
            }

            response = unreal.send_command("configure_platform_input", params)
            return response or {"success": True, "message": "Platform input configured successfully"}

        except Exception as e:
            logger.error(f"Error configuring platform input: {e}")
            return {"success": False, "message": f"Error configuring platform input: {e}"}

    @mcp.tool()
    def setup_platform_packaging(
        target_platform: str,
        build_configuration: str = "Shipping",
        architecture: str = "x64",
        compression_method: str = "Default",
        include_prerequisites: bool = True,
        create_installer: bool = False
    ) -> Dict[str, Any]:
        """
        Setup platform-specific packaging configuration.
        Compatible with HandleSetupPlatformPackaging in C++.

        Args:
            target_platform: Target platform for packaging
            build_configuration: Build configuration (Debug, Development, Shipping)
            architecture: Target architecture (x64, ARM64, Universal)
            compression_method: Compression method (None, Default, LZ4, Oodle)
            include_prerequisites: Include platform prerequisites
            create_installer: Create platform installer
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "target_platform": target_platform,
                "build_configuration": build_configuration,
                "architecture": architecture,
                "compression_method": compression_method,
                "include_prerequisites": include_prerequisites,
                "create_installer": create_installer
            }

            response = unreal.send_command("setup_platform_packaging", params)
            return response or {"success": True, "message": "Platform packaging setup successfully"}

        except Exception as e:
            logger.error(f"Error setting up platform packaging: {e}")
            return {"success": False, "message": f"Error setting up platform packaging: {e}"}

    @mcp.tool()
    def manage_platform_store_integration(
        platform: str,
        store_type: str = "Default",
        app_id: str = "",
        enable_achievements: bool = True,
        enable_leaderboards: bool = True,
        enable_cloud_saves: bool = True
    ) -> Dict[str, Any]:
        """
        Manage platform store integration and services.
        Compatible with HandleManagePlatformStoreIntegration in C++.

        Args:
            platform: Target platform (Steam, EpicGames, PlayStation, Xbox, Switch, iOS, Android)
            store_type: Store type (Steam, EGS, PSN, Xbox, Nintendo, AppStore, GooglePlay)
            app_id: Application ID for the store
            enable_achievements: Enable achievements system
            enable_leaderboards: Enable leaderboards
            enable_cloud_saves: Enable cloud save functionality
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "platform": platform,
                "store_type": store_type,
                "app_id": app_id,
                "enable_achievements": enable_achievements,
                "enable_leaderboards": enable_leaderboards,
                "enable_cloud_saves": enable_cloud_saves
            }

            response = unreal.send_command("manage_platform_store_integration", params)
            return response or {"success": True, "message": "Platform store integration managed successfully"}

        except Exception as e:
            logger.error(f"Error managing platform store integration: {e}")
            return {"success": False, "message": f"Error managing platform store integration: {e}"}

    @mcp.tool()
    def analyze_platform_compatibility(
        project_path: str = "",
        target_platforms: List[str] = None,
        check_assets: bool = True,
        check_code: bool = True,
        generate_report: bool = True
    ) -> Dict[str, Any]:
        """
        Analyze project compatibility across platforms.
        Compatible with HandleAnalyzePlatformCompatibility in C++.

        Args:
            project_path: Path to the project to analyze
            target_platforms: List of target platforms to check
            check_assets: Check asset compatibility
            check_code: Check code compatibility
            generate_report: Generate detailed compatibility report
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "project_path": project_path,
                "target_platforms": target_platforms or [],
                "check_assets": check_assets,
                "check_code": check_code,
                "generate_report": generate_report
            }

            response = unreal.send_command("analyze_platform_compatibility", params)
            return response or {"success": True, "message": "Platform compatibility analysis completed successfully"}

        except Exception as e:
            logger.error(f"Error analyzing platform compatibility: {e}")
            return {"success": False, "message": f"Error analyzing platform compatibility: {e}"}
