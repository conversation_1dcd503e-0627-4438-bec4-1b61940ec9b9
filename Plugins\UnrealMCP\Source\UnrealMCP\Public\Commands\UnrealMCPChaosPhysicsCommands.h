// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "GeometryCollection/GeometryCollectionActor.h"
#include "GeometryCollection/GeometryCollectionComponent.h"
#include "GeometryCollection/GeometryCollection.h"
#include "GeometryCollection/GeometryCollectionObject.h"
#include "Chaos/ChaosEngineInterface.h"
#include "Chaos/ChaosSolverActor.h"
#include "Field/FieldSystemActor.h"
#include "Field/FieldSystemComponent.h"
#include "Field/FieldSystemTypes.h"
#include "PhysicsEngine/PhysicsSettings.h"
#include "PhysicsEngine/BodySetup.h"
#include "Components/StaticMeshComponent.h"
#include "Engine/StaticMesh.h"
#include "ClothingAsset.h"
#include "ClothingSimulation.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "UObject/SavePackage.h"
#include "Misc/PackageName.h"
#include "EditorAssetLibrary.h"
#include "GeometryCollection/GeometryCollectionFactory.h"

/**
 * UnrealMCPChaosPhysicsCommands
 * 
 * Handles all Chaos Physics System related commands for the MCP server.
 * Provides comprehensive physics simulation, destruction, and optimization tools
 * compatible with Unreal Engine 5.6 Chaos Physics system.
 * 
 * Features:
 * - Geometry Collection creation and fracturing
 * - Chaos Physics Fields for dynamic forces
 * - Destructible actor spawning and damage
 * - Fluid simulation (Fluxo Prismal integration)
 * - Cloth and rigid body simulations
 * - Multi-threading and GPU acceleration
 * - Performance analysis and optimization
 * - Cross-platform physics configuration
 * - Integration with Niagara particles
 */
class UNREALMCP_API UnrealMCPChaosPhysicsCommands
{
public:
    UnrealMCPChaosPhysicsCommands();
    ~UnrealMCPChaosPhysicsCommands();

    // Core Chaos Physics Management
    static FString HandleCreateGeometryCollection(const FString& JsonParams);
    static FString HandleFractureGeometryCollection(const FString& JsonParams);
    static FString HandleCreateChaosPhysicsField(const FString& JsonParams);
    static FString HandleSpawnDestructibleActor(const FString& JsonParams);
    static FString HandleApplyChaosDamage(const FString& JsonParams);
    static FString HandleConfigureChaosSolver(const FString& JsonParams);

    // Advanced Simulation Types
    static FString HandleCreateFluidSimulation(const FString& JsonParams);
    static FString HandleCreateClothSimulation(const FString& JsonParams);
    static FString HandleCreateRigidBodySimulation(const FString& JsonParams);

    // Performance and Optimization
    static FString HandleOptimizeChaosPerformance(const FString& JsonParams);
    static FString HandleAnalyzeChaosPerformance(const FString& JsonParams);
    static FString HandleConfigureChaosMultithreading(const FString& JsonParams);
    static FString HandleSetupChaosGPUAcceleration(const FString& JsonParams);

private:
    // Helper Functions for Geometry Collections
    static UGeometryCollection* CreateGeometryCollectionAsset(const FString& CollectionName, 
        const FString& PackagePath);
    static bool AddMeshesToGeometryCollection(UGeometryCollection* Collection, 
        const TArray<FString>& MeshPaths);
    static bool FractureGeometryCollectionInternal(UGeometryCollection* Collection, 
        const FString& FractureMethod, int32 FractureCount, int32 RandomSeed);
    
    // Helper Functions for Destructible Actors
    static AGeometryCollectionActor* SpawnGeometryCollectionActor(UGeometryCollection* Collection, 
        const FVector& Location, const FRotator& Rotation);
    static bool ConfigureDestructibleProperties(AGeometryCollectionActor* Actor, 
        bool bEnableDamage, float DamageThreshold);
    static bool ApplyDamageToGeometryCollection(AGeometryCollectionActor* Actor, 
        const FVector& DamageLocation, float DamageRadius, float DamageAmount, float ImpulseStrength);
    
    // Helper Functions for Physics Fields
    static AFieldSystemActor* CreatePhysicsFieldActor(const FString& FieldName, 
        const FString& FieldType, const FVector& Location);
    static UFieldSystemComponent* SetupFieldComponent(AFieldSystemActor* FieldActor, 
        const FString& FieldType, float Radius, float Strength);
    static bool ConfigureRadialForceField(UFieldSystemComponent* FieldComponent, 
        float Radius, float Strength);
    static bool ConfigureDirectionalForceField(UFieldSystemComponent* FieldComponent, 
        const FVector& Direction, float Strength);
    
    // Helper Functions for Solver Configuration
    static AChaosSolverActor* GetOrCreateChaosSolver(const FString& SolverName);
    static bool ConfigureSolverSettings(AChaosSolverActor* Solver, const FVector& Gravity, 
        float Damping, int32 MaxSubsteps, bool bEnableMultithreading);
    static bool SetupSolverCollisionSettings(AChaosSolverActor* Solver);
    
    // Helper Functions for Fluid Simulation
    static UObject* CreateFluidSimulationAsset(const FString& SimulationName, 
        const FString& PackagePath, const FString& FluidType);
    static bool ConfigureFluidProperties(UObject* FluidAsset, int32 GridResolution, 
        float Viscosity);
    static bool SetupFluidBoundaries(UObject* FluidAsset);
    
    // Helper Functions for Cloth Simulation
    static UClothingAssetBase* CreateClothAsset(const FString& ClothName, 
        const FString& PackagePath, UStaticMesh* SourceMesh);
    static bool ConfigureClothProperties(UClothingAssetBase* ClothAsset, 
        float Stiffness, float Damping);
    static bool SetupClothConstraints(UClothingAssetBase* ClothAsset);
    
    // Helper Functions for Rigid Body Simulation
    static UStaticMeshComponent* CreateRigidBodyComponent(UStaticMesh* Mesh, 
        float Mass, float LinearDamping, float AngularDamping);
    static bool ConfigureRigidBodyPhysics(UStaticMeshComponent* Component);
    static bool SetupRigidBodyConstraints(UStaticMeshComponent* Component);
    
    // Helper Functions for Performance Optimization
    static void OptimizePhysicsForPlatform(const FString& Platform, 
        const FString& OptimizationLevel);
    static void ConfigurePhysicsLOD(const FString& OptimizationLevel);
    static void SetupPhysicsCulling(const FString& OptimizationLevel);
    static void EnableGPUAcceleration(bool bEnable, int32 MaxParticles);
    static void ConfigureMultithreading(bool bEnable, int32 ThreadCount);
    
    // Helper Functions for Performance Analysis
    static TSharedPtr<FJsonObject> CollectPhysicsMetrics(float Duration);
    static TSharedPtr<FJsonObject> AnalyzeMemoryUsage();
    static TSharedPtr<FJsonObject> AnalyzeTimingStats();
    static TSharedPtr<FJsonObject> GeneratePerformanceReport(
        const TSharedPtr<FJsonObject>& Metrics);
    
    // Utility Functions
    static FString ChaosPathToPackageName(const FString& ChaosPath);
    static UGeometryCollection* LoadGeometryCollectionFromPath(const FString& CollectionPath);
    static AGeometryCollectionActor* FindGeometryCollectionActorByName(const FString& ActorName);
    static bool SaveChaosAsset(UObject* Asset, const FString& PackagePath);
    static FString GetChaosErrorMessage(const FString& ErrorCode);
    
    // JSON Parsing Helpers
    static TSharedPtr<FJsonObject> ParseJsonParams(const FString& JsonParams);
    static FString CreateJsonResponse(bool bSuccess, const FString& Message, 
        const TMap<FString, FString>& AdditionalData = TMap<FString, FString>());
    
    // Validation Functions
    static bool ValidateChaosPath(const FString& ChaosPath);
    static bool ValidateFractureMethod(const FString& FractureMethod);
    static bool ValidateFieldType(const FString& FieldType);
    static bool ValidateFluidType(const FString& FluidType);
    static bool ValidateOptimizationLevel(const FString& OptimizationLevel);

    // ============================================================================
    // Real Chaos Physics Performance Analysis Functions
    // ============================================================================

    /**
     * Collects real Chaos physics performance metrics using UE 5.6 APIs.
     */
    static TSharedPtr<FJsonObject> CollectRealChaosPerformanceMetrics();

    // ============================================================================
    // Real Geometry Collection Fracturing Functions
    // ============================================================================

    /**
     * Creates real geometry collection with advanced fracturing using UE 5.6 APIs.
     */
    static TSharedPtr<FJsonObject> CreateRealGeometryCollectionWithFracturing(
        const FString& CollectionName,
        const TArray<FString>& MeshPaths,
        const FString& FractureMethod,
        int32 FractureCount,
        int32 RandomSeed);

    /**
     * Adds real meshes to geometry collection using UE 5.6 APIs.
     */
    static bool AddRealMeshesToGeometryCollection(UGeometryCollection* Collection, const TArray<FString>& MeshPaths);

    /**
     * Applies real fracturing to geometry collection using UE 5.6 fracture algorithms.
     */
    static bool ApplyRealFracturingToCollection(UGeometryCollection* Collection, const FString& FractureMethod, int32 FractureCount, int32 RandomSeed);

    // Fracturing algorithm implementations
    static bool ApplyVoronoiFracturing(FGeometryCollection* GeometryCollection, int32 FractureCount, int32 RandomSeed);
    static bool ApplyUniformFracturing(FGeometryCollection* GeometryCollection, int32 FractureCount, int32 RandomSeed);
    static bool ApplyClusteredFracturing(FGeometryCollection* GeometryCollection, int32 FractureCount, int32 RandomSeed);
    static bool ApplyRadialFracturing(FGeometryCollection* GeometryCollection, int32 FractureCount, int32 RandomSeed);
    static bool ApplyPlanarFracturing(FGeometryCollection* GeometryCollection, int32 FractureCount, int32 RandomSeed);

    // Helper functions for geometry collection operations
    static FBox GetGeometryCollectionBounds(FGeometryCollection* GeometryCollection);
    static void ConfigureGeometryCollectionPhysics(UGeometryCollection* Collection);
    static TSharedPtr<FJsonObject> GetGeometryCollectionStatistics(UGeometryCollection* Collection);
    
    // Constants for Chaos Physics System
    static const TArray<FString> SupportedFractureMethods;
    static const TArray<FString> SupportedFieldTypes;
    static const TArray<FString> SupportedFluidTypes;
    static const TArray<FString> SupportedOptimizationLevels;
    static const TArray<FString> SupportedPlatforms;
    static const TArray<FString> SupportedOutputFormats;
};
