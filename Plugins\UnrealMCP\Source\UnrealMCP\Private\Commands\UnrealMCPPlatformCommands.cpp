// Copyright Epic Games, Inc. All Rights Reserved.

#include "Commands/UnrealMCPPlatformCommands.h"
#include "Dom/JsonObject.h"
#include "Serialization/JsonSerializer.h"
#include "Serialization/JsonWriter.h"
#include "Engine/World.h"
#include "Misc/DateTime.h"
#include "Misc/FileHelper.h"
#include "Misc/Paths.h"
#include "HAL/PlatformMemory.h"

// Initialize static constants
const TArray<FString> UnrealMCPPlatformCommands::SupportedPlatforms = {
    TEXT("Windows"),
    TEXT("Mac"),
    TEXT("Linux"),
    TEXT("Android"),
    TEXT("iOS"),
    TEXT("PlayStation4"),
    TEXT("PlayStation5"),
    TEXT("Xbox"),
    TEXT("Switch")
};

const TArray<FString> UnrealMCPPlatformCommands::SupportedQualityLevels = {
    TEXT("Low"),
    TEXT("Medium"),
    TEXT("High"),
    TEXT("Epic")
};

const TArray<FString> UnrealMCPPlatformCommands::SupportedBuildConfigurations = {
    TEXT("Debug"),
    TEXT("Development"),
    TEXT("Shipping")
};

const TArray<FString> UnrealMCPPlatformCommands::SupportedArchitectures = {
    TEXT("x64"),
    TEXT("ARM64"),
    TEXT("Universal")
};

const TArray<FString> UnrealMCPPlatformCommands::SupportedCompressionMethods = {
    TEXT("None"),
    TEXT("Default"),
    TEXT("LZ4"),
    TEXT("Oodle")
};

const TArray<FString> UnrealMCPPlatformCommands::SupportedOnlineSubsystems = {
    TEXT("Steam"),
    TEXT("EOS"),
    TEXT("Platform"),
    TEXT("Null")
};

const TArray<FString> UnrealMCPPlatformCommands::SupportedStoreTypes = {
    TEXT("Steam"),
    TEXT("EGS"),
    TEXT("PSN"),
    TEXT("Xbox"),
    TEXT("Nintendo"),
    TEXT("AppStore"),
    TEXT("GooglePlay")
};

const TArray<FString> UnrealMCPPlatformCommands::MobilePlatforms = {
    TEXT("Android"),
    TEXT("iOS")
};

const TArray<FString> UnrealMCPPlatformCommands::ConsolePlatforms = {
    TEXT("PlayStation4"),
    TEXT("PlayStation5"),
    TEXT("Xbox"),
    TEXT("Switch")
};

const TArray<FString> UnrealMCPPlatformCommands::DesktopPlatforms = {
    TEXT("Windows"),
    TEXT("Mac"),
    TEXT("Linux")
};

const int32 UnrealMCPPlatformCommands::DefaultTargetFPS = 60;
const int32 UnrealMCPPlatformCommands::DefaultMemoryBudgetMB = 2048;
const float UnrealMCPPlatformCommands::DefaultCullingDistance = 10000.0f;
const int32 UnrealMCPPlatformCommands::DefaultTexturePoolSizeMB = 512;
const int32 UnrealMCPPlatformCommands::DefaultMinSDKVersion = 21;
const int32 UnrealMCPPlatformCommands::DefaultTargetSDKVersion = 34;

UnrealMCPPlatformCommands::UnrealMCPPlatformCommands()
{
}

UnrealMCPPlatformCommands::~UnrealMCPPlatformCommands()
{
}

FString UnrealMCPPlatformCommands::HandleConfigurePlatformSettings(const FString& JsonParams)
{
    TSharedPtr<FJsonObject> JsonObject = ParseJsonParams(JsonParams);
    if (!JsonObject.IsValid())
    {
        return CreateJsonResponse(false, TEXT("Invalid JSON parameters"));
    }

    FString PlatformName;
    if (!JsonObject->TryGetStringField(TEXT("platform_name"), PlatformName) || PlatformName.IsEmpty())
    {
        return CreateJsonResponse(false, TEXT("Platform name is required"));
    }

    FString QualityLevel = TEXT("High");
    JsonObject->TryGetStringField(TEXT("quality_level"), QualityLevel);

    FString MaterialQuality = TEXT("High");
    JsonObject->TryGetStringField(TEXT("material_quality"), MaterialQuality);

    FString TextureQuality = TEXT("High");
    JsonObject->TryGetStringField(TEXT("texture_quality"), TextureQuality);

    FString ShadowQuality = TEXT("High");
    JsonObject->TryGetStringField(TEXT("shadow_quality"), ShadowQuality);

    bool bEnableVulkan = false;
    JsonObject->TryGetBoolField(TEXT("enable_vulkan"), bEnableVulkan);

    // Validate parameters
    if (!ValidatePlatformName(PlatformName))
    {
        return CreateJsonResponse(false, FString::Printf(TEXT("Unsupported platform: %s"), *PlatformName));
    }

    if (!ValidateQualityLevel(QualityLevel))
    {
        return CreateJsonResponse(false, FString::Printf(TEXT("Invalid quality level: %s"), *QualityLevel));
    }

    // Configure platform settings
    if (!ConfigurePlatformQualitySettings(PlatformName, QualityLevel, MaterialQuality, TextureQuality, ShadowQuality))
    {
        return CreateJsonResponse(false, TEXT("Failed to configure platform quality settings"));
    }

    if (bEnableVulkan && !EnableVulkanRenderer(PlatformName, true))
    {
        return CreateJsonResponse(false, TEXT("Failed to enable Vulkan renderer"));
    }

    TMap<FString, FString> ResponseData;
    ResponseData.Add(TEXT("platform_name"), PlatformName);
    ResponseData.Add(TEXT("quality_level"), QualityLevel);
    ResponseData.Add(TEXT("vulkan_enabled"), bEnableVulkan ? TEXT("true") : TEXT("false"));

    return CreateJsonResponse(true, TEXT("Platform settings configured successfully"), ResponseData);
}

FString UnrealMCPPlatformCommands::HandleSetupMobilePlatform(const FString& JsonParams)
{
    TSharedPtr<FJsonObject> JsonObject = ParseJsonParams(JsonParams);
    if (!JsonObject.IsValid())
    {
        return CreateJsonResponse(false, TEXT("Invalid JSON parameters"));
    }

    FString Platform = TEXT("Android");
    JsonObject->TryGetStringField(TEXT("platform"), Platform);

    FString SDKPath;
    JsonObject->TryGetStringField(TEXT("sdk_path"), SDKPath);

    int32 MinSDKVersion = DefaultMinSDKVersion;
    JsonObject->TryGetNumberField(TEXT("min_sdk_version"), MinSDKVersion);

    int32 TargetSDKVersion = DefaultTargetSDKVersion;
    JsonObject->TryGetNumberField(TEXT("target_sdk_version"), TargetSDKVersion);

    bool bEnableARM64 = true;
    JsonObject->TryGetBoolField(TEXT("enable_arm64"), bEnableARM64);

    bool bEnableX86_64 = false;
    JsonObject->TryGetBoolField(TEXT("enable_x86_64"), bEnableX86_64);

    // Validate mobile platform
    if (!IsMobilePlatform(Platform))
    {
        return CreateJsonResponse(false, FString::Printf(TEXT("Not a mobile platform: %s"), *Platform));
    }

    // Configure mobile SDK
    if (!SDKPath.IsEmpty() && !ConfigureMobileSDK(Platform, SDKPath, MinSDKVersion, TargetSDKVersion))
    {
        return CreateJsonResponse(false, TEXT("Failed to configure mobile SDK"));
    }

    // Setup mobile architectures
    if (!SetupMobileArchitectures(Platform, bEnableARM64, bEnableX86_64))
    {
        return CreateJsonResponse(false, TEXT("Failed to setup mobile architectures"));
    }

    // Configure mobile rendering
    bool bEnableVulkan = Platform == TEXT("Android");
    bool bEnableMetal = Platform == TEXT("iOS");
    if (!ConfigureMobileRendering(Platform, bEnableVulkan, bEnableMetal))
    {
        return CreateJsonResponse(false, TEXT("Failed to configure mobile rendering"));
    }

    TMap<FString, FString> ResponseData;
    ResponseData.Add(TEXT("platform"), Platform);
    ResponseData.Add(TEXT("min_sdk_version"), FString::FromInt(MinSDKVersion));
    ResponseData.Add(TEXT("target_sdk_version"), FString::FromInt(TargetSDKVersion));
    ResponseData.Add(TEXT("arm64_enabled"), bEnableARM64 ? TEXT("true") : TEXT("false"));

    return CreateJsonResponse(true, TEXT("Mobile platform setup successfully"), ResponseData);
}

FString UnrealMCPPlatformCommands::HandleConfigureConsolePlatform(const FString& JsonParams)
{
    TSharedPtr<FJsonObject> JsonObject = ParseJsonParams(JsonParams);
    if (!JsonObject.IsValid())
    {
        return CreateJsonResponse(false, TEXT("Invalid JSON parameters"));
    }

    FString ConsoleType = TEXT("PlayStation5");
    JsonObject->TryGetStringField(TEXT("console_type"), ConsoleType);

    FString DevKitPath;
    JsonObject->TryGetStringField(TEXT("dev_kit_path"), DevKitPath);

    bool bEnableDevMode = true;
    JsonObject->TryGetBoolField(TEXT("enable_dev_mode"), bEnableDevMode);

    FString PerformanceProfile = TEXT("Balanced");
    JsonObject->TryGetStringField(TEXT("performance_profile"), PerformanceProfile);

    bool bEnableRayTracing = false;
    JsonObject->TryGetBoolField(TEXT("enable_ray_tracing"), bEnableRayTracing);

    // Validate console platform
    if (!IsConsolePlatform(ConsoleType))
    {
        return CreateJsonResponse(false, FString::Printf(TEXT("Not a console platform: %s"), *ConsoleType));
    }

    // Setup development kit
    if (!DevKitPath.IsEmpty() && !SetupConsoleDevelopmentKit(ConsoleType, DevKitPath))
    {
        return CreateJsonResponse(false, TEXT("Failed to setup console development kit"));
    }

    // Configure performance profile
    if (!ConfigureConsolePerformanceProfile(ConsoleType, PerformanceProfile))
    {
        return CreateJsonResponse(false, TEXT("Failed to configure console performance profile"));
    }

    // Enable ray tracing if supported
    if (bEnableRayTracing && !EnableConsoleRayTracing(ConsoleType, true))
    {
        return CreateJsonResponse(false, TEXT("Failed to enable console ray tracing"));
    }

    TMap<FString, FString> ResponseData;
    ResponseData.Add(TEXT("console_type"), ConsoleType);
    ResponseData.Add(TEXT("performance_profile"), PerformanceProfile);
    ResponseData.Add(TEXT("ray_tracing_enabled"), bEnableRayTracing ? TEXT("true") : TEXT("false"));

    return CreateJsonResponse(true, TEXT("Console platform configured successfully"), ResponseData);
}

// Helper function implementations
bool UnrealMCPPlatformCommands::ConfigurePlatformQualitySettings(const FString& PlatformName,
    const FString& QualityLevel, const FString& MaterialQuality, const FString& TextureQuality,
    const FString& ShadowQuality)
{
    UDeviceProfile* DeviceProfile = GetOrCreateDeviceProfile(PlatformName);
    if (!DeviceProfile)
    {
        return false;
    }

    // Configure quality settings based on platform
    TMap<FString, FString> QualitySettings;

    // Set overall quality level
    int32 QualityIndex = 2; // High by default
    if (QualityLevel == TEXT("Low")) QualityIndex = 0;
    else if (QualityLevel == TEXT("Medium")) QualityIndex = 1;
    else if (QualityLevel == TEXT("High")) QualityIndex = 2;
    else if (QualityLevel == TEXT("Epic")) QualityIndex = 3;

    QualitySettings.Add(TEXT("sg.ResolutionQuality"), FString::FromInt(QualityIndex));
    QualitySettings.Add(TEXT("sg.ViewDistanceQuality"), FString::FromInt(QualityIndex));
    QualitySettings.Add(TEXT("sg.AntiAliasingQuality"), FString::FromInt(QualityIndex));
    QualitySettings.Add(TEXT("sg.ShadowQuality"), FString::FromInt(QualityIndex));
    QualitySettings.Add(TEXT("sg.PostProcessQuality"), FString::FromInt(QualityIndex));
    QualitySettings.Add(TEXT("sg.TextureQuality"), FString::FromInt(QualityIndex));
    QualitySettings.Add(TEXT("sg.EffectsQuality"), FString::FromInt(QualityIndex));
    QualitySettings.Add(TEXT("sg.FoliageQuality"), FString::FromInt(QualityIndex));
    QualitySettings.Add(TEXT("sg.ShadingQuality"), FString::FromInt(QualityIndex));

    return ApplyPlatformSpecificSettings(PlatformName, QualitySettings);
}

bool UnrealMCPPlatformCommands::EnableVulkanRenderer(const FString& PlatformName, bool bEnable)
{
    // Check if platform supports Vulkan
    if (PlatformName == TEXT("Android") || PlatformName == TEXT("Windows") || PlatformName == TEXT("Linux"))
    {
        FString ConfigPath = GetPlatformConfigPath(PlatformName);
        if (!ConfigPath.IsEmpty())
        {
            // This would typically modify the platform's config files
            // For now, we'll return true to indicate the concept is supported
            return true;
        }
    }
    return false;
}

UDeviceProfile* UnrealMCPPlatformCommands::GetOrCreateDeviceProfile(const FString& PlatformName)
{
    UDeviceProfileManager& DeviceProfileManager = UDeviceProfileManager::Get();

    // Try to find existing device profile
    UDeviceProfile* DeviceProfile = DeviceProfileManager.FindProfile(PlatformName);

    if (!DeviceProfile)
    {
        // Create new device profile if it doesn't exist
        DeviceProfile = DeviceProfileManager.CreateProfile(PlatformName, TEXT(""), TEXT(""));
    }

    return DeviceProfile;
}

bool UnrealMCPPlatformCommands::ApplyPlatformSpecificSettings(const FString& PlatformName,
    const TMap<FString, FString>& Settings)
{
    UDeviceProfile* DeviceProfile = GetOrCreateDeviceProfile(PlatformName);
    if (!DeviceProfile)
    {
        return false;
    }

    // Apply settings to device profile
    for (const auto& Setting : Settings)
    {
        DeviceProfile->ModifyCVarValue(*Setting.Key, Setting.Value, true);
    }

    return true;
}

bool UnrealMCPPlatformCommands::ConfigureMobileSDK(const FString& Platform, const FString& SDKPath,
    int32 MinSDKVersion, int32 TargetSDKVersion)
{
    if (Platform == TEXT("Android"))
    {
        // Configure Android SDK settings
        GConfig->SetString(TEXT("/Script/AndroidRuntimeSettings.AndroidRuntimeSettings"),
            TEXT("SDKAPILevel"), *FString::FromInt(TargetSDKVersion), GEngineIni);
        GConfig->SetString(TEXT("/Script/AndroidRuntimeSettings.AndroidRuntimeSettings"),
            TEXT("NDKAPILevel"), *FString::FromInt(MinSDKVersion), GEngineIni);
        return true;
    }
    else if (Platform == TEXT("iOS"))
    {
        // Configure iOS SDK settings
        GConfig->SetString(TEXT("/Script/IOSRuntimeSettings.IOSRuntimeSettings"),
            TEXT("MinimumiOSVersion"), *FString::FromInt(MinSDKVersion), GEngineIni);
        return true;
    }

    return false;
}

bool UnrealMCPPlatformCommands::SetupMobileArchitectures(const FString& Platform, bool bEnableARM64, bool bEnableX86_64)
{
    if (Platform == TEXT("Android"))
    {
        TArray<FString> Architectures;
        if (bEnableARM64) Architectures.Add(TEXT("arm64"));
        if (bEnableX86_64) Architectures.Add(TEXT("x64"));

        FString ArchitectureString = FString::Join(Architectures, TEXT(","));
        GConfig->SetString(TEXT("/Script/AndroidRuntimeSettings.AndroidRuntimeSettings"),
            TEXT("PackageForOculusMobile"), *ArchitectureString, GEngineIni);
        return true;
    }
    else if (Platform == TEXT("iOS"))
    {
        // iOS typically uses ARM64 by default
        GConfig->SetBool(TEXT("/Script/IOSRuntimeSettings.IOSRuntimeSettings"),
            TEXT("bSupportsPortraitOrientation"), bEnableARM64, GEngineIni);
        return true;
    }

    return false;
}

bool UnrealMCPPlatformCommands::ConfigureMobileRendering(const FString& Platform, bool bEnableVulkan, bool bEnableMetal)
{
    if (Platform == TEXT("Android") && bEnableVulkan)
    {
        GConfig->SetBool(TEXT("/Script/AndroidRuntimeSettings.AndroidRuntimeSettings"),
            TEXT("bSupportsVulkan"), true, GEngineIni);
        return true;
    }
    else if (Platform == TEXT("iOS") && bEnableMetal)
    {
        GConfig->SetBool(TEXT("/Script/IOSRuntimeSettings.IOSRuntimeSettings"),
            TEXT("bSupportsMetal"), true, GEngineIni);
        return true;
    }

    return false;
}

bool UnrealMCPPlatformCommands::SetupConsoleDevelopmentKit(const FString& ConsoleType, const FString& DevKitPath)
{
    // This would typically configure console-specific development kit paths
    // Implementation would depend on specific console SDKs
    return !DevKitPath.IsEmpty() && FPaths::DirectoryExists(DevKitPath);
}

bool UnrealMCPPlatformCommands::ConfigureConsolePerformanceProfile(const FString& ConsoleType, const FString& Profile)
{
    UDeviceProfile* DeviceProfile = GetOrCreateDeviceProfile(ConsoleType);
    if (!DeviceProfile)
    {
        return false;
    }

    // Configure performance settings based on profile
    if (Profile == TEXT("Performance"))
    {
        DeviceProfile->ModifyCVarValue(TEXT("r.ScreenPercentage"), TEXT("90.0"), true);
        DeviceProfile->ModifyCVarValue(TEXT("sg.ShadowQuality"), TEXT("1.0"), true);
        DeviceProfile->ModifyCVarValue(TEXT("sg.PostProcessQuality"), TEXT("1.0"), true);
    }
    else if (Profile == TEXT("Quality"))
    {
        DeviceProfile->ModifyCVarValue(TEXT("r.ScreenPercentage"), TEXT("100.0"), true);
        DeviceProfile->ModifyCVarValue(TEXT("sg.ShadowQuality"), TEXT("3.0"), true);
        DeviceProfile->ModifyCVarValue(TEXT("sg.PostProcessQuality"), TEXT("3.0"), true);
    }
    else // Balanced
    {
        DeviceProfile->ModifyCVarValue(TEXT("r.ScreenPercentage"), TEXT("95.0"), true);
        DeviceProfile->ModifyCVarValue(TEXT("sg.ShadowQuality"), TEXT("2.0"), true);
        DeviceProfile->ModifyCVarValue(TEXT("sg.PostProcessQuality"), TEXT("2.0"), true);
    }

    return true;
}

bool UnrealMCPPlatformCommands::EnableConsoleRayTracing(const FString& ConsoleType, bool bEnable)
{
    // Ray tracing is supported on PlayStation 5 and Xbox Series X/S
    if (ConsoleType == TEXT("PlayStation5") || ConsoleType == TEXT("Xbox"))
    {
        UDeviceProfile* DeviceProfile = GetOrCreateDeviceProfile(ConsoleType);
        if (DeviceProfile)
        {
            DeviceProfile->ModifyCVarValue(TEXT("r.RayTracing"), bEnable ? TEXT("1.0") : TEXT("0.0"), true);
            DeviceProfile->ModifyCVarValue(TEXT("r.RayTracing.Reflections"), bEnable ? TEXT("1.0") : TEXT("0.0"), true);
            DeviceProfile->ModifyCVarValue(TEXT("r.RayTracing.GlobalIllumination"), bEnable ? TEXT("1.0") : TEXT("0.0"), true);
            return true;
        }
    }

    return false;
}

// Utility function implementations
FString UnrealMCPPlatformCommands::GetCurrentPlatformName()
{
#if PLATFORM_WINDOWS
    return TEXT("Windows");
#elif PLATFORM_MAC
    return TEXT("Mac");
#elif PLATFORM_LINUX
    return TEXT("Linux");
#elif PLATFORM_ANDROID
    return TEXT("Android");
#elif PLATFORM_IOS
    return TEXT("iOS");
#else
    return TEXT("Unknown");
#endif
}

TArray<FString> UnrealMCPPlatformCommands::GetSupportedPlatforms()
{
    return SupportedPlatforms;
}

bool UnrealMCPPlatformCommands::IsPlatformSupported(const FString& PlatformName)
{
    return SupportedPlatforms.Contains(PlatformName);
}

bool UnrealMCPPlatformCommands::IsMobilePlatform(const FString& PlatformName)
{
    return MobilePlatforms.Contains(PlatformName);
}

bool UnrealMCPPlatformCommands::IsConsolePlatform(const FString& PlatformName)
{
    return ConsolePlatforms.Contains(PlatformName);
}

bool UnrealMCPPlatformCommands::IsDesktopPlatform(const FString& PlatformName)
{
    return DesktopPlatforms.Contains(PlatformName);
}

FString UnrealMCPPlatformCommands::GetPlatformSDKPath(const FString& PlatformName)
{
    FString SDKPath;

    if (PlatformName == TEXT("Android"))
    {
        GConfig->GetString(TEXT("/Script/AndroidRuntimeSettings.AndroidRuntimeSettings"),
            TEXT("SDKPath"), SDKPath, GEngineIni);
    }
    else if (PlatformName == TEXT("iOS"))
    {
        // iOS SDK path is typically handled by Xcode
        SDKPath = TEXT("/Applications/Xcode.app/Contents/Developer");
    }

    return SDKPath;
}

FString UnrealMCPPlatformCommands::GetPlatformConfigPath(const FString& PlatformName)
{
    return FPaths::Combine(FPaths::ProjectConfigDir(), TEXT("Platform"), PlatformName);
}

TSharedPtr<FJsonObject> UnrealMCPPlatformCommands::ParseJsonParams(const FString& JsonParams)
{
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonParams);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return nullptr;
    }

    return JsonObject;
}

FString UnrealMCPPlatformCommands::CreateJsonResponse(bool bSuccess, const FString& Message,
    const TMap<FString, FString>& AdditionalData)
{
    TSharedPtr<FJsonObject> ResponseObject = MakeShareable(new FJsonObject);
    ResponseObject->SetBoolField(TEXT("success"), bSuccess);
    ResponseObject->SetStringField(TEXT("message"), Message);

    if (AdditionalData.Num() > 0)
    {
        TSharedPtr<FJsonObject> DataObject = MakeShareable(new FJsonObject);
        for (const auto& Pair : AdditionalData)
        {
            DataObject->SetStringField(Pair.Key, Pair.Value);
        }
        ResponseObject->SetObjectField(TEXT("data"), DataObject);
    }

    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    FJsonSerializer::Serialize(ResponseObject.ToSharedRef(), Writer);

    return OutputString;
}

FString UnrealMCPPlatformCommands::CreateJsonResponse(bool bSuccess, const FString& Message,
    const TSharedPtr<FJsonObject>& DataObject)
{
    TSharedPtr<FJsonObject> ResponseObject = MakeShareable(new FJsonObject);
    ResponseObject->SetBoolField(TEXT("success"), bSuccess);
    ResponseObject->SetStringField(TEXT("message"), Message);

    if (DataObject.IsValid())
    {
        ResponseObject->SetObjectField(TEXT("data"), DataObject);
    }

    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);
    FJsonSerializer::Serialize(ResponseObject.ToSharedRef(), Writer);

    return OutputString;
}

bool UnrealMCPPlatformCommands::ValidatePlatformName(const FString& PlatformName)
{
    return SupportedPlatforms.Contains(PlatformName);
}

bool UnrealMCPPlatformCommands::ValidateQualityLevel(const FString& QualityLevel)
{
    return SupportedQualityLevels.Contains(QualityLevel);
}

bool UnrealMCPPlatformCommands::ValidateBuildConfiguration(const FString& BuildConfig)
{
    return SupportedBuildConfigurations.Contains(BuildConfig);
}

bool UnrealMCPPlatformCommands::ValidateArchitecture(const FString& Architecture)
{
    return SupportedArchitectures.Contains(Architecture);
}

bool UnrealMCPPlatformCommands::ValidateCompressionMethod(const FString& CompressionMethod)
{
    return SupportedCompressionMethods.Contains(CompressionMethod);
}

bool UnrealMCPPlatformCommands::ValidateOnlineSubsystem(const FString& SubsystemName)
{
    return SupportedOnlineSubsystems.Contains(SubsystemName);
}

bool UnrealMCPPlatformCommands::ValidateStoreType(const FString& StoreType)
{
    return SupportedStoreTypes.Contains(StoreType);
}

FString UnrealMCPPlatformCommands::HandleSetupCrossPlatformFeatures(const FString& JsonString)
{
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return TEXT("{\"success\": false, \"error\": \"Invalid JSON\"}");
    }

    bool bEnableCrossPlay = JsonObject->GetBoolField(TEXT("enable_cross_play"));
    bool bEnableCloudSave = JsonObject->GetBoolField(TEXT("enable_cloud_save"));
    bool bEnableAchievements = JsonObject->GetBoolField(TEXT("enable_achievements"));
    bool bEnableLeaderboards = JsonObject->GetBoolField(TEXT("enable_leaderboards"));
    FString OnlineSubsystem = JsonObject->GetStringField(TEXT("online_subsystem"));

    // Configure cross-platform features
    if (bEnableCrossPlay)
    {
        // Enable cross-platform play
        GConfig->SetBool(TEXT("/Script/Engine.GameSession"), TEXT("bRequiresPushToTalk"), false, GEngineIni);
        GConfig->SetBool(TEXT("/Script/OnlineSubsystemUtils.OnlineEngineInterface"), TEXT("bHasVoiceEnabled"), true, GEngineIni);
    }

    // Configure online subsystem
    if (!OnlineSubsystem.IsEmpty() && ValidateOnlineSubsystem(OnlineSubsystem))
    {
        GConfig->SetString(TEXT("OnlineSubsystem"), TEXT("DefaultPlatformService"), *OnlineSubsystem, GEngineIni);

        // Configure platform-specific settings
        if (OnlineSubsystem == TEXT("Steam"))
        {
            GConfig->SetString(TEXT("OnlineSubsystemSteam"), TEXT("bEnabled"), TEXT("true"), GEngineIni);
            GConfig->SetString(TEXT("OnlineSubsystemSteam"), TEXT("SteamDevAppId"), TEXT("480"), GEngineIni);
        }
        else if (OnlineSubsystem == TEXT("EOS"))
        {
            GConfig->SetString(TEXT("OnlineSubsystemEOS"), TEXT("bEnabled"), TEXT("true"), GEngineIni);
        }
    }

    // Configure cloud save
    if (bEnableCloudSave)
    {
        GConfig->SetBool(TEXT("/Script/Engine.GameUserSettings"), TEXT("bUseCloudStorage"), true, GGameUserSettingsIni);
    }

    // Configure achievements system
    if (bEnableAchievements)
    {
        GConfig->SetBool(TEXT("OnlineSubsystem"), TEXT("bHasAchievementsInterface"), true, GEngineIni);
    }

    // Configure leaderboards
    if (bEnableLeaderboards)
    {
        GConfig->SetBool(TEXT("OnlineSubsystem"), TEXT("bHasLeaderboardInterface"), true, GEngineIni);
    }

    // Save configuration changes
    GConfig->Flush(false, GEngineIni);
    GConfig->Flush(false, GGameUserSettingsIni);

    return TEXT("{\"success\": true, \"message\": \"Cross-platform features configured successfully\"}");
}

FString UnrealMCPPlatformCommands::HandleOptimizePlatformPerformance(const FString& JsonString)
{
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return TEXT("{\"success\": false, \"error\": \"Invalid JSON\"}");
    }

    FString TargetPlatform = JsonObject->GetStringField(TEXT("target_platform"));
    FString PerformanceLevel = JsonObject->GetStringField(TEXT("performance_level"));

    // Apply platform-specific optimizations
    if (TargetPlatform == TEXT("Mobile"))
    {
        // Mobile optimizations
        if (PerformanceLevel == TEXT("Low"))
        {
            GConfig->SetInt(TEXT("/Script/Engine.RendererSettings"), TEXT("r.MobileContentScaleFactor"), 0.5f, GEngineIni);
            GConfig->SetInt(TEXT("/Script/Engine.RendererSettings"), TEXT("r.Mobile.AntiAliasing"), 0, GEngineIni);
            GConfig->SetBool(TEXT("/Script/Engine.RendererSettings"), TEXT("r.Mobile.DisableVertexFog"), true, GEngineIni);
        }
        else if (PerformanceLevel == TEXT("Medium"))
        {
            GConfig->SetFloat(TEXT("/Script/Engine.RendererSettings"), TEXT("r.MobileContentScaleFactor"), 0.75f, GEngineIni);
            GConfig->SetInt(TEXT("/Script/Engine.RendererSettings"), TEXT("r.Mobile.AntiAliasing"), 1, GEngineIni);
            GConfig->SetBool(TEXT("/Script/Engine.RendererSettings"), TEXT("r.Mobile.DisableVertexFog"), false, GEngineIni);
        }
        else if (PerformanceLevel == TEXT("High"))
        {
            GConfig->SetFloat(TEXT("/Script/Engine.RendererSettings"), TEXT("r.MobileContentScaleFactor"), 1.0f, GEngineIni);
            GConfig->SetInt(TEXT("/Script/Engine.RendererSettings"), TEXT("r.Mobile.AntiAliasing"), 2, GEngineIni);
            GConfig->SetBool(TEXT("/Script/Engine.RendererSettings"), TEXT("r.Mobile.AllowDitheredLODTransition"), true, GEngineIni);
        }
    }
    else if (TargetPlatform == TEXT("Console"))
    {
        // Console optimizations
        if (PerformanceLevel == TEXT("Performance"))
        {
            GConfig->SetInt(TEXT("/Script/Engine.RendererSettings"), TEXT("r.DefaultFeature.AntiAliasing"), 2, GEngineIni);
            GConfig->SetFloat(TEXT("/Script/Engine.RendererSettings"), TEXT("r.ScreenPercentage"), 90.0f, GEngineIni);
        }
        else if (PerformanceLevel == TEXT("Quality"))
        {
            GConfig->SetInt(TEXT("/Script/Engine.RendererSettings"), TEXT("r.DefaultFeature.AntiAliasing"), 4, GEngineIni);
            GConfig->SetFloat(TEXT("/Script/Engine.RendererSettings"), TEXT("r.ScreenPercentage"), 100.0f, GEngineIni);
        }
    }
    else if (TargetPlatform == TEXT("PC"))
    {
        // PC optimizations
        if (PerformanceLevel == TEXT("Low"))
        {
            GConfig->SetInt(TEXT("SystemSettings"), TEXT("sg.ViewDistanceQuality"), 0, GEngineIni);
            GConfig->SetInt(TEXT("SystemSettings"), TEXT("sg.ShadowQuality"), 0, GEngineIni);
            GConfig->SetInt(TEXT("SystemSettings"), TEXT("sg.TextureQuality"), 0, GEngineIni);
        }
        else if (PerformanceLevel == TEXT("High"))
        {
            GConfig->SetInt(TEXT("SystemSettings"), TEXT("sg.ViewDistanceQuality"), 3, GEngineIni);
            GConfig->SetInt(TEXT("SystemSettings"), TEXT("sg.ShadowQuality"), 3, GEngineIni);
            GConfig->SetInt(TEXT("SystemSettings"), TEXT("sg.TextureQuality"), 3, GEngineIni);
        }
    }

    // Save configuration changes
    GConfig->Flush(false, GEngineIni);

    return TEXT("{\"success\": true, \"message\": \"Platform performance optimized successfully\"}");
}

FString UnrealMCPPlatformCommands::HandleConfigurePlatformInput(const FString& JsonString)
{
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return TEXT("{\"success\": false, \"error\": \"Invalid JSON\"}");
    }

    FString TargetPlatform = JsonObject->GetStringField(TEXT("target_platform"));
    bool bEnableGamepad = JsonObject->GetBoolField(TEXT("enable_gamepad"));
    bool bEnableTouch = JsonObject->GetBoolField(TEXT("enable_touch"));
    bool bEnableKeyboard = JsonObject->GetBoolField(TEXT("enable_keyboard"));
    bool bEnableMouse = JsonObject->GetBoolField(TEXT("enable_mouse"));

    // Configure platform-specific input settings
    if (TargetPlatform == TEXT("Mobile"))
    {
        // Mobile input configuration
        GConfig->SetBool(TEXT("/Script/Engine.InputSettings"), TEXT("bUseMouseForTouch"), false, GInputIni);
        GConfig->SetBool(TEXT("/Script/Engine.InputSettings"), TEXT("bEnableTouchEvents"), bEnableTouch, GInputIni);
        GConfig->SetBool(TEXT("/Script/Engine.InputSettings"), TEXT("bEnableMouseEvents"), false, GInputIni);

        // Configure touch sensitivity
        GConfig->SetFloat(TEXT("/Script/Engine.InputSettings"), TEXT("TouchSensitivity"), 1.0f, GInputIni);

        // Configure virtual joystick
        if (bEnableTouch)
        {
            GConfig->SetBool(TEXT("/Script/Engine.InputSettings"), TEXT("bAlwaysShowTouchInterface"), true, GInputIni);
            GConfig->SetString(TEXT("/Script/Engine.InputSettings"), TEXT("DefaultTouchInterface"), TEXT("/Engine/MobileResources/HUD/DefaultVirtualJoysticks.DefaultVirtualJoysticks"), GInputIni);
        }
    }
    else if (TargetPlatform == TEXT("Console"))
    {
        // Console input configuration
        GConfig->SetBool(TEXT("/Script/Engine.InputSettings"), TEXT("bEnableMouseEvents"), false, GInputIni);
        GConfig->SetBool(TEXT("/Script/Engine.InputSettings"), TEXT("bEnableTouchEvents"), false, GInputIni);
        GConfig->SetBool(TEXT("/Script/Engine.InputSettings"), TEXT("bCaptureMouseOnLaunch"), false, GInputIni);

        // Configure gamepad settings
        if (bEnableGamepad)
        {
            GConfig->SetBool(TEXT("/Script/Engine.InputSettings"), TEXT("bEnableGamepadEvents"), true, GInputIni);
            GConfig->SetFloat(TEXT("/Script/Engine.InputSettings"), TEXT("GamepadDeadzone"), 0.25f, GInputIni);
        }
    }
    else if (TargetPlatform == TEXT("PC"))
    {
        // PC input configuration
        GConfig->SetBool(TEXT("/Script/Engine.InputSettings"), TEXT("bEnableMouseEvents"), bEnableMouse, GInputIni);
        GConfig->SetBool(TEXT("/Script/Engine.InputSettings"), TEXT("bEnableTouchEvents"), bEnableTouch, GInputIni);
        GConfig->SetBool(TEXT("/Script/Engine.InputSettings"), TEXT("bCaptureMouseOnLaunch"), bEnableMouse, GInputIni);

        // Configure keyboard and mouse settings
        if (bEnableKeyboard)
        {
            GConfig->SetBool(TEXT("/Script/Engine.InputSettings"), TEXT("bAltEnterTogglesFullscreen"), true, GInputIni);
        }

        if (bEnableGamepad)
        {
            GConfig->SetBool(TEXT("/Script/Engine.InputSettings"), TEXT("bEnableGamepadEvents"), true, GInputIni);
        }
    }

    // Save input configuration
    GConfig->Flush(false, GInputIni);

    return TEXT("{\"success\": true, \"message\": \"Platform input configured successfully\"}");
}

FString UnrealMCPPlatformCommands::HandleSetupPlatformPackaging(const FString& JsonString)
{
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return TEXT("{\"success\": false, \"error\": \"Invalid JSON\"}");
    }

    FString TargetPlatform = JsonObject->GetStringField(TEXT("target_platform"));
    FString BuildConfiguration = JsonObject->GetStringField(TEXT("build_configuration"));
    bool bCreateAppBundle = JsonObject->GetBoolField(TEXT("create_app_bundle"));
    bool bIncludeDebugFiles = JsonObject->GetBoolField(TEXT("include_debug_files"));

    // Configure platform-specific packaging settings
    if (TargetPlatform == TEXT("Android"))
    {
        // Android packaging configuration
        GConfig->SetString(TEXT("/Script/AndroidRuntimeSettings.AndroidRuntimeSettings"), TEXT("PackageName"), TEXT("com.yourcompany.yourgame"), GEngineIni);
        GConfig->SetString(TEXT("/Script/AndroidRuntimeSettings.AndroidRuntimeSettings"), TEXT("ApplicationDisplayName"), TEXT("Your Game"), GEngineIni);
        GConfig->SetInt(TEXT("/Script/AndroidRuntimeSettings.AndroidRuntimeSettings"), TEXT("VersionDisplayName"), 1, GEngineIni);
        GConfig->SetInt(TEXT("/Script/AndroidRuntimeSettings.AndroidRuntimeSettings"), TEXT("StoreVersion"), 1, GEngineIni);

        // Configure APK settings
        GConfig->SetBool(TEXT("/Script/AndroidRuntimeSettings.AndroidRuntimeSettings"), TEXT("bPackageDataInsideApk"), true, GEngineIni);
        GConfig->SetBool(TEXT("/Script/AndroidRuntimeSettings.AndroidRuntimeSettings"), TEXT("bCreateAllPlatformsInstall"), false, GEngineIni);

        if (BuildConfiguration == TEXT("Shipping"))
        {
            GConfig->SetBool(TEXT("/Script/AndroidRuntimeSettings.AndroidRuntimeSettings"), TEXT("bEnableProGuard"), true, GEngineIni);
        }
    }
    else if (TargetPlatform == TEXT("iOS"))
    {
        // iOS packaging configuration
        GConfig->SetString(TEXT("/Script/IOSRuntimeSettings.IOSRuntimeSettings"), TEXT("BundleDisplayName"), TEXT("Your Game"), GEngineIni);
        GConfig->SetString(TEXT("/Script/IOSRuntimeSettings.IOSRuntimeSettings"), TEXT("BundleName"), TEXT("YourGame"), GEngineIni);
        GConfig->SetString(TEXT("/Script/IOSRuntimeSettings.IOSRuntimeSettings"), TEXT("BundleIdentifier"), TEXT("com.yourcompany.yourgame"), GEngineIni);
        GConfig->SetString(TEXT("/Script/IOSRuntimeSettings.IOSRuntimeSettings"), TEXT("VersionInfo"), TEXT("1.0"), GEngineIni);

        // Configure app bundle settings
        if (bCreateAppBundle)
        {
            GConfig->SetBool(TEXT("/Script/IOSRuntimeSettings.IOSRuntimeSettings"), TEXT("bGenerateXCArchive"), true, GEngineIni);
        }
    }
    else if (TargetPlatform == TEXT("Windows"))
    {
        // Windows packaging configuration
        GConfig->SetString(TEXT("/Script/WindowsTargetPlatform.WindowsTargetSettings"), TEXT("CompilerVersion"), TEXT("VisualStudio2022"), GEngineIni);

        if (!bIncludeDebugFiles && BuildConfiguration == TEXT("Shipping"))
        {
            GConfig->SetBool(TEXT("/Script/UnrealEd.ProjectPackagingSettings"), TEXT("bIncludeDebugFiles"), false, GEngineIni);
            GConfig->SetBool(TEXT("/Script/UnrealEd.ProjectPackagingSettings"), TEXT("bIncludeCrashReporter"), false, GEngineIni);
        }
    }

    // General packaging settings
    GConfig->SetString(TEXT("/Script/UnrealEd.ProjectPackagingSettings"), TEXT("BuildConfiguration"), *BuildConfiguration, GEngineIni);
    GConfig->SetBool(TEXT("/Script/UnrealEd.ProjectPackagingSettings"), TEXT("bCompressed"), true, GEngineIni);

    // Save packaging configuration
    GConfig->Flush(false, GEngineIni);

    return TEXT("{\"success\": true, \"message\": \"Platform packaging configured successfully\"}");
}

FString UnrealMCPPlatformCommands::HandleManagePlatformStoreIntegration(const FString& JsonString)
{
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return TEXT("{\"success\": false, \"error\": \"Invalid JSON\"}");
    }

    FString StoreType = JsonObject->GetStringField(TEXT("store_type"));
    bool bEnableInAppPurchases = JsonObject->GetBoolField(TEXT("enable_in_app_purchases"));
    bool bEnableAchievements = JsonObject->GetBoolField(TEXT("enable_achievements"));
    bool bEnableLeaderboards = JsonObject->GetBoolField(TEXT("enable_leaderboards"));
    FString AppID = JsonObject->GetStringField(TEXT("app_id"));

    if (!ValidateStoreType(StoreType))
    {
        return TEXT("{\"success\": false, \"error\": \"Invalid store type\"}");
    }

    // Configure store-specific integration
    if (StoreType == TEXT("Steam"))
    {
        // Steam store integration
        GConfig->SetString(TEXT("OnlineSubsystemSteam"), TEXT("SteamDevAppId"), *AppID, GEngineIni);
        GConfig->SetBool(TEXT("OnlineSubsystemSteam"), TEXT("bEnabled"), true, GEngineIni);
        GConfig->SetBool(TEXT("OnlineSubsystemSteam"), TEXT("bInitServerOnClient"), true, GEngineIni);

        if (bEnableInAppPurchases)
        {
            GConfig->SetBool(TEXT("OnlineSubsystemSteam"), TEXT("bHasStoreInterface"), true, GEngineIni);
        }

        if (bEnableAchievements)
        {
            GConfig->SetBool(TEXT("OnlineSubsystemSteam"), TEXT("bHasAchievementsInterface"), true, GEngineIni);
        }

        if (bEnableLeaderboards)
        {
            GConfig->SetBool(TEXT("OnlineSubsystemSteam"), TEXT("bHasLeaderboardInterface"), true, GEngineIni);
        }
    }
    else if (StoreType == TEXT("EpicGamesStore"))
    {
        // Epic Games Store integration
        GConfig->SetString(TEXT("OnlineSubsystemEOS"), TEXT("ProductId"), *AppID, GEngineIni);
        GConfig->SetBool(TEXT("OnlineSubsystemEOS"), TEXT("bEnabled"), true, GEngineIni);

        if (bEnableInAppPurchases)
        {
            GConfig->SetBool(TEXT("OnlineSubsystemEOS"), TEXT("bHasStoreInterface"), true, GEngineIni);
        }

        if (bEnableAchievements)
        {
            GConfig->SetBool(TEXT("OnlineSubsystemEOS"), TEXT("bHasAchievementsInterface"), true, GEngineIni);
        }
    }
    else if (StoreType == TEXT("GooglePlay"))
    {
        // Google Play Store integration
        GConfig->SetString(TEXT("/Script/AndroidRuntimeSettings.AndroidRuntimeSettings"), TEXT("GooglePlayAppID"), *AppID, GEngineIni);
        GConfig->SetBool(TEXT("/Script/AndroidRuntimeSettings.AndroidRuntimeSettings"), TEXT("bEnableGooglePlaySupport"), true, GEngineIni);

        if (bEnableInAppPurchases)
        {
            GConfig->SetBool(TEXT("/Script/AndroidRuntimeSettings.AndroidRuntimeSettings"), TEXT("bGooglePlayBillingSupport"), true, GEngineIni);
        }

        if (bEnableAchievements)
        {
            GConfig->SetBool(TEXT("/Script/AndroidRuntimeSettings.AndroidRuntimeSettings"), TEXT("bEnableGooglePlayGameServices"), true, GEngineIni);
        }
    }
    else if (StoreType == TEXT("AppStore"))
    {
        // Apple App Store integration
        GConfig->SetString(TEXT("/Script/IOSRuntimeSettings.IOSRuntimeSettings"), TEXT("BundleIdentifier"), *AppID, GEngineIni);

        if (bEnableInAppPurchases)
        {
            GConfig->SetBool(TEXT("/Script/IOSRuntimeSettings.IOSRuntimeSettings"), TEXT("bEnableStoreKit"), true, GEngineIni);
        }

        if (bEnableAchievements)
        {
            GConfig->SetBool(TEXT("/Script/IOSRuntimeSettings.IOSRuntimeSettings"), TEXT("bEnableGameCenter"), true, GEngineIni);
        }
    }

    // Save store integration configuration
    GConfig->Flush(false, GEngineIni);

    return TEXT("{\"success\": true, \"message\": \"Platform store integration configured successfully\"}");
}

FString UnrealMCPPlatformCommands::HandleAnalyzePlatformCompatibility(const FString& JsonString)
{
    TSharedPtr<FJsonObject> JsonObject;
    TSharedRef<TJsonReader<>> Reader = TJsonReaderFactory<>::Create(JsonString);

    if (!FJsonSerializer::Deserialize(Reader, JsonObject) || !JsonObject.IsValid())
    {
        return TEXT("{\"success\": false, \"error\": \"Invalid JSON\"}");
    }

    FString TargetPlatform = JsonObject->GetStringField(TEXT("target_platform"));

    // Analyze platform compatibility
    TSharedPtr<FJsonObject> CompatibilityReport = MakeShareable(new FJsonObject);

    // Current platform information
    CompatibilityReport->SetStringField(TEXT("current_platform"), FPlatformProperties::PlatformName());
    CompatibilityReport->SetStringField(TEXT("target_platform"), TargetPlatform);

    // Hardware compatibility analysis
    TSharedPtr<FJsonObject> HardwareCompatibility = MakeShareable(new FJsonObject);

    // CPU compatibility
    int32 CoreCount = FPlatformMisc::NumberOfCores();
    HardwareCompatibility->SetNumberField(TEXT("cpu_cores"), CoreCount);
    HardwareCompatibility->SetBoolField(TEXT("cpu_compatible"), CoreCount >= 2);

    // Memory compatibility
    FPlatformMemoryStats MemStats = FPlatformMemory::GetStats();
    double TotalGB = MemStats.TotalPhysical / (1024.0 * 1024.0 * 1024.0);
    HardwareCompatibility->SetNumberField(TEXT("total_memory_gb"), TotalGB);

    bool bMemoryCompatible = true;
    if (TargetPlatform == TEXT("Mobile"))
    {
        bMemoryCompatible = TotalGB >= 2.0; // Minimum 2GB for mobile
    }
    else if (TargetPlatform == TEXT("Console"))
    {
        bMemoryCompatible = TotalGB >= 8.0; // Minimum 8GB for console
    }
    else if (TargetPlatform == TEXT("PC"))
    {
        bMemoryCompatible = TotalGB >= 4.0; // Minimum 4GB for PC
    }

    HardwareCompatibility->SetBoolField(TEXT("memory_compatible"), bMemoryCompatible);

    CompatibilityReport->SetObjectField(TEXT("hardware_compatibility"), HardwareCompatibility);

    // Feature compatibility analysis
    TSharedPtr<FJsonObject> FeatureCompatibility = MakeShareable(new FJsonObject);

    // Graphics API compatibility - using compile-time detection
    bool bVulkanSupported = false;
    bool bDirectX12Supported = false;
    bool bMetalSupported = false;

#if PLATFORM_WINDOWS
    bVulkanSupported = true;
    bDirectX12Supported = true;
#elif PLATFORM_MAC
    bMetalSupported = true;
    bVulkanSupported = true;
#elif PLATFORM_LINUX
    bVulkanSupported = true;
#elif PLATFORM_ANDROID
    bVulkanSupported = true;
#elif PLATFORM_IOS
    bMetalSupported = true;
#endif

    FeatureCompatibility->SetBoolField(TEXT("vulkan_supported"), bVulkanSupported);
    FeatureCompatibility->SetBoolField(TEXT("directx12_supported"), bDirectX12Supported);
    FeatureCompatibility->SetBoolField(TEXT("metal_supported"), bMetalSupported);

    // Platform-specific feature compatibility
    if (TargetPlatform == TEXT("Mobile"))
    {
        FeatureCompatibility->SetBoolField(TEXT("touch_input_supported"), true);
        FeatureCompatibility->SetBoolField(TEXT("accelerometer_supported"), true);
        FeatureCompatibility->SetBoolField(TEXT("gps_supported"), true);
    }
    else if (TargetPlatform == TEXT("Console"))
    {
        FeatureCompatibility->SetBoolField(TEXT("gamepad_required"), true);
        FeatureCompatibility->SetBoolField(TEXT("4k_rendering_supported"), true);
        FeatureCompatibility->SetBoolField(TEXT("hdr_supported"), true);
    }
    else if (TargetPlatform == TEXT("PC"))
    {
        FeatureCompatibility->SetBoolField(TEXT("keyboard_mouse_supported"), true);
        FeatureCompatibility->SetBoolField(TEXT("multiple_monitors_supported"), true);
        FeatureCompatibility->SetBoolField(TEXT("mod_support_possible"), true);
    }

    CompatibilityReport->SetObjectField(TEXT("feature_compatibility"), FeatureCompatibility);

    // Compatibility recommendations
    TArray<TSharedPtr<FJsonValue>> Recommendations;

    if (!bMemoryCompatible)
    {
        Recommendations.Add(MakeShareable(new FJsonValueString(TEXT("Insufficient memory for target platform - consider memory optimizations"))));
    }

    if (CoreCount < 4)
    {
        Recommendations.Add(MakeShareable(new FJsonValueString(TEXT("Low CPU core count - consider CPU-intensive feature limitations"))));
    }

    if (TargetPlatform == TEXT("Mobile") && !bVulkanSupported && !bMetalSupported)
    {
        Recommendations.Add(MakeShareable(new FJsonValueString(TEXT("Modern graphics API not supported - consider OpenGL ES fallback"))));
    }

    CompatibilityReport->SetArrayField(TEXT("recommendations"), Recommendations);

    // Overall compatibility score
    int32 CompatibilityScore = 100;
    if (!bMemoryCompatible) CompatibilityScore -= 30;
    if (CoreCount < 4) CompatibilityScore -= 20;
    if (!bVulkanSupported && !bDirectX12Supported && !bMetalSupported) CompatibilityScore -= 25;

    CompatibilityReport->SetNumberField(TEXT("compatibility_score"), CompatibilityScore);

    FString CompatibilityStatus = TEXT("Excellent");
    if (CompatibilityScore < 50)
    {
        CompatibilityStatus = TEXT("Poor");
    }
    else if (CompatibilityScore < 75)
    {
        CompatibilityStatus = TEXT("Fair");
    }
    else if (CompatibilityScore < 90)
    {
        CompatibilityStatus = TEXT("Good");
    }

    CompatibilityReport->SetStringField(TEXT("compatibility_status"), CompatibilityStatus);

    // Serialize result
    FString OutputString;
    TSharedRef<TJsonWriter<>> Writer = TJsonWriterFactory<>::Create(&OutputString);

    TSharedPtr<FJsonObject> ResponseObject = MakeShareable(new FJsonObject);
    ResponseObject->SetBoolField(TEXT("success"), true);
    ResponseObject->SetObjectField(TEXT("compatibility_report"), CompatibilityReport);

    FJsonSerializer::Serialize(ResponseObject.ToSharedRef(), Writer);

    return OutputString;
}
