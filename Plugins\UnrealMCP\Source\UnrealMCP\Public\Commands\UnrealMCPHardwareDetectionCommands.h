// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "RHI.h"
#include "RHIDefinitions.h"
#include "GenericPlatform/GenericPlatformMemory.h"
#include "GenericPlatform/GenericPlatformMisc.h"
#include "HAL/PlatformMemory.h"
#include "HAL/PlatformMisc.h"
#include "HAL/PlatformFilemanager.h"
#include "Misc/ConfigCacheIni.h"
#include "GameFramework/GameUserSettings.h"
#include "Scalability.h"
#include "DeviceProfiles/DeviceProfile.h"
#include "DeviceProfiles/DeviceProfileManager.h"
#include "ProfilingDebugging/RealtimeGPUProfiler.h"
#include "Stats/Stats.h"
#include "HAL/IConsoleManager.h"
#include "Rendering/RenderingCommon.h"
#include "DataDrivenShaderPlatformInfo.h"

/**
 * UnrealMCPHardwareDetectionCommands
 * 
 * Handles all Hardware Detection related commands for the MCP server.
 * Provides comprehensive hardware analysis, benchmarking, and optimization tools
 * compatible with Unreal Engine 5.6 hardware detection systems.
 * 
 * Features:
 * - System hardware detection (CPU, GPU, Memory, Storage)
 * - Hardware benchmarking and performance analysis
 * - GPU capability detection and testing
 * - Memory configuration analysis
 * - Platform-specific capability detection
 * - Device profile configuration and optimization
 * - Hardware tier optimization
 * - Real-time performance monitoring
 * - Comprehensive hardware reporting
 * - Cross-platform hardware support
 */
class UNREALMCP_API UnrealMCPHardwareDetectionCommands
{
public:
    UnrealMCPHardwareDetectionCommands();
    ~UnrealMCPHardwareDetectionCommands();

    // Core Hardware Detection
    static FString HandleDetectSystemHardware(const FString& JsonParams);
    static FString HandleRunHardwareBenchmark(const FString& JsonParams);
    static FString HandleDetectGPUCapabilities(const FString& JsonParams);
    static FString HandleAnalyzeMemoryConfiguration(const FString& JsonParams);
    static FString HandleDetectPlatformCapabilities(const FString& JsonParams);

    // Device Profile and Optimization
    static FString HandleConfigureDeviceProfile(const FString& JsonParams);
    static FString HandleOptimizeForHardwareTier(const FString& JsonParams);

    // Performance Monitoring and Reporting
    static FString HandleMonitorHardwarePerformance(const FString& JsonParams);
    static FString HandleGenerateHardwareReport(const FString& JsonParams);

private:
    // Helper Functions for System Hardware Detection
    static TSharedPtr<FJsonObject> DetectCPUInformation();
    static TSharedPtr<FJsonObject> DetectGPUInformation();
    static TSharedPtr<FJsonObject> DetectMemoryInformation();
    static TSharedPtr<FJsonObject> DetectStorageInformation();
    static TSharedPtr<FJsonObject> DetectSystemInformation();
    
    // Helper Functions for Hardware Benchmarking
    static TSharedPtr<FJsonObject> RunCPUBenchmark(int32 Duration);
    static TSharedPtr<FJsonObject> RunGPUBenchmark(int32 Duration);
    static TSharedPtr<FJsonObject> RunMemoryBenchmark(int32 Duration);
    static TSharedPtr<FJsonObject> RunFullSystemBenchmark(int32 Duration);
    static bool ApplyBenchmarkResults(const TSharedPtr<FJsonObject>& BenchmarkResults);
    
    // Helper Functions for GPU Capability Detection
    static bool TestRayTracingSupport();
    static bool TestComputeShaderSupport();
    static TSharedPtr<FJsonObject> TestGPUMemoryBandwidth();
    static TSharedPtr<FJsonObject> TestAdvancedFeatureSupport();
    static TSharedPtr<FJsonObject> GenerateGPUCapabilityReport();
    
    // Helper Functions for Memory Analysis
    static TSharedPtr<FJsonObject> TestMemoryAllocationSpeed();
    static TSharedPtr<FJsonObject> TestMemoryBandwidth();
    static FString DetectMemoryBucket();
    static TSharedPtr<FJsonObject> AnalyzeVirtualMemoryUsage();
    static bool CheckMemoryPressure();
    
    // Helper Functions for Platform Detection
    static TSharedPtr<FJsonObject> DetectOperatingSystemFeatures();
    static TSharedPtr<FJsonObject> DetectGraphicsDriverVersions();
    static TSharedPtr<FJsonObject> DetectGraphicsAPISupport();
    static TSharedPtr<FJsonObject> DetectHardwareSpecificFeatures();
    static bool CheckUnrealEngineCompatibility();
    
    // Helper Functions for Device Profile Configuration
    static UDeviceProfile* DetectOptimalDeviceProfile();
    static bool ApplyDeviceProfile(const FString& ProfileName);
    static bool ConfigureMemoryBucketSettings(const FString& MemoryBucket);
    static bool ConfigureGPUFamilySettings(const FString& GPUFamily);
    static bool ConfigurePlatformSettings(const FString& PlatformType);
    
    // Helper Functions for Hardware Tier Optimization
    static bool OptimizeForHighEndHardware(int32 TargetFPS, const FString& QualityPreset);
    static bool OptimizeForMidRangeHardware(int32 TargetFPS, const FString& QualityPreset);
    static bool OptimizeForLowEndHardware(int32 TargetFPS, const FString& QualityPreset);
    static bool OptimizeForMobileHardware(int32 TargetFPS, const FString& QualityPreset);
    static bool EnableDynamicQualityScaling(bool bEnable);
    
    // Helper Functions for Performance Monitoring
    static TSharedPtr<FJsonObject> MonitorCPUPerformance(float Duration);
    static TSharedPtr<FJsonObject> MonitorGPUPerformance(float Duration);
    static TSharedPtr<FJsonObject> MonitorMemoryUsage(float Duration);
    static TSharedPtr<FJsonObject> MonitorThermalConditions(float Duration);
    static bool GeneratePerformanceAlerts(const TSharedPtr<FJsonObject>& MonitoringData);
    
    // Helper Functions for Hardware Reporting
    static TSharedPtr<FJsonObject> CompileHardwareSpecifications();
    static TSharedPtr<FJsonObject> CompileBenchmarkResults();
    static TSharedPtr<FJsonObject> CompileCompatibilityAnalysis();
    static TSharedPtr<FJsonObject> GenerateOptimizationRecommendations();
    static bool SaveReportToFile(const TSharedPtr<FJsonObject>& Report, 
        const FString& Format, const FString& Filename);
    
    // Utility Functions
    static FString GetCPUBrandString();
    static FString GetGPUAdapterName();
    static int64 GetTotalPhysicalMemory();
    static int64 GetAvailablePhysicalMemory();
    static FString GetOperatingSystemVersion();
    static FString GetGraphicsDriverVersion();
    static ERHIFeatureLevel::Type GetMaxSupportedFeatureLevel();
    static bool IsRayTracingSupported();
    static bool IsVulkanSupported();
    static bool IsDirectX12Supported();
    
    // JSON Parsing Helpers
    static TSharedPtr<FJsonObject> ParseJsonParams(const FString& JsonParams);
    static FString CreateJsonResponse(bool bSuccess, const FString& Message, 
        const TMap<FString, FString>& AdditionalData = TMap<FString, FString>());
    static FString CreateJsonResponse(bool bSuccess, const FString& Message, 
        const TSharedPtr<FJsonObject>& DataObject);
    
    // Validation Functions
    static bool ValidateBenchmarkType(const FString& BenchmarkType);
    static bool ValidateHardwareTier(const FString& HardwareTier);
    static bool ValidateQualityPreset(const FString& QualityPreset);
    static bool ValidateOutputFormat(const FString& OutputFormat);
    static bool ValidateMemoryBucket(const FString& MemoryBucket);
    static bool ValidateGPUFamily(const FString& GPUFamily);
    
    // Constants for Hardware Detection
    static const TArray<FString> SupportedBenchmarkTypes;
    static const TArray<FString> SupportedHardwareTiers;
    static const TArray<FString> SupportedQualityPresets;
    static const TArray<FString> SupportedOutputFormats;
    static const TArray<FString> SupportedMemoryBuckets;
    static const TArray<FString> SupportedGPUFamilies;
    static const TArray<FString> SupportedPlatformTypes;
    
    // Hardware Detection Constants
    static const int32 DefaultBenchmarkDuration;
    static const int32 MaxBenchmarkDuration;
    static const float DefaultMonitoringDuration;
    static const float MaxMonitoringDuration;
};
