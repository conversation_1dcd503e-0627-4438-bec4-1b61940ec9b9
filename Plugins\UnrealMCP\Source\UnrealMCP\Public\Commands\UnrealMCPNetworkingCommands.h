// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "GameFramework/GameModeBase.h"
#include "GameFramework/PlayerController.h"
#include "GameFramework/Pawn.h"
#include "GameFramework/SpectatorPawn.h"
#include "Engine/NetDriver.h"
#include "Engine/NetConnection.h"
#include "Net/UnrealNetwork.h"
#include "Net/RepLayout.h"
#include "Net/DataReplication.h"
#include "ReplicationGraph.h"
#include "Iris/ReplicationSystem/ReplicationSystem.h"
#include "OnlineSubsystem.h"
#include "OnlineSessionSettings.h"
#include "Interfaces/OnlineSessionInterface.h"
#include "Kismet/GameplayStatics.h"
#include "HAL/IConsoleManager.h"
#include "Stats/Stats.h"
#include "Net/NetworkProfiler.h"

/**
 * UnrealMCPNetworkingCommands
 * 
 * Handles all Networking related commands for the MCP server.
 * Provides comprehensive multiplayer networking tools and configuration
 * compatible with Unreal Engine 5.6 networking systems.
 * 
 * Features:
 * - Multiplayer session management
 * - Actor replication configuration
 * - Remote Procedure Call (RPC) creation
 * - Network settings configuration
 * - Game Mode networking setup
 * - Player connection management
 * - Replication Graph optimization
 * - Network debugging and simulation
 * - Performance analysis and profiling
 * - Cross-platform networking support
 */
class UNREALMCP_API UnrealMCPNetworkingCommands
{
public:
    UnrealMCPNetworkingCommands();
    ~UnrealMCPNetworkingCommands();

    // Core Networking Functions
    static FString HandleCreateMultiplayerSession(const FString& JsonParams);
    static FString HandleSetupActorReplication(const FString& JsonParams);
    static FString HandleCreateRPCFunction(const FString& JsonParams);
    static FString HandleConfigureNetworkSettings(const FString& JsonParams);

    // Game Mode and Player Management
    static FString HandleSetupGameModeNetworking(const FString& JsonParams);
    static FString HandleManagePlayerConnections(const FString& JsonParams);

    // Advanced Networking Features
    static FString HandleConfigureReplicationGraph(const FString& JsonParams);
    static FString HandleSetupNetworkDebugging(const FString& JsonParams);
    static FString HandleAnalyzeNetworkPerformance(const FString& JsonParams);

private:
    // Helper Functions for Session Management
    static bool CreateOnlineSession(const FString& SessionName, int32 MaxPlayers, bool bIsLAN, 
        bool bIsDedicated, bool bAllowJoinInProgress, bool bUsePresence);
    static bool DestroyOnlineSession(const FString& SessionName);
    static bool StartOnlineSession(const FString& SessionName);
    static bool EndOnlineSession(const FString& SessionName);
    static TArray<FOnlineSessionSearchResult> FindOnlineSessions(bool bIsLAN, int32 MaxResults);
    
    // Helper Functions for Actor Replication
    static bool SetupActorForReplication(const FString& ActorClassPath, bool bReplicateMovement, 
        const TArray<FString>& ReplicateProperties, float NetCullDistance, float NetUpdateFrequency);
    static bool EnableActorReplication(AActor* Actor, bool bEnable);
    static bool ConfigureActorNetworkSettings(AActor* Actor, float CullDistance, float UpdateFrequency);
    static bool AddReplicatedProperty(UClass* ActorClass, const FString& PropertyName);
    static bool SetupMovementReplication(AActor* Actor, bool bEnable);
    
    // Helper Functions for RPC Creation
    static bool CreateServerRPC(UClass* TargetClass, const FString& FunctionName, bool bIsReliable, 
        const TArray<TPair<FString, FString>>& Parameters);
    static bool CreateClientRPC(UClass* TargetClass, const FString& FunctionName, bool bIsReliable, 
        const TArray<TPair<FString, FString>>& Parameters);
    static bool CreateNetMulticastRPC(UClass* TargetClass, const FString& FunctionName, bool bIsReliable, 
        const TArray<TPair<FString, FString>>& Parameters);
    static bool ValidateRPCParameters(const TArray<TPair<FString, FString>>& Parameters);
    
    // Helper Functions for Network Configuration
    static bool SetNetworkTickRate(int32 TickRate);
    static bool SetMaxClientRate(int32 MaxInternetClientRate, int32 LocalMaxClientRate);
    static bool EnableNetworkProfiler(bool bEnable);
    static bool ConfigureNetworkEmulation(const TMap<FString, float>& EmulationSettings);
    static bool ApplyNetworkSettings();
    
    // Helper Functions for Game Mode Setup
    static bool ConfigureGameModeForNetworking(const FString& GameModeClassPath, 
        const FString& PlayerControllerClassPath, const FString& PawnClassPath, 
        const FString& SpectatorClassPath, bool bEnableSeamlessTravel);
    static bool SetDefaultGameModeClasses(UClass* GameModeClass, UClass* PlayerControllerClass, 
        UClass* PawnClass, UClass* SpectatorClass);
    static bool EnableSeamlessTravel(bool bEnable);
    
    // Helper Functions for Player Management
    static TArray<TSharedPtr<FJsonObject>> GetConnectedPlayers();
    static bool KickPlayer(const FString& PlayerID, const FString& Reason);
    static bool BanPlayer(const FString& PlayerID, const FString& Reason, int32 DurationMinutes);
    static bool UnbanPlayer(const FString& PlayerID);
    static bool IsPlayerBanned(const FString& PlayerID);
    
    // Helper Functions for Replication Graph
    static bool EnableReplicationGraph(bool bEnable, float SpatialBias, int32 MaxActorsPerFrame);
    static bool ConfigureIrisReplication(bool bEnable);
    static bool AddCustomReplicationNodes(const TArray<FString>& NodeClassPaths);
    static bool SetReplicationGraphSettings(const TMap<FString, float>& Settings);
    
    // Helper Functions for Network Debugging
    static bool EnableNetworkLogging(bool bEnable, const FString& LogLevel);
    static bool SetupPacketLossSimulation(bool bEnable, float LossPercentage);
    static bool SetupLatencySimulation(int32 LatencyMS);
    static bool EnableNetworkStats(bool bEnable);
    static TSharedPtr<FJsonObject> GetNetworkDebugInfo();
    
    // Helper Functions for Performance Analysis
    static TSharedPtr<FJsonObject> CollectBandwidthStats(float Duration);
    static TSharedPtr<FJsonObject> CollectLatencyStats(float Duration);
    static TSharedPtr<FJsonObject> CollectPacketLossStats(float Duration);
    static TSharedPtr<FJsonObject> GenerateNetworkPerformanceReport(const TSharedPtr<FJsonObject>& Stats);
    static bool SaveNetworkPerformanceReport(const TSharedPtr<FJsonObject>& Report, const FString& Filename);
    
    // Utility Functions
    static UWorld* GetNetworkWorld();
    static UNetDriver* GetNetDriver();
    static IOnlineSubsystem* GetOnlineSubsystem();
    static IOnlineSessionPtr GetSessionInterface();
    static UClass* LoadClassFromPath(const FString& ClassPath);
    static bool IsValidNetworkClass(UClass* Class);
    static FString GetPlayerIDFromController(APlayerController* Controller);
    
    // JSON Parsing Helpers
    static TSharedPtr<FJsonObject> ParseJsonParams(const FString& JsonParams);
    static FString CreateJsonResponse(bool bSuccess, const FString& Message, 
        const TMap<FString, FString>& AdditionalData = TMap<FString, FString>());
    static FString CreateJsonResponse(bool bSuccess, const FString& Message, 
        const TSharedPtr<FJsonObject>& DataObject);
    
    // Validation Functions
    static bool ValidateSessionName(const FString& SessionName);
    static bool ValidateMaxPlayers(int32 MaxPlayers);
    static bool ValidateRPCType(const FString& RPCType);
    static bool ValidateLogLevel(const FString& LogLevel);
    static bool ValidatePlayerAction(const FString& Action);
    static bool ValidateNetworkSettings(int32 TickRate, int32 LocalMaxClientRate);
    
    // Constants for Networking
    static const TArray<FString> SupportedRPCTypes;
    static const TArray<FString> SupportedLogLevels;
    static const TArray<FString> SupportedPlayerActions;
    static const TArray<FString> SupportedParameterTypes;
    
    // Networking Constants
    static const int32 DefaultMaxPlayers;
    static const int32 DefaultTickRate;
    static const int32 DefaultMaxClientRate;
    static const float DefaultNetCullDistance;
    static const float DefaultNetUpdateFrequency;
    static const float DefaultSpatialBias;
    static const int32 DefaultMaxActorsPerFrame;
    static const float MaxAnalysisDuration;

    // ============================================================================
    // Real Multiplayer/Networking System Functions using UE 5.6 APIs
    // ============================================================================

    /**
     * Initializes the real networking system using UE 5.6 networking APIs.
     */
    static TSharedPtr<FJsonObject> InitializeRealNetworkingSystem();

    /**
     * Collects real networking metrics using UE 5.6 APIs.
     */
    static TSharedPtr<FJsonObject> CollectRealNetworkingMetrics(UWorld* World);

    // Helper functions for real networking system
    static bool InitializeNetworkDriverSystem(UWorld* World);
    static bool InitializeConnectionManagementSystem(UWorld* World);
    static bool InitializeReplicationSystem(UWorld* World);
    static bool InitializeRPCSystem(UWorld* World);
    static bool InitializeNetworkSecuritySystem();
    static void InitializeNetworkConfiguration();
    static void InitializeBandwidthManagement();
    static void InitializeLagCompensation();
    static void InitializeAntiCheatNetworking();
    static void InitializeConnectionQualityMonitoring();
    static void InitializeRelevancySystem();
    static void InitializeDeltaCompression();
    static void InitializePropertyReplication();
    static void InitializeRPCValidation();
    static void InitializeDDoSProtection();
    static float CalculateNetworkingPerformanceImpact();
    static float CalculateNetworkLoad();

    // Static member variables for networking system state
    static inline bool NetworkDriverActive = false;
    static inline bool ConnectionManagementActive = false;
    static inline bool ReplicationSystemActive = false;
    static inline bool RPCSystemActive = false;
    static inline bool NetworkSecurityActive = false;
    static inline bool NetworkConfigurationActive = false;
    static inline bool BandwidthManagementActive = false;
    static inline bool LagCompensationActive = false;
    static inline bool AntiCheatNetworkingActive = false;
    static inline bool ConnectionQualityMonitoringActive = false;
    static inline bool RelevancySystemActive = false;
    static inline bool DeltaCompressionActive = false;
    static inline bool PropertyReplicationActive = false;
    static inline bool RPCValidationActive = false;
    static inline bool DDoSProtectionActive = false;
    static inline bool NetworkConfigurationInitialized = false;
    static inline bool BandwidthManagementInitialized = false;
    static inline bool LagCompensationInitialized = false;
    static inline bool AntiCheatNetworkingInitialized = false;
    static inline bool ConnectionQualityMonitoringInitialized = false;
    static inline bool RelevancySystemInitialized = false;
    static inline bool DeltaCompressionInitialized = false;
    static inline bool PropertyReplicationInitialized = false;
    static inline bool RPCValidationInitialized = false;
    static inline bool DDoSProtectionInitialized = false;
    static inline bool EncryptionEnabled = false;
    static inline bool PacketValidationEnabled = false;
    static inline bool AntiSpamEnabled = false;
    static inline bool BandwidthThrottlingEnabled = false;
    static inline bool AdaptiveBandwidthEnabled = false;
    static inline bool TrafficShapingEnabled = false;
    static inline bool PriorityQueuesEnabled = false;
    static inline bool InterpolationEnabled = false;
    static inline bool ExtrapolationEnabled = false;
    static inline bool ClientSidePredictionEnabled = false;
    static inline bool ServerReconciliationEnabled = false;
    static inline bool ServerAuthoritativeMovement = false;
    static inline bool InputValidationEnabled = false;
    static inline bool StateValidationEnabled = false;
    static inline bool SpeedHackDetectionEnabled = false;
    static inline bool TeleportDetectionEnabled = false;
    static inline bool PingMonitoringEnabled = false;
    static inline bool PacketLossMonitoringEnabled = false;
    static inline bool JitterMonitoringEnabled = false;
    static inline bool SpatialPartitioningEnabled = false;
    static inline bool CullingEnabled = false;
    static inline bool CompressionEnabled = false;
    static inline bool DeltaSerializationEnabled = false;
    static inline bool ConditionalReplicationEnabled = false;
    static inline bool ReplicationConditionFiltering = false;
    static inline bool PropertyChangeTrackingEnabled = false;
    static inline bool RPCParameterValidationEnabled = false;
    static inline bool RPCRateValidationEnabled = false;
    static inline bool RPCAuthorizationEnabled = false;
    static inline bool ConnectionRateLimitingEnabled = false;
    static inline bool PacketFloodProtectionEnabled = false;
    static inline bool IPBlacklistingEnabled = false;
    static inline bool QoSEnabled = false;
    static inline bool PriorityTrafficEnabled = false;
    static inline bool LatencyCompensationEnabled = false;

    static inline int32 MaxConnections = 0;
    static inline int32 MaxPacketSize = 0;
    static inline int32 ServerTickRate = 0;
    static inline int32 MaxClientRate = 0;
    static inline int32 ClientTickRate = 0;
    static inline int32 MaxReconnectAttempts = 0;
    static inline int32 MaxRPCsPerFrame = 0;
    static inline int32 ReliableRPCRetries = 0;
    static inline int32 EncryptionKeySize = 0;
    static inline int32 MaxPacketsPerSecond = 0;
    static inline int32 MaxBytesPerSecond = 0;
    static inline int32 MaxUploadBandwidth = 0;
    static inline int32 MaxDownloadBandwidth = 0;
    static inline int32 CompressionLevel = 0;
    static inline int32 MaxConnectionsPerIP = 0;
    static inline int32 MaxPacketsPerSecondPerIP = 0;

    static inline float ConnectionTimeout = 0.0f;
    static inline float KeepAliveInterval = 0.0f;
    static inline float ReplicationRate = 0.0f;
    static inline float MaxReplicationDistance = 0.0f;
    static inline float ReplicationPriority = 0.0f;
    static inline float RPCTimeout = 0.0f;
    static inline float MaxAcceptableLatency = 0.0f;
    static inline float MaxLagCompensationTime = 0.0f;
    static inline float MaxAcceptablePing = 0.0f;
    static inline float MaxAcceptablePacketLoss = 0.0f;
    static inline float MaxAcceptableJitter = 0.0f;
    static inline float RelevancyDistance = 0.0f;
    static inline float RelevancyUpdateRate = 0.0f;

    static inline FString NetworkRole = TEXT("Unknown");

    static inline TArray<class UNetConnection*> ActiveConnections;
    static inline TMap<class UNetConnection*, float> ConnectionTimeouts;
    static inline TArray<FString> PendingRPCs;
    static inline TArray<FString> ReliableRPCs;
};
