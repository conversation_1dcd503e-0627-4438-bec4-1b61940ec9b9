#pragma once

#include "CoreMinimal.h"
#include "Engine/Engine.h"
#include "Json.h"

/**
 * Backend Services Commands for Unreal MCP
 * 
 * This class provides comprehensive backend services integration including:
 * - Firebase (Firestore, Realtime Database, Authentication, Analytics)
 * - Epic Online Services (EOS) (Matchmaking, Social Features, Achievements)
 * - AWS GameLift (Server Hosting, Auto-scaling)
 * - CloudFlare (CDN, DDoS Protection, SSL)
 * - Cloud Saves Management
 * - User Authentication
 * - Social Features
 * - Leaderboards & Achievements
 * - Performance Analytics
 * 
 * All functions are production-ready and based on UE 5.6 official documentation.
 */
class UNREALMCP_API UnrealMCPBackendServicesCommands
{
public:
    // === Firebase Integration ===
    
    /**
     * Setup Firebase integration for backend services
     * @param JsonString JSON parameters containing Firebase configuration
     * @return JSON response with setup status
     */
    static FString HandleSetupFirebaseIntegration(const FString& JsonString);
    
    // === Epic Online Services (EOS) ===
    
    /**
     * Configure Epic Online Services integration
     * @param JsonString JSON parameters containing EOS configuration
     * @return JSON response with configuration status
     */
    static FString HandleConfigureEpicOnlineServices(const FString& JsonString);
    
    // === AWS GameLift ===
    
    /**
     * Setup AWS GameLift for server hosting
     * @param JsonString JSON parameters containing GameLift configuration
     * @return JSON response with setup status
     */
    static FString HandleSetupAWSGameLift(const FString& JsonString);
    
    // === CloudFlare Services ===
    
    /**
     * Configure CloudFlare services for CDN and DDoS protection
     * @param JsonString JSON parameters containing CloudFlare configuration
     * @return JSON response with configuration status
     */
    static FString HandleConfigureCloudFlareServices(const FString& JsonString);
    
    // === Cloud Saves Management ===
    
    /**
     * Manage cloud save functionality
     * @param JsonString JSON parameters containing save management configuration
     * @return JSON response with operation status
     */
    static FString HandleManageCloudSaves(const FString& JsonString);
    
    // === Matchmaking Service ===
    
    /**
     * Setup matchmaking service configuration
     * @param JsonString JSON parameters containing matchmaking configuration
     * @return JSON response with setup status
     */
    static FString HandleSetupMatchmakingService(const FString& JsonString);
    
    // === User Authentication ===
    
    /**
     * Manage user authentication across different providers
     * @param JsonString JSON parameters containing authentication configuration
     * @return JSON response with authentication status
     */
    static FString HandleManageUserAuthentication(const FString& JsonString);
    
    // === Social Features ===
    
    /**
     * Configure social features and systems
     * @param JsonString JSON parameters containing social features configuration
     * @return JSON response with configuration status
     */
    static FString HandleConfigureSocialFeatures(const FString& JsonString);
    
    // === Leaderboards System ===
    
    /**
     * Setup leaderboards system configuration
     * @param JsonString JSON parameters containing leaderboards configuration
     * @return JSON response with setup status
     */
    static FString HandleSetupLeaderboardsSystem(const FString& JsonString);
    
    // === Achievements System ===
    
    /**
     * Manage achievements system operations
     * @param JsonString JSON parameters containing achievements configuration
     * @return JSON response with operation status
     */
    static FString HandleManageAchievementsSystem(const FString& JsonString);
    
    // === Performance Analytics ===
    
    /**
     * Analyze backend services performance and metrics
     * @param JsonString JSON parameters containing analytics configuration
     * @return JSON response with analysis results
     */
    static FString HandleAnalyzeBackendPerformance(const FString& JsonString);

private:
    // === Helper Functions ===
    
    /**
     * Validate Firebase configuration parameters
     * @param JsonObject JSON object containing Firebase parameters
     * @return True if valid, false otherwise
     */
    static bool ValidateFirebaseConfig(const TSharedPtr<FJsonObject>& JsonObject);
    
    /**
     * Validate EOS configuration parameters
     * @param JsonObject JSON object containing EOS parameters
     * @return True if valid, false otherwise
     */
    static bool ValidateEOSConfig(const TSharedPtr<FJsonObject>& JsonObject);
    
    /**
     * Validate AWS GameLift configuration parameters
     * @param JsonObject JSON object containing GameLift parameters
     * @return True if valid, false otherwise
     */
    static bool ValidateGameLiftConfig(const TSharedPtr<FJsonObject>& JsonObject);
    
    /**
     * Validate CloudFlare configuration parameters
     * @param JsonObject JSON object containing CloudFlare parameters
     * @return True if valid, false otherwise
     */
    static bool ValidateCloudFlareConfig(const TSharedPtr<FJsonObject>& JsonObject);
    
    /**
     * Setup Firebase Firestore database
     * @param ProjectId Firebase project ID
     * @param ApiKey Firebase API key
     * @return True if successful
     */
    static bool SetupFirestore(const FString& ProjectId, const FString& ApiKey);
    
    /**
     * Setup Firebase Realtime Database
     * @param DatabaseUrl Firebase database URL
     * @return True if successful
     */
    static bool SetupRealtimeDatabase(const FString& DatabaseUrl);
    
    /**
     * Setup Firebase Authentication
     * @param ProjectId Firebase project ID
     * @param ApiKey Firebase API key
     * @return True if successful
     */
    static bool SetupFirebaseAuth(const FString& ProjectId, const FString& ApiKey);
    
    /**
     * Initialize EOS SDK with credentials
     * @param ProductId EOS Product ID
     * @param SandboxId EOS Sandbox ID
     * @param DeploymentId EOS Deployment ID
     * @param ClientId EOS Client ID
     * @param ClientSecret EOS Client Secret
     * @return True if successful
     */
    static bool InitializeEOS(const FString& ProductId, const FString& SandboxId, 
                             const FString& DeploymentId, const FString& ClientId, 
                             const FString& ClientSecret);
    
    /**
     * Configure EOS achievements system
     * @param bEnable Enable achievements
     * @return True if successful
     */
    static bool ConfigureEOSAchievements(bool bEnable);
    
    /**
     * Configure EOS leaderboards system
     * @param bEnable Enable leaderboards
     * @return True if successful
     */
    static bool ConfigureEOSLeaderboards(bool bEnable);
    
    /**
     * Configure EOS matchmaking system
     * @param bEnable Enable matchmaking
     * @return True if successful
     */
    static bool ConfigureEOSMatchmaking(bool bEnable);
    
    /**
     * Configure EOS social features
     * @param bEnable Enable social features
     * @return True if successful
     */
    static bool ConfigureEOSSocialFeatures(bool bEnable);
    
    /**
     * Setup AWS GameLift fleet configuration
     * @param Region AWS region
     * @param FleetId GameLift fleet ID
     * @param AliasId GameLift alias ID
     * @return True if successful
     */
    static bool SetupGameLiftFleet(const FString& Region, const FString& FleetId, const FString& AliasId);
    
    /**
     * Configure GameLift auto-scaling
     * @param bEnable Enable auto-scaling
     * @param MinInstances Minimum instances
     * @param MaxInstances Maximum instances
     * @param TargetCapacity Target capacity percentage
     * @return True if successful
     */
    static bool ConfigureGameLiftAutoScaling(bool bEnable, int32 MinInstances, int32 MaxInstances, int32 TargetCapacity);
    
    /**
     * Setup CloudFlare CDN configuration
     * @param ZoneId CloudFlare Zone ID
     * @param ApiToken CloudFlare API token
     * @param bEnableCDN Enable CDN
     * @param CacheLevel Cache level
     * @return True if successful
     */
    static bool SetupCloudFlareCDN(const FString& ZoneId, const FString& ApiToken, bool bEnableCDN, const FString& CacheLevel);
    
    /**
     * Configure CloudFlare security settings
     * @param bEnableDDoSProtection Enable DDoS protection
     * @param bEnableSSL Enable SSL
     * @param SecurityLevel Security level
     * @return True if successful
     */
    static bool ConfigureCloudFlareSecurity(bool bEnableDDoSProtection, bool bEnableSSL, const FString& SecurityLevel);
    
    /**
     * Create JSON response with success status
     * @param bSuccess Success status
     * @param Message Response message
     * @param Data Optional data object
     * @return JSON response string
     */
    static FString CreateJsonResponse(bool bSuccess, const FString& Message, const TSharedPtr<FJsonObject>& Data = nullptr);
    
    /**
     * Log backend services operation
     * @param Operation Operation name
     * @param bSuccess Success status
     * @param Message Log message
     */
    static void LogBackendOperation(const FString& Operation, bool bSuccess, const FString& Message);

    // === Constants ===
    
    // Firebase constants
    static const FString FIREBASE_CONFIG_SECTION;
    static const FString FIREBASE_API_ENDPOINT;
    
    // EOS constants
    static const FString EOS_CONFIG_SECTION;
    static const FString EOS_SDK_VERSION;
    
    // AWS GameLift constants
    static const FString GAMELIFT_CONFIG_SECTION;
    static const FString GAMELIFT_API_VERSION;
    
    // CloudFlare constants
    static const FString CLOUDFLARE_CONFIG_SECTION;
    static const FString CLOUDFLARE_API_ENDPOINT;
    
    // Authentication providers
    static const FString AUTH_PROVIDER_EOS;
    static const FString AUTH_PROVIDER_STEAM;
    static const FString AUTH_PROVIDER_FIREBASE;
    static const FString AUTH_PROVIDER_CUSTOM;
    
    // Cache levels
    static const FString CACHE_LEVEL_OFF;
    static const FString CACHE_LEVEL_BASIC;
    static const FString CACHE_LEVEL_STANDARD;
    static const FString CACHE_LEVEL_AGGRESSIVE;
    
    // Security levels
    static const FString SECURITY_LEVEL_OFF;
    static const FString SECURITY_LEVEL_LOW;
    static const FString SECURITY_LEVEL_MEDIUM;
    static const FString SECURITY_LEVEL_HIGH;
    static const FString SECURITY_LEVEL_UNDER_ATTACK;

    // ============================================================================
    // Real Backend Services Functions using UE 5.6 HTTP APIs
    // ============================================================================

    /**
     * Initializes the real HTTP client using UE 5.6 FHttpModule APIs.
     */
    static TSharedPtr<FJsonObject> InitializeRealHTTPClient();

    /**
     * Tests real HTTP connection using UE 5.6 HTTP APIs.
     */
    static TSharedPtr<FJsonObject> TestRealHTTPConnection();
};
