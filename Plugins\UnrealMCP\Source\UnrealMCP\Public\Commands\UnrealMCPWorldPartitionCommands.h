#pragma once

#include "CoreMinimal.h"
#include "Json.h"
#include "Engine/World.h"
#include "WorldPartition/WorldPartition.h"
#include "WorldPartition/WorldPartitionSubsystem.h"

// Forward declarations
class UWorldSettings;

/**
 * Handler class for World Partition-related MCP commands
 * Gerencia comandos relacionados ao sistema de World Partition Multicamada
 */
class UNREALMCP_API FUnrealMCPWorldPartitionCommands
{
public:
    FUnrealMCPWorldPartitionCommands();

    // Handle world partition commands
    TSharedPtr<FJsonObject> HandleCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params);

private:
    // Specific world partition command handlers
    TSharedPtr<FJsonObject> HandleCreateWorldPartitionLevel(const TSharedPtr<FJsonObject>& Params);
    TSharedPtr<FJsonObject> HandleConfigureStreamingCell(const TSharedPtr<FJsonObject>& Params);
    TSharedPtr<FJsonObject> HandleCreateLayerHierarchy(const TSharedPtr<FJsonObject>& Params);
    TSharedPtr<FJsonObject> HandleSetSpatialDivisionRules(const TSharedPtr<FJsonObject>& Params);
    TSharedPtr<FJsonObject> HandleConfigureLayerStreamingManager(const TSharedPtr<FJsonObject>& Params);
    TSharedPtr<FJsonObject> HandleGetWorldPartitionStatus(const TSharedPtr<FJsonObject>& Params);
    TSharedPtr<FJsonObject> HandleOptimizeStreamingCells(const TSharedPtr<FJsonObject>& Params);
    
    // === Auracron Architecture Specific Streaming Commands ===
    TSharedPtr<FJsonObject> HandleStreamInLayer(const TSharedPtr<FJsonObject>& Params);
    TSharedPtr<FJsonObject> HandleStreamOutLayer(const TSharedPtr<FJsonObject>& Params);
    TSharedPtr<FJsonObject> HandlePreloadVerticalConnectors(const TSharedPtr<FJsonObject>& Params);
    TSharedPtr<FJsonObject> HandleGetLayerLoadStates(const TSharedPtr<FJsonObject>& Params);

    // Helper functions
    UWorld* GetCurrentWorld();
    UWorldPartition* GetWorldPartition(UWorld* World);
    bool ValidateLayerType(const FString& LayerType);
    FString GetLayerTypePrefix(const FString& LayerType);
    
    // Layer management helpers
    bool CreateStreamingCell(UWorldPartition* WorldPartition, int32 CellX, int32 CellY, 
                           const FString& LayerType, float StreamingDistance, int32 Priority);
    bool SetupLayerTransition(const FString& ParentLayer, const FString& ChildLayer, 
                            const FString& TransitionType);
    bool ConfigureSpatialDivision(const FString& LayerType, const FString& Algorithm, 
                                int32 MaxDepth, int32 MinObjectsPerCell);
    
    // Streaming optimization helpers
    bool OptimizeCellsForPerformance(UWorldPartition* WorldPartition);
    bool OptimizeCellsForMemory(UWorldPartition* WorldPartition);
    bool OptimizeCellsForQuality(UWorldPartition* WorldPartition);
    
    // Status and monitoring helpers
    TSharedPtr<FJsonObject> GetStreamingCellsStatus(UWorldPartition* WorldPartition);
    TSharedPtr<FJsonObject> GetLayerHierarchyStatus();
    TSharedPtr<FJsonObject> GetMemoryUsageStatus();
    
    // Constants for layer types
    static const FString LAYER_FIRMAMENTO;
    static const FString LAYER_PLANICIE;
    static const FString LAYER_ABISMO;
    
    // Constants for transition types
    static const FString TRANSITION_PORTAL;
    static const FString TRANSITION_SEAMLESS;
    static const FString TRANSITION_TELEPORT;
    
    // Constants for division algorithms
    static const FString ALGORITHM_QUADTREE;
    static const FString ALGORITHM_OCTREE;
    static const FString ALGORITHM_GRID;

    // ============================================================================
    // Real World Partition Performance Analysis Functions
    // ============================================================================

    /**
     * Collects real World Partition performance metrics using UE 5.6 APIs.
     */
    static TSharedPtr<FJsonObject> CollectRealWorldPartitionPerformanceMetrics();
};