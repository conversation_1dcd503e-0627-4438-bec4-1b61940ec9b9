"""
Hardware Detection Tools for Unreal Engine MCP Server

This module provides comprehensive Hardware Detection tools that are 100% compatible with the 
C++ implementations in UnrealMCPHardwareDetectionCommands.cpp. Based on UE 5.6 official documentation.
"""

import logging
import json
from typing import Dict, List, Any, Optional
from mcp.server.fastmcp import FastMCP

# Set up logging
logger = logging.getLogger("UnrealMCP")

def register_hardware_detection_tools(mcp: FastMCP):
    """Register Hardware Detection tools with the MCP server - 100% compatible with C++ implementations."""

    @mcp.tool()
    def detect_system_hardware(
        include_cpu_info: bool = True,
        include_gpu_info: bool = True,
        include_memory_info: bool = True,
        include_storage_info: bool = True,
        detailed_analysis: bool = False
    ) -> Dict[str, Any]:
        """
        Detect and analyze system hardware specifications.
        Compatible with HandleDetectSystemHardware in C++.
        
        Args:
            include_cpu_info: Include CPU information in detection
            include_gpu_info: Include GPU information in detection
            include_memory_info: Include memory information in detection
            include_storage_info: Include storage information in detection
            detailed_analysis: Perform detailed hardware analysis
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "include_cpu_info": include_cpu_info,
                "include_gpu_info": include_gpu_info,
                "include_memory_info": include_memory_info,
                "include_storage_info": include_storage_info,
                "detailed_analysis": detailed_analysis
            }

            response = unreal.send_command("detect_system_hardware", params)
            return response or {"success": True, "message": "System hardware detected successfully"}

        except Exception as e:
            logger.error(f"Error detecting system hardware: {e}")
            return {"success": False, "message": f"Error detecting system hardware: {e}"}

    @mcp.tool()
    def run_hardware_benchmark(
        benchmark_type: str = "Full",
        duration_seconds: int = 30,
        apply_results: bool = True,
        save_results: bool = True,
        output_format: str = "JSON"
    ) -> Dict[str, Any]:
        """
        Run hardware benchmark to determine optimal settings.
        Compatible with HandleRunHardwareBenchmark in C++.
        
        Args:
            benchmark_type: Type of benchmark (Full, Quick, GPU, CPU, Memory)
            duration_seconds: Duration of benchmark in seconds
            apply_results: Automatically apply benchmark results
            save_results: Save benchmark results to file
            output_format: Output format (JSON, CSV, XML)
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "benchmark_type": benchmark_type,
                "duration_seconds": duration_seconds,
                "apply_results": apply_results,
                "save_results": save_results,
                "output_format": output_format
            }

            response = unreal.send_command("run_hardware_benchmark", params)
            return response or {"success": True, "message": "Hardware benchmark completed successfully"}

        except Exception as e:
            logger.error(f"Error running hardware benchmark: {e}")
            return {"success": False, "message": f"Error running hardware benchmark: {e}"}

    @mcp.tool()
    def detect_gpu_capabilities(
        test_ray_tracing: bool = True,
        test_compute_shaders: bool = True,
        test_memory_bandwidth: bool = True,
        test_feature_support: bool = True,
        generate_report: bool = True
    ) -> Dict[str, Any]:
        """
        Detect GPU capabilities and feature support.
        Compatible with HandleDetectGPUCapabilities in C++.
        
        Args:
            test_ray_tracing: Test hardware ray tracing support
            test_compute_shaders: Test compute shader capabilities
            test_memory_bandwidth: Test GPU memory bandwidth
            test_feature_support: Test advanced feature support
            generate_report: Generate detailed capability report
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "test_ray_tracing": test_ray_tracing,
                "test_compute_shaders": test_compute_shaders,
                "test_memory_bandwidth": test_memory_bandwidth,
                "test_feature_support": test_feature_support,
                "generate_report": generate_report
            }

            response = unreal.send_command("detect_gpu_capabilities", params)
            return response or {"success": True, "message": "GPU capabilities detected successfully"}

        except Exception as e:
            logger.error(f"Error detecting GPU capabilities: {e}")
            return {"success": False, "message": f"Error detecting GPU capabilities: {e}"}

    @mcp.tool()
    def analyze_memory_configuration(
        test_allocation_speed: bool = True,
        test_bandwidth: bool = True,
        detect_memory_buckets: bool = True,
        analyze_virtual_memory: bool = True,
        check_memory_pressure: bool = True
    ) -> Dict[str, Any]:
        """
        Analyze system memory configuration and performance.
        Compatible with HandleAnalyzeMemoryConfiguration in C++.
        
        Args:
            test_allocation_speed: Test memory allocation speed
            test_bandwidth: Test memory bandwidth
            detect_memory_buckets: Detect memory bucket classification
            analyze_virtual_memory: Analyze virtual memory usage
            check_memory_pressure: Check for memory pressure indicators
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "test_allocation_speed": test_allocation_speed,
                "test_bandwidth": test_bandwidth,
                "detect_memory_buckets": detect_memory_buckets,
                "analyze_virtual_memory": analyze_virtual_memory,
                "check_memory_pressure": check_memory_pressure
            }

            response = unreal.send_command("analyze_memory_configuration", params)
            return response or {"success": True, "message": "Memory configuration analyzed successfully"}

        except Exception as e:
            logger.error(f"Error analyzing memory configuration: {e}")
            return {"success": False, "message": f"Error analyzing memory configuration: {e}"}

    @mcp.tool()
    def detect_platform_capabilities(
        detect_os_features: bool = True,
        detect_driver_versions: bool = True,
        detect_api_support: bool = True,
        detect_hardware_features: bool = True,
        check_compatibility: bool = True
    ) -> Dict[str, Any]:
        """
        Detect platform-specific capabilities and features.
        Compatible with HandleDetectPlatformCapabilities in C++.
        
        Args:
            detect_os_features: Detect operating system features
            detect_driver_versions: Detect graphics driver versions
            detect_api_support: Detect graphics API support
            detect_hardware_features: Detect hardware-specific features
            check_compatibility: Check UE compatibility
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "detect_os_features": detect_os_features,
                "detect_driver_versions": detect_driver_versions,
                "detect_api_support": detect_api_support,
                "detect_hardware_features": detect_hardware_features,
                "check_compatibility": check_compatibility
            }

            response = unreal.send_command("detect_platform_capabilities", params)
            return response or {"success": True, "message": "Platform capabilities detected successfully"}

        except Exception as e:
            logger.error(f"Error detecting platform capabilities: {e}")
            return {"success": False, "message": f"Error detecting platform capabilities: {e}"}

    @mcp.tool()
    def configure_device_profile(
        auto_detect_profile: bool = True,
        profile_name: str = "",
        memory_bucket: str = "Auto",
        gpu_family: str = "Auto",
        platform_type: str = "Auto"
    ) -> Dict[str, Any]:
        """
        Configure device profile based on hardware detection.
        Compatible with HandleConfigureDeviceProfile in C++.
        
        Args:
            auto_detect_profile: Automatically detect optimal device profile
            profile_name: Specific profile name to apply
            memory_bucket: Memory bucket classification
            gpu_family: GPU family classification
            platform_type: Platform type classification
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "auto_detect_profile": auto_detect_profile,
                "profile_name": profile_name,
                "memory_bucket": memory_bucket,
                "gpu_family": gpu_family,
                "platform_type": platform_type
            }

            response = unreal.send_command("configure_device_profile", params)
            return response or {"success": True, "message": "Device profile configured successfully"}

        except Exception as e:
            logger.error(f"Error configuring device profile: {e}")
            return {"success": False, "message": f"Error configuring device profile: {e}"}

    @mcp.tool()
    def optimize_for_hardware_tier(
        target_tier: str = "Auto",
        target_fps: int = 60,
        quality_preset: str = "Balanced",
        enable_dynamic_scaling: bool = True,
        apply_immediately: bool = True
    ) -> Dict[str, Any]:
        """
        Optimize settings for specific hardware tier.
        Compatible with HandleOptimizeForHardwareTier in C++.

        Args:
            target_tier: Hardware tier (HighEnd, MidRange, LowEnd, Mobile, Auto)
            target_fps: Target frame rate
            quality_preset: Quality preset (Performance, Balanced, Quality)
            enable_dynamic_scaling: Enable dynamic quality scaling
            apply_immediately: Apply optimizations immediately
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "target_tier": target_tier,
                "target_fps": target_fps,
                "quality_preset": quality_preset,
                "enable_dynamic_scaling": enable_dynamic_scaling,
                "apply_immediately": apply_immediately
            }

            response = unreal.send_command("optimize_for_hardware_tier", params)
            return response or {"success": True, "message": "Hardware tier optimization completed successfully"}

        except Exception as e:
            logger.error(f"Error optimizing for hardware tier: {e}")
            return {"success": False, "message": f"Error optimizing for hardware tier: {e}"}

    @mcp.tool()
    def monitor_hardware_performance(
        monitoring_duration: float = 60.0,
        monitor_cpu: bool = True,
        monitor_gpu: bool = True,
        monitor_memory: bool = True,
        monitor_thermal: bool = True,
        generate_alerts: bool = True
    ) -> Dict[str, Any]:
        """
        Monitor hardware performance in real-time.
        Compatible with HandleMonitorHardwarePerformance in C++.

        Args:
            monitoring_duration: Duration of monitoring in seconds
            monitor_cpu: Monitor CPU performance
            monitor_gpu: Monitor GPU performance
            monitor_memory: Monitor memory usage
            monitor_thermal: Monitor thermal conditions
            generate_alerts: Generate performance alerts
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "monitoring_duration": monitoring_duration,
                "monitor_cpu": monitor_cpu,
                "monitor_gpu": monitor_gpu,
                "monitor_memory": monitor_memory,
                "monitor_thermal": monitor_thermal,
                "generate_alerts": generate_alerts
            }

            response = unreal.send_command("monitor_hardware_performance", params)
            return response or {"success": True, "message": "Hardware performance monitoring completed successfully"}

        except Exception as e:
            logger.error(f"Error monitoring hardware performance: {e}")
            return {"success": False, "message": f"Error monitoring hardware performance: {e}"}

    @mcp.tool()
    def generate_hardware_report(
        include_specifications: bool = True,
        include_benchmarks: bool = True,
        include_compatibility: bool = True,
        include_recommendations: bool = True,
        output_format: str = "JSON",
        save_to_file: bool = True
    ) -> Dict[str, Any]:
        """
        Generate comprehensive hardware analysis report.
        Compatible with HandleGenerateHardwareReport in C++.

        Args:
            include_specifications: Include hardware specifications
            include_benchmarks: Include benchmark results
            include_compatibility: Include compatibility analysis
            include_recommendations: Include optimization recommendations
            output_format: Output format (JSON, HTML, PDF, CSV)
            save_to_file: Save report to file
        """
        from unreal_mcp_server import get_unreal_connection

        try:
            unreal = get_unreal_connection()
            if not unreal:
                return {"success": False, "message": "Failed to connect to Unreal Engine"}

            params = {
                "include_specifications": include_specifications,
                "include_benchmarks": include_benchmarks,
                "include_compatibility": include_compatibility,
                "include_recommendations": include_recommendations,
                "output_format": output_format,
                "save_to_file": save_to_file
            }

            response = unreal.send_command("generate_hardware_report", params)
            return response or {"success": True, "message": "Hardware report generated successfully"}

        except Exception as e:
            logger.error(f"Error generating hardware report: {e}")
            return {"success": False, "message": f"Error generating hardware report: {e}"}
