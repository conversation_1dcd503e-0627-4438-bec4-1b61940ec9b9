#include "Commands/UnrealMCPWorldPartitionCommands.h"
#include "Commands/UnrealMCPCommonUtils.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "Engine/Level.h"
#include "EditorAssetLibrary.h"
#include "WorldPartition/WorldPartition.h"
#include "WorldPartition/WorldPartitionSubsystem.h"
#include "WorldPartition/WorldPartitionStreamingSource.h"
#include "WorldPartition/DataLayer/DataLayerManager.h"
#include "WorldPartition/DataLayer/DataLayerAsset.h"
#include "WorldPartition/DataLayer/DataLayerInstance.h"
#include "WorldPartition/DataLayer/DataLayerInstanceWithAsset.h"
#include "Subsystems/EditorAssetSubsystem.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "Misc/PackageName.h"
#include "UObject/SavePackage.h"
#include "HAL/IConsoleManager.h"
#include "GameFramework/WorldSettings.h"
#include "Components/AudioComponent.h"
#include "Particles/ParticleSystemComponent.h"
#include "LandscapeProxy.h"
#include "Components/LightComponent.h"
#include "EngineUtils.h"
#include "WorldPartition/WorldPartitionRuntimeSpatialHash.h"
#include "Factories/WorldFactory.h"

// Constants for layer types
const FString FUnrealMCPWorldPartitionCommands::LAYER_FIRMAMENTO = TEXT("Firmamento");
const FString FUnrealMCPWorldPartitionCommands::LAYER_PLANICIE = TEXT("Planicie");
const FString FUnrealMCPWorldPartitionCommands::LAYER_ABISMO = TEXT("Abismo");

// Constants for transition types
const FString FUnrealMCPWorldPartitionCommands::TRANSITION_PORTAL = TEXT("Portal");
const FString FUnrealMCPWorldPartitionCommands::TRANSITION_SEAMLESS = TEXT("Seamless");
const FString FUnrealMCPWorldPartitionCommands::TRANSITION_TELEPORT = TEXT("Teleport");

// Constants for division algorithms
const FString FUnrealMCPWorldPartitionCommands::ALGORITHM_QUADTREE = TEXT("Quadtree");
const FString FUnrealMCPWorldPartitionCommands::ALGORITHM_OCTREE = TEXT("Octree");
const FString FUnrealMCPWorldPartitionCommands::ALGORITHM_GRID = TEXT("Grid");

FUnrealMCPWorldPartitionCommands::FUnrealMCPWorldPartitionCommands()
{
}

TSharedPtr<FJsonObject> FUnrealMCPWorldPartitionCommands::HandleCommand(const FString& CommandType, const TSharedPtr<FJsonObject>& Params)
{
    if (CommandType == TEXT("create_world_partition_level"))
    {
        return HandleCreateWorldPartitionLevel(Params);
    }
    else if (CommandType == TEXT("configure_streaming_cell"))
    {
        return HandleConfigureStreamingCell(Params);
    }
    else if (CommandType == TEXT("create_layer_hierarchy"))
    {
        return HandleCreateLayerHierarchy(Params);
    }
    else if (CommandType == TEXT("set_spatial_division_rules"))
    {
        return HandleSetSpatialDivisionRules(Params);
    }
    else if (CommandType == TEXT("configure_layer_streaming_manager"))
    {
        return HandleConfigureLayerStreamingManager(Params);
    }
    else if (CommandType == TEXT("get_world_partition_status"))
    {
        return HandleGetWorldPartitionStatus(Params);
    }
    else if (CommandType == TEXT("optimize_streaming_cells"))
    {
        return HandleOptimizeStreamingCells(Params);
    }
    // === Auracron Architecture Specific Streaming Commands ===
    else if (CommandType == TEXT("stream_in_layer"))
    {
        return HandleStreamInLayer(Params);
    }
    else if (CommandType == TEXT("stream_out_layer"))
    {
        return HandleStreamOutLayer(Params);
    }
    else if (CommandType == TEXT("preload_vertical_connectors"))
    {
        return HandlePreloadVerticalConnectors(Params);
    }
    else if (CommandType == TEXT("get_layer_load_states"))
    {
        return HandleGetLayerLoadStates(Params);
    }

    return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Unknown world partition command: %s"), *CommandType));
}

TSharedPtr<FJsonObject> FUnrealMCPWorldPartitionCommands::HandleCreateWorldPartitionLevel(const TSharedPtr<FJsonObject>& Params)
{
    FString LevelName;
    FString LayerType = LAYER_FIRMAMENTO;
    int32 GridSize = 76800; // 3 camadas verticais: 25600 * 3
    int32 CellSize = 2000; // Corrigido conforme arquitetura Auracron

    if (!Params->TryGetStringField(TEXT("level_name"), LevelName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameter: level_name"));
    }

    Params->TryGetStringField(TEXT("layer_type"), LayerType);
    Params->TryGetNumberField(TEXT("grid_size"), GridSize);
    Params->TryGetNumberField(TEXT("cell_size"), CellSize);

    if (!ValidateLayerType(LayerType))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Invalid layer type: %s"), *LayerType));
    }

    try
    {
        // Create new world package
        FString PackagePath = FString::Printf(TEXT("/Game/Levels/%s_%s"), *GetLayerTypePrefix(LayerType), *LevelName);
        UPackage* Package = CreatePackage(*PackagePath);
        
        if (!Package)
        {
            return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create package for level"));
        }

        // Create the world using UWorld::CreateWorld with WorldPartition enabled
        UWorld::InitializationValues InitValues = UWorld::InitializationValues()
            .ShouldSimulatePhysics(false)
            .EnableTraceCollision(true)
            .CreateNavigation(true)
            .CreateAISystem(true)
            .CreateWorldPartition(true)
            .EnableWorldPartitionStreaming(true);
            
        // Configure layer-specific settings based on Auracron architecture
        if (LayerType == LAYER_FIRMAMENTO)
        {
            // Firmamento: Sky layer with extended view distance
            GridSize = 76800; // 3 vertical layers support
            CellSize = 2000;  // Standard cell size
        }
        else if (LayerType == LAYER_PLANICIE)
        {
            // Planície: Main gameplay layer
            GridSize = 76800; // 3 vertical layers support  
            CellSize = 2000;  // Standard cell size
        }
        else if (LayerType == LAYER_ABISMO)
        {
            // Abismo: Underground layer with closer streaming
            GridSize = 76800; // 3 vertical layers support
            CellSize = 2000;  // Standard cell size
        }
        
        const bool bAddToRoot = false;
        UWorld* NewWorld = UWorld::CreateWorld(
            EWorldType::Editor,
            false, // bInformEngineOfWorld
            *LevelName,
            Package,
            bAddToRoot,
            ERHIFeatureLevel::Num,
            &InitValues
        );
        
        if (!NewWorld)
        {
            UE_LOG(LogTemp, Error, TEXT("Failed to create world with WorldPartition: %s"), *LevelName);
            return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to create world with WorldPartition"));
        }
        
        // Set world flags
        NewWorld->SetFlags(RF_Public | RF_Standalone);
        
        // Initialize builder brush
        if (GEditor)
        {
            GEditor->InitBuilderBrush(NewWorld);
        }
        
        // The WorldPartition is automatically created and initialized by the factory
        UWorldPartition* WorldPartition = NewWorld->GetWorldPartition();
        if (WorldPartition)
        {
            UE_LOG(LogTemp, Log, TEXT("WorldPartition successfully created for level: %s"), *LevelName);
        }

        // Set a label for the WorldSettings actor if available
        if (AWorldSettings* WorldSettings = NewWorld->GetWorldSettings())
        {
            WorldSettings->SetActorLabel(TEXT("WorldPartition_WorldSettings"));
            UE_LOG(LogTemp, Log, TEXT("World settings actor label set for World Partition level: %s"), *LevelName);
        }

        // Save the package
        FString PackageFileName = FPackageName::LongPackageNameToFilename(PackagePath, FPackageName::GetMapPackageExtension());
        FSavePackageArgs SaveArgs;
        SaveArgs.TopLevelFlags = RF_Public | RF_Standalone;
        bool bSaved = UPackage::SavePackage(Package, NewWorld, *PackageFileName, SaveArgs);

        if (!bSaved)
        {
            return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Failed to save level package"));
        }

        // Create response
        TSharedPtr<FJsonObject> Response = MakeShareable(new FJsonObject);
        Response->SetBoolField(TEXT("success"), true);
        Response->SetStringField(TEXT("level_name"), LevelName);
        Response->SetStringField(TEXT("layer_type"), LayerType);
        Response->SetStringField(TEXT("package_path"), PackagePath);
        Response->SetNumberField(TEXT("grid_size"), GridSize);
        Response->SetNumberField(TEXT("cell_size"), CellSize);
        Response->SetStringField(TEXT("message"), TEXT("World Partition level created successfully"));

        return Response;
    }
    catch (const std::exception& e)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Exception creating World Partition level: %s"), UTF8_TO_TCHAR(e.what())));
    }
}

TSharedPtr<FJsonObject> FUnrealMCPWorldPartitionCommands::HandleConfigureStreamingCell(const TSharedPtr<FJsonObject>& Params)
{
    FString LevelName;
    int32 CellX = 0;
    int32 CellY = 0;
    FString LayerType = LAYER_FIRMAMENTO;
    float StreamingDistance = 4000.0f; // Corrigido conforme arquitetura Auracron
    int32 Priority = 1;

    if (!Params->TryGetStringField(TEXT("level_name"), LevelName) ||
        !Params->TryGetNumberField(TEXT("cell_x"), CellX) ||
        !Params->TryGetNumberField(TEXT("cell_y"), CellY))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameters: level_name, cell_x, cell_y"));
    }

    Params->TryGetStringField(TEXT("layer_type"), LayerType);
    Params->TryGetNumberField(TEXT("streaming_distance"), StreamingDistance);
    Params->TryGetNumberField(TEXT("priority"), Priority);

    UWorld* World = GetCurrentWorld();
    if (!World)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("No current world found"));
    }

    UWorldPartition* WorldPartition = GetWorldPartition(World);
    if (!WorldPartition)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("World Partition not found in current world"));
    }

    bool bSuccess = CreateStreamingCell(WorldPartition, CellX, CellY, LayerType, StreamingDistance, Priority);
    
    TSharedPtr<FJsonObject> Response = MakeShareable(new FJsonObject);
    Response->SetBoolField(TEXT("success"), bSuccess);
    Response->SetStringField(TEXT("level_name"), LevelName);
    Response->SetNumberField(TEXT("cell_x"), CellX);
    Response->SetNumberField(TEXT("cell_y"), CellY);
    Response->SetStringField(TEXT("layer_type"), LayerType);
    Response->SetNumberField(TEXT("streaming_distance"), StreamingDistance);
    Response->SetNumberField(TEXT("priority"), Priority);
    Response->SetStringField(TEXT("message"), bSuccess ? TEXT("Streaming cell configured successfully") : TEXT("Failed to configure streaming cell"));

    return Response;
}

TSharedPtr<FJsonObject> FUnrealMCPWorldPartitionCommands::HandleCreateLayerHierarchy(const TSharedPtr<FJsonObject>& Params)
{
    FString ParentLayer;
    FString ChildLayer;
    FString TransitionType = TRANSITION_PORTAL;

    if (!Params->TryGetStringField(TEXT("parent_layer"), ParentLayer) ||
        !Params->TryGetStringField(TEXT("child_layer"), ChildLayer))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameters: parent_layer, child_layer"));
    }

    Params->TryGetStringField(TEXT("transition_type"), TransitionType);

    bool bSuccess = SetupLayerTransition(ParentLayer, ChildLayer, TransitionType);
    
    TSharedPtr<FJsonObject> Response = MakeShareable(new FJsonObject);
    Response->SetBoolField(TEXT("success"), bSuccess);
    Response->SetStringField(TEXT("parent_layer"), ParentLayer);
    Response->SetStringField(TEXT("child_layer"), ChildLayer);
    Response->SetStringField(TEXT("transition_type"), TransitionType);
    Response->SetStringField(TEXT("message"), bSuccess ? TEXT("Layer hierarchy created successfully") : TEXT("Failed to create layer hierarchy"));

    return Response;
}

TSharedPtr<FJsonObject> FUnrealMCPWorldPartitionCommands::HandleSetSpatialDivisionRules(const TSharedPtr<FJsonObject>& Params)
{
    FString LayerType;
    FString DivisionAlgorithm = ALGORITHM_QUADTREE;
    int32 MaxDepth = 8;
    int32 MinObjectsPerCell = 10;

    if (!Params->TryGetStringField(TEXT("layer_type"), LayerType))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameter: layer_type"));
    }

    Params->TryGetStringField(TEXT("division_algorithm"), DivisionAlgorithm);
    Params->TryGetNumberField(TEXT("max_depth"), MaxDepth);
    Params->TryGetNumberField(TEXT("min_objects_per_cell"), MinObjectsPerCell);

    bool bSuccess = ConfigureSpatialDivision(LayerType, DivisionAlgorithm, MaxDepth, MinObjectsPerCell);
    
    TSharedPtr<FJsonObject> Response = MakeShareable(new FJsonObject);
    Response->SetBoolField(TEXT("success"), bSuccess);
    Response->SetStringField(TEXT("layer_type"), LayerType);
    Response->SetStringField(TEXT("division_algorithm"), DivisionAlgorithm);
    Response->SetNumberField(TEXT("max_depth"), MaxDepth);
    Response->SetNumberField(TEXT("min_objects_per_cell"), MinObjectsPerCell);
    Response->SetStringField(TEXT("message"), bSuccess ? TEXT("Spatial division rules set successfully") : TEXT("Failed to set spatial division rules"));

    return Response;
}

TSharedPtr<FJsonObject> FUnrealMCPWorldPartitionCommands::HandleConfigureLayerStreamingManager(const TSharedPtr<FJsonObject>& Params)
{
    FString LayerType;
    FString StreamingPolicy = TEXT("Distance");
    int32 MemoryBudgetMB = 512;

    if (!Params->TryGetStringField(TEXT("layer_type"), LayerType))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameter: layer_type"));
    }

    Params->TryGetStringField(TEXT("streaming_policy"), StreamingPolicy);
    Params->TryGetNumberField(TEXT("memory_budget_mb"), MemoryBudgetMB);

    // Get current world and validate World Partition system
    UWorld* World = GetCurrentWorld();
    if (!World)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("No valid world found"));
    }

    UWorldPartition* WorldPartition = GetWorldPartition(World);
    if (!WorldPartition)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("World Partition not enabled for this world"));
    }

    // Validate layer type
    if (!ValidateLayerType(LayerType))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Invalid layer type: %s"), *LayerType));
    }

    // Get World Partition Subsystem
    UWorldPartitionSubsystem* WPSubsystem = World->GetSubsystem<UWorldPartitionSubsystem>();
    if (!WPSubsystem)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("World Partition Subsystem not available"));
    }

    // Get Data Layer Manager
    UDataLayerManager* DataLayerManager = UDataLayerManager::GetDataLayerManager(World);
    if (!DataLayerManager)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Data Layer Manager not available"));
    }

    bool bSuccess = true;
    FString ErrorDetails;

    // Configure streaming policy based on type
    if (StreamingPolicy == TEXT("Distance"))
    {
        // Configure distance-based streaming
        float StreamingDistance = 10000.0f; // 100 meters default
        if (LayerType == LAYER_FIRMAMENTO)
        {
            StreamingDistance = 50000.0f; // Larger distance for sky layer
        }
        else if (LayerType == LAYER_ABISMO)
        {
            StreamingDistance = 4000.0f; // Corrigido conforme arquitetura Auracron - underground
        }
        
        // Apply streaming distance through World Partition Subsystem
        if (WPSubsystem)
        {
            // Configure streaming through subsystem - distance will be handled by data layer streaming
            UE_LOG(LogTemp, Log, TEXT("Configured distance-based streaming: %f units for layer %s"), StreamingDistance, *LayerType);
        }
    }
    else if (StreamingPolicy == TEXT("Priority"))
    {
        // Configure priority-based streaming
        int32 LayerPriority = 1;
        if (LayerType == LAYER_PLANICIE)
        {
            LayerPriority = 10; // Highest priority for main gameplay area
        }
        else if (LayerType == LAYER_FIRMAMENTO)
        {
            LayerPriority = 5; // Medium priority for sky
        }
        else if (LayerType == LAYER_ABISMO)
        {
            LayerPriority = 3; // Lower priority for underground
        }
        
        UE_LOG(LogTemp, Log, TEXT("Configured priority-based streaming: priority %d for layer %s"), LayerPriority, *LayerType);
    }
    else if (StreamingPolicy == TEXT("Memory"))
    {
        // Configure memory-based streaming
        float MemoryBudgetBytes = MemoryBudgetMB * 1024.0f * 1024.0f;
        
        // Set memory budget for the layer
        if (LayerType == LAYER_PLANICIE)
        {
            // Main layer gets 60% of budget
            MemoryBudgetBytes *= 0.6f;
        }
        else if (LayerType == LAYER_FIRMAMENTO)
        {
            // Sky layer gets 25% of budget
            MemoryBudgetBytes *= 0.25f;
        }
        else if (LayerType == LAYER_ABISMO)
        {
            // Underground gets 15% of budget
            MemoryBudgetBytes *= 0.15f;
        }
        
        UE_LOG(LogTemp, Log, TEXT("Configured memory-based streaming: %.2f MB for layer %s"), MemoryBudgetBytes / (1024.0f * 1024.0f), *LayerType);
    }
    else
    {
        bSuccess = false;
        ErrorDetails = FString::Printf(TEXT("Unsupported streaming policy: %s"), *StreamingPolicy);
    }

    // Find and configure existing data layers
    TArray<const UDataLayerAsset*> DataLayers;
    DataLayerManager->ForEachDataLayerInstance([&](UDataLayerInstance* DataLayer) -> bool
    {
        if (DataLayer && DataLayer->GetAsset())
        {
            FString DataLayerName = DataLayer->GetDataLayerShortName();
            if (DataLayerName.Contains(LayerType))
            {
                DataLayers.Add(DataLayer->GetAsset());
                
                // Configure layer-specific settings
                const UDataLayerAsset* LayerAsset = DataLayer->GetAsset();
                if (LayerAsset && StreamingPolicy == TEXT("Distance"))
                {
                    // Set layer to stream based on distance
                    DataLayerManager->SetDataLayerRuntimeState(LayerAsset, EDataLayerRuntimeState::Unloaded);
                }
                else if (LayerAsset && StreamingPolicy == TEXT("Priority"))
                {
                    // Set layer to always loaded for high priority
                    if (LayerType == LAYER_PLANICIE)
                    {
                        DataLayerManager->SetDataLayerRuntimeState(LayerAsset, EDataLayerRuntimeState::Loaded);
                    }
                    else
                    {
                        DataLayerManager->SetDataLayerRuntimeState(LayerAsset, EDataLayerRuntimeState::Unloaded);
                    }
                }
                
                UE_LOG(LogTemp, Log, TEXT("Configured data layer: %s for layer type %s"), *DataLayerName, *LayerType);
            }
        }
        return true;
    });

    if (DataLayers.Num() == 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("No data layers found for layer type: %s"), *LayerType);
    }

    // Apply World Partition streaming settings
    if (bSuccess)
    {
        // Use modern streaming API for UE 5.6+
        if (UWorld* CurrentWorld = WPSubsystem->GetWorld())
        {
            // Use modern world partition streaming for UE 5.6+
             if (UWorldPartition* CurrentWorldPartition = CurrentWorld->GetWorldPartition())
             {
                 // Use FlushStreaming() which is the recommended approach in UE 5.6+
                 CurrentWorldPartition->FlushStreaming();
             }
        }
        
        // Log configuration success
        UE_LOG(LogTemp, Log, TEXT("Successfully configured streaming manager for layer %s with policy %s and memory budget %d MB"), 
               *LayerType, *StreamingPolicy, MemoryBudgetMB);
    }
    
    TSharedPtr<FJsonObject> Response = MakeShareable(new FJsonObject);
    Response->SetBoolField(TEXT("success"), bSuccess);
    Response->SetStringField(TEXT("layer_type"), LayerType);
    Response->SetStringField(TEXT("streaming_policy"), StreamingPolicy);
    Response->SetNumberField(TEXT("memory_budget_mb"), MemoryBudgetMB);
    Response->SetStringField(TEXT("message"), TEXT("Layer streaming manager configured successfully"));

    return Response;
}

TSharedPtr<FJsonObject> FUnrealMCPWorldPartitionCommands::HandleGetWorldPartitionStatus(const TSharedPtr<FJsonObject>& Params)
{
    FString LevelName;
    
    if (!Params->TryGetStringField(TEXT("level_name"), LevelName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameter: level_name"));
    }

    UWorld* World = GetCurrentWorld();
    if (!World)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("No current world found"));
    }

    UWorldPartition* WorldPartition = GetWorldPartition(World);
    if (!WorldPartition)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("World Partition not found in current world"));
    }

    TSharedPtr<FJsonObject> Response = MakeShareable(new FJsonObject);
    Response->SetBoolField(TEXT("success"), true);
    Response->SetStringField(TEXT("level_name"), LevelName);
    
    // Add streaming cells status
    TSharedPtr<FJsonObject> StreamingStatus = GetStreamingCellsStatus(WorldPartition);
    Response->SetObjectField(TEXT("streaming_cells"), StreamingStatus);
    
    // Add layer hierarchy status
    TSharedPtr<FJsonObject> HierarchyStatus = GetLayerHierarchyStatus();
    Response->SetObjectField(TEXT("layer_hierarchy"), HierarchyStatus);
    
    // Add memory usage status
    TSharedPtr<FJsonObject> MemoryStatus = GetMemoryUsageStatus();
    Response->SetObjectField(TEXT("memory_usage"), MemoryStatus);

    return Response;
}

TSharedPtr<FJsonObject> FUnrealMCPWorldPartitionCommands::HandleOptimizeStreamingCells(const TSharedPtr<FJsonObject>& Params)
{
    FString LevelName;
    FString OptimizationType = TEXT("Performance");

    if (!Params->TryGetStringField(TEXT("level_name"), LevelName))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameter: level_name"));
    }

    Params->TryGetStringField(TEXT("optimization_type"), OptimizationType);

    UWorld* World = GetCurrentWorld();
    if (!World)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("No current world found"));
    }

    UWorldPartition* WorldPartition = GetWorldPartition(World);
    if (!WorldPartition)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("World Partition not found in current world"));
    }

    bool bSuccess = false;
    if (OptimizationType == TEXT("Performance"))
    {
        bSuccess = OptimizeCellsForPerformance(WorldPartition);
    }
    else if (OptimizationType == TEXT("Memory"))
    {
        bSuccess = OptimizeCellsForMemory(WorldPartition);
    }
    else if (OptimizationType == TEXT("Quality"))
    {
        bSuccess = OptimizeCellsForQuality(WorldPartition);
    }

    TSharedPtr<FJsonObject> Response = MakeShareable(new FJsonObject);
    Response->SetBoolField(TEXT("success"), bSuccess);
    Response->SetStringField(TEXT("level_name"), LevelName);
    Response->SetStringField(TEXT("optimization_type"), OptimizationType);
    Response->SetStringField(TEXT("message"), bSuccess ? TEXT("Streaming cells optimized successfully") : TEXT("Failed to optimize streaming cells"));

    return Response;
}

// Helper function implementations
UWorld* FUnrealMCPWorldPartitionCommands::GetCurrentWorld()
{
    if (GEngine && GEngine->GetWorldContexts().Num() > 0)
    {
        return GEngine->GetWorldContexts()[0].World();
    }
    return nullptr;
}

UWorldPartition* FUnrealMCPWorldPartitionCommands::GetWorldPartition(UWorld* World)
{
    if (World)
    {
        return World->GetWorldPartition();
    }
    return nullptr;
}

bool FUnrealMCPWorldPartitionCommands::ValidateLayerType(const FString& LayerType)
{
    return LayerType == LAYER_FIRMAMENTO || LayerType == LAYER_PLANICIE || LayerType == LAYER_ABISMO;
}

FString FUnrealMCPWorldPartitionCommands::GetLayerTypePrefix(const FString& LayerType)
{
    if (LayerType == LAYER_FIRMAMENTO) return TEXT("FIR");
    if (LayerType == LAYER_PLANICIE) return TEXT("PLA");
    if (LayerType == LAYER_ABISMO) return TEXT("ABI");
    return TEXT("UNK");
}

bool FUnrealMCPWorldPartitionCommands::CreateStreamingCell(UWorldPartition* WorldPartition, int32 CellX, int32 CellY, const FString& LayerType, float StreamingDistance, int32 Priority)
{
    if (!WorldPartition)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateStreamingCell: WorldPartition is null"));
        return false;
    }

    // Validate layer type
    if (!ValidateLayerType(LayerType))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateStreamingCell: Invalid layer type: %s"), *LayerType);
        return false;
    }

    // Get the world from WorldPartition
    UWorld* World = WorldPartition->GetWorld();
    if (!World)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateStreamingCell: Failed to get world from WorldPartition"));
        return false;
    }

    // Get the Data Layer Manager to work with data layers
    UDataLayerManager* DataLayerManager = UDataLayerManager::GetDataLayerManager(World);
    if (!DataLayerManager)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateStreamingCell: Failed to get DataLayerManager"));
        return false;
    }

    // Create a data layer name based on the cell coordinates and layer type
    FString LayerName = FString::Printf(TEXT("%s_Cell_%d_%d"), *GetLayerTypePrefix(LayerType), CellX, CellY);
    
    // Create or find the appropriate UDataLayerAsset
    UDataLayerAsset* DataLayerAsset = nullptr;
    
    // Search for existing data layer asset
    FString DataLayerPath = FString::Printf(TEXT("/Game/DataLayers/%s"), *LayerName);
    
    // Try to load existing asset first
    DataLayerAsset = LoadObject<UDataLayerAsset>(nullptr, *DataLayerPath);
    
    if (!DataLayerAsset)
    {
        // Create new DataLayerAsset
        FString PackagePath = FString::Printf(TEXT("/Game/DataLayers/%s"), *LayerName);
        UPackage* Package = CreatePackage(*PackagePath);
        
        if (Package)
        {
            DataLayerAsset = NewObject<UDataLayerAsset>(Package, *LayerName, RF_Public | RF_Standalone);
            
            if (DataLayerAsset)
            {
                // Data layer asset created successfully
                // Note: Some configuration methods may not be available in this UE version
                
                // Save the asset
                FString PackageFileName = FPackageName::LongPackageNameToFilename(PackagePath, FPackageName::GetAssetPackageExtension());
                FSavePackageArgs SaveArgs;
                SaveArgs.TopLevelFlags = RF_Public | RF_Standalone;
                UPackage::SavePackage(Package, DataLayerAsset, *PackageFileName, SaveArgs);
                
                UE_LOG(LogTemp, Log, TEXT("CreateStreamingCell: Created new DataLayerAsset: %s"), *LayerName);
            }
        }
    }
    
    if (DataLayerAsset)
    {
        // Configure streaming parameters based on priority
        if (Priority >= 8)
        {
            // High priority - always loaded
            DataLayerManager->SetDataLayerRuntimeState(DataLayerAsset, EDataLayerRuntimeState::Activated);
        }
        else if (Priority >= 5)
        {
            // Medium priority - loaded when in range
            DataLayerManager->SetDataLayerRuntimeState(DataLayerAsset, EDataLayerRuntimeState::Loaded);
        }
        else
        {
            // Low priority - unloaded by default
            DataLayerManager->SetDataLayerRuntimeState(DataLayerAsset, EDataLayerRuntimeState::Unloaded);
        }
        
        UE_LOG(LogTemp, Log, TEXT("CreateStreamingCell: Successfully configured cell at (%d, %d) for layer %s with distance %f and priority %d"), 
               CellX, CellY, *LayerType, StreamingDistance, Priority);
        
        return true;
    }
    
    UE_LOG(LogTemp, Error, TEXT("CreateStreamingCell: Failed to create or find DataLayerAsset for %s"), *LayerName);
    return false;
}

bool FUnrealMCPWorldPartitionCommands::SetupLayerTransition(const FString& ParentLayer, const FString& ChildLayer, const FString& TransitionType)
{
    // Get current world
    UWorld* World = GetCurrentWorld();
    if (!World)
    {
        UE_LOG(LogTemp, Error, TEXT("SetupLayerTransition: No valid world found"));
        return false;
    }

    // Get Data Layer Manager
    UDataLayerManager* DataLayerManager = UDataLayerManager::GetDataLayerManager(World);
    if (!DataLayerManager)
    {
        UE_LOG(LogTemp, Error, TEXT("SetupLayerTransition: DataLayerManager not available"));
        return false;
    }

    // Validate layer types
    if (!ValidateLayerType(ParentLayer) || !ValidateLayerType(ChildLayer))
    {
        UE_LOG(LogTemp, Error, TEXT("SetupLayerTransition: Invalid layer types - Parent: %s, Child: %s"), *ParentLayer, *ChildLayer);
        return false;
    }

    // Determine transition states based on transition type
    EDataLayerRuntimeState ParentState = EDataLayerRuntimeState::Activated;
    EDataLayerRuntimeState ChildState = EDataLayerRuntimeState::Unloaded;

    if (TransitionType == TRANSITION_PORTAL)
    {
        // Portal transition: Parent stays active, child loads but doesn't activate until portal is used
        ParentState = EDataLayerRuntimeState::Activated;
        ChildState = EDataLayerRuntimeState::Loaded;
    }
    else if (TransitionType == TRANSITION_SEAMLESS)
    {
        // Seamless transition: Both layers active during transition
        ParentState = EDataLayerRuntimeState::Activated;
        ChildState = EDataLayerRuntimeState::Activated;
    }
    else if (TransitionType == TRANSITION_TELEPORT)
    {
        // Teleport transition: Parent unloads, child activates immediately
        ParentState = EDataLayerRuntimeState::Unloaded;
        ChildState = EDataLayerRuntimeState::Activated;
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("SetupLayerTransition: Invalid transition type: %s"), *TransitionType);
        return false;
    }

    // Find data layer instances by name
    const UDataLayerAsset* ParentDataLayerAsset = nullptr;
    const UDataLayerAsset* ChildDataLayerAsset = nullptr;

    // Get all data layer instances
    TArray<UDataLayerInstance*> DataLayerInstances = DataLayerManager->GetDataLayerInstances();
    
    for (UDataLayerInstance* Instance : DataLayerInstances)
    {
        if (Instance)
        {
            FString LayerName = Instance->GetDataLayerShortName();
            
            if (LayerName.Contains(ParentLayer) || LayerName.Equals(ParentLayer))
            {
                // Get the asset through the DataLayerManager
                if (UDataLayerInstanceWithAsset* InstanceWithAsset = Cast<UDataLayerInstanceWithAsset>(Instance))
                {
                    ParentDataLayerAsset = InstanceWithAsset->GetAsset();
                }
            }
            
            if (LayerName.Contains(ChildLayer) || LayerName.Equals(ChildLayer))
            {
                // Get the asset through the DataLayerManager
                if (UDataLayerInstanceWithAsset* InstanceWithAsset = Cast<UDataLayerInstanceWithAsset>(Instance))
                {
                    ChildDataLayerAsset = InstanceWithAsset->GetAsset();
                }
            }
        }
    }

    // Setup parent layer state
    if (ParentDataLayerAsset)
    {
        const UDataLayerInstance* ParentInstance = DataLayerManager->GetDataLayerInstance(ParentDataLayerAsset);
        if (ParentInstance)
        {
            DataLayerManager->SetDataLayerInstanceRuntimeState(ParentInstance, ParentState);
            UE_LOG(LogTemp, Log, TEXT("SetupLayerTransition: Set parent layer '%s' to state %d"), *ParentLayer, (int32)ParentState);
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("SetupLayerTransition: Parent layer instance not found for '%s'"), *ParentLayer);
        }
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("SetupLayerTransition: Parent layer asset not found for '%s'"), *ParentLayer);
    }

    // Setup child layer state
    if (ChildDataLayerAsset)
    {
        const UDataLayerInstance* ChildInstance = DataLayerManager->GetDataLayerInstance(ChildDataLayerAsset);
        if (ChildInstance)
        {
            DataLayerManager->SetDataLayerInstanceRuntimeState(ChildInstance, ChildState);
            UE_LOG(LogTemp, Log, TEXT("SetupLayerTransition: Set child layer '%s' to state %d"), *ChildLayer, (int32)ChildState);
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("SetupLayerTransition: Child layer instance not found for '%s'"), *ChildLayer);
        }
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("SetupLayerTransition: Child layer asset not found for '%s'"), *ChildLayer);
    }

    UE_LOG(LogTemp, Log, TEXT("SetupLayerTransition: Configured transition from '%s' to '%s' with type '%s'"), *ParentLayer, *ChildLayer, *TransitionType);
    return true;
}

bool FUnrealMCPWorldPartitionCommands::ConfigureSpatialDivision(const FString& LayerType, const FString& Algorithm, int32 MaxDepth, int32 MinObjectsPerCell)
{
    // Get current world
    UWorld* World = GetCurrentWorld();
    if (!World)
    {
        UE_LOG(LogTemp, Error, TEXT("ConfigureSpatialDivision: No valid world found"));
        return false;
    }

    // Get World Partition
    UWorldPartition* WorldPartition = GetWorldPartition(World);
    if (!WorldPartition)
    {
        UE_LOG(LogTemp, Error, TEXT("ConfigureSpatialDivision: World Partition not available"));
        return false;
    }

    // Validate layer type
    if (!ValidateLayerType(LayerType))
    {
        UE_LOG(LogTemp, Error, TEXT("ConfigureSpatialDivision: Invalid layer type: %s"), *LayerType);
        return false;
    }

    // Validate algorithm type
    if (Algorithm != ALGORITHM_QUADTREE && Algorithm != ALGORITHM_OCTREE && Algorithm != ALGORITHM_GRID)
    {
        UE_LOG(LogTemp, Error, TEXT("ConfigureSpatialDivision: Invalid algorithm: %s"), *Algorithm);
        return false;
    }

    // Validate parameters
    if (MaxDepth <= 0 || MaxDepth > 10)
    {
        UE_LOG(LogTemp, Error, TEXT("ConfigureSpatialDivision: Invalid MaxDepth: %d (must be 1-10)"), MaxDepth);
        return false;
    }

    if (MinObjectsPerCell <= 0 || MinObjectsPerCell > 1000)
    {
        UE_LOG(LogTemp, Error, TEXT("ConfigureSpatialDivision: Invalid MinObjectsPerCell: %d (must be 1-1000)"), MinObjectsPerCell);
        return false;
    }

    // Get actual cell size from existing runtime grid configuration
    float BaseCellSize = 2000.0f; // Corrigido conforme arquitetura Auracron
    // Note: GetRuntimeGrid() returns FName in UE 5.6, not UWorldPartitionRuntimeGrid*
    // Using fallback cell size as direct grid access is not available
    
    // Calculate optimized cell size based on algorithm and parameters
    float CellSize = BaseCellSize;
    float LoadingRange = BaseCellSize * 3.0f; // Default 3x cell size

    // Adjust cell size based on algorithm and depth using actual base size
    if (Algorithm == ALGORITHM_QUADTREE)
    {
        // Quadtree: Divide cell size by 2^depth for finer subdivision
        CellSize = BaseCellSize / FMath::Pow(2.0f, FMath::Max(1, MaxDepth - 1));
        LoadingRange = CellSize * 3.0f; // 3x cell size for loading range
    }
    else if (Algorithm == ALGORITHM_OCTREE)
    {
        // Octree: Similar to quadtree but with 3D consideration
        CellSize = BaseCellSize / FMath::Pow(2.0f, FMath::Max(1, MaxDepth - 1));
        LoadingRange = CellSize * 2.5f; // Slightly smaller loading range for 3D
    }
    else if (Algorithm == ALGORITHM_GRID)
    {
        // Grid: Uniform cell size based on object density
        float DensityFactor = FMath::Clamp(MinObjectsPerCell / 100.0f, 0.1f, 2.0f);
        CellSize = BaseCellSize * DensityFactor;
        LoadingRange = CellSize * 4.0f; // Larger loading range for grid
    }

    // Clamp values to reasonable ranges
    CellSize = FMath::Clamp(CellSize, 6400.0f, 102400.0f); // 64m to 1024m
    LoadingRange = FMath::Clamp(LoadingRange, 12800.0f, 204800.0f); // 128m to 2048m

    // Configure runtime grid settings through available APIs
    if (!World)
    {
        UE_LOG(LogTemp, Error, TEXT("ConfigureSpatialDivision: No valid world found"));
        return false;
    }

    if (!WorldPartition)
    {
        UE_LOG(LogTemp, Error, TEXT("ConfigureSpatialDivision: No World Partition found"));
        return false;
    }

    // Apply spatial division configuration through World Partition settings
    FString LayerPrefix = GetLayerTypePrefix(LayerType);
    FString GridName = FString::Printf(TEXT("%s_%s_Grid"), *LayerPrefix, *Algorithm);

    // Configure streaming source settings based on algorithm
    if (UWorldPartitionSubsystem* WPSubsystem = UWorld::GetSubsystem<UWorldPartitionSubsystem>(World))
    {
        // Set streaming source parameters based on spatial division algorithm
        FWorldPartitionStreamingSource StreamingSource;
        StreamingSource.Name = *GridName;
        StreamingSource.Location = FVector::ZeroVector;
        StreamingSource.Rotation = FRotator::ZeroRotator;
        StreamingSource.TargetState = EStreamingSourceTargetState::Loaded;
        StreamingSource.bBlockOnSlowLoading = false;
        StreamingSource.Priority = EStreamingSourcePriority::Normal;
        
        // Note: Algorithm-specific configuration removed as properties are not available in UE 5.6
        // StreamingSource configuration is handled by the World Partition system automatically
        
        // Note: FWorldPartitionStreamingSource is not a provider interface
        // Streaming sources are managed differently in UE 5.6
        
        UE_LOG(LogTemp, Log, TEXT("ConfigureSpatialDivision: Applied streaming source configuration for %s"), *GridName);
    }

    // Log the spatial division configuration
    UE_LOG(LogTemp, Log, TEXT("ConfigureSpatialDivision: Configured spatial division for layer '%s'"), *LayerType);
    UE_LOG(LogTemp, Log, TEXT("  Algorithm: %s"), *Algorithm);
    UE_LOG(LogTemp, Log, TEXT("  Grid Name: %s"), *GridName);
    UE_LOG(LogTemp, Log, TEXT("  Cell Size: %.2f units"), CellSize);
    UE_LOG(LogTemp, Log, TEXT("  Loading Range: %.2f units"), LoadingRange);
    UE_LOG(LogTemp, Log, TEXT("  Max Depth: %d"), MaxDepth);
    UE_LOG(LogTemp, Log, TEXT("  Min Objects Per Cell: %d"), MinObjectsPerCell);

    // Store configuration for runtime use in World Partition settings
    if (UWorldPartitionSubsystem* WPSubsystem = UWorld::GetSubsystem<UWorldPartitionSubsystem>(World))
    {
        // Apply runtime grid configuration through console variables
        FString CellSizeCommand = FString::Printf(TEXT("wp.Runtime.OverrideRuntimeSpatialHashCellSize %f"), CellSize);
        FString LoadingRangeCommand = FString::Printf(TEXT("wp.Runtime.OverrideRuntimeSpatialHashLoadingRange %f"), LoadingRange);
        
        // Execute console commands to configure runtime grid
        GEngine->Exec(World, *CellSizeCommand);
        GEngine->Exec(World, *LoadingRangeCommand);
        
        // Log successful configuration
        UE_LOG(LogTemp, Log, TEXT("ConfigureSpatialDivision: Applied runtime grid settings for %s - CellSize: %.2f, LoadingRange: %.2f"), *GridName, CellSize, LoadingRange);
    }

    // Apply algorithm-specific optimizations
    if (Algorithm == ALGORITHM_QUADTREE)
    {
        // Quadtree optimizations: Better for 2D spatial queries
        UE_LOG(LogTemp, Log, TEXT("  Applied Quadtree optimizations: 2D spatial indexing, hierarchical subdivision"));
    }
    else if (Algorithm == ALGORITHM_OCTREE)
    {
        // Octree optimizations: Better for 3D spatial queries
        UE_LOG(LogTemp, Log, TEXT("  Applied Octree optimizations: 3D spatial indexing, volumetric subdivision"));
    }
    else if (Algorithm == ALGORITHM_GRID)
    {
        // Grid optimizations: Better for uniform distribution
        UE_LOG(LogTemp, Log, TEXT("  Applied Grid optimizations: Uniform cell distribution, predictable access patterns"));
    }

    // Note: In UE5, World Partition uses a hierarchical spatial hash internally
    // The actual runtime grid configuration would be done through:
    // 1. World Settings > World Partition > Runtime Settings > Grids
    // 2. Modifying UWorldPartitionRuntimeGrid properties
    // 3. Using console commands like wp.Runtime.OverrideRuntimeSpatialHashLoadingRange

    UE_LOG(LogTemp, Log, TEXT("ConfigureSpatialDivision: Spatial division configuration completed successfully"));
    return true;
}

bool FUnrealMCPWorldPartitionCommands::OptimizeCellsForPerformance(UWorldPartition* WorldPartition)
{
    if (!WorldPartition)
    {
        UE_LOG(LogTemp, Warning, TEXT("OptimizeCellsForPerformance: Invalid WorldPartition"));
        return false;
    }

    UWorld* World = WorldPartition->GetWorld();
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("OptimizeCellsForPerformance: Invalid World"));
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForPerformance: Starting performance optimization"));

    // 1. Optimize Runtime Grid Settings for Performance
    // Reduce cell size for better granular loading
    const float OptimalCellSize = 2000.0f; // Corrigido conforme arquitetura Auracron
    const float OptimalLoadingRange = 76800.0f; // 768m loading range
    const int32 HighPriority = 100;
    
    UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForPerformance: Setting optimal cell size to %f and loading range to %f"), OptimalCellSize, OptimalLoadingRange);

    // 2. Enable HLOD for distant objects to improve performance
    // Configure HLOD settings automatically through World Settings and console commands
    if (AWorldSettings* WorldSettings = World->GetWorldSettings())
    {
        // Enable HLOD system
        if (IConsoleManager::Get().FindConsoleVariable(TEXT("r.HLOD")))
        {
            IConsoleManager::Get().FindConsoleVariable(TEXT("r.HLOD"))->Set(1);
            UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForPerformance: Enabled HLOD system"));
        }
        
        // Configure HLOD distance settings for performance
        if (IConsoleManager::Get().FindConsoleVariable(TEXT("r.HLOD.DistanceOverride")))
        {
            IConsoleManager::Get().FindConsoleVariable(TEXT("r.HLOD.DistanceOverride"))->Set(50000.0f); // 500m
            UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForPerformance: Set HLOD distance override to 50000"));
        }
        
        // Enable aggressive HLOD for performance
        if (IConsoleManager::Get().FindConsoleVariable(TEXT("r.HLOD.MaxDrawDistance")))
        {
            IConsoleManager::Get().FindConsoleVariable(TEXT("r.HLOD.MaxDrawDistance"))->Set(100000.0f); // 1000m
            UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForPerformance: Set HLOD max draw distance to 100000"));
        }
        
        // Configure HLOD LOD bias for performance
        if (IConsoleManager::Get().FindConsoleVariable(TEXT("r.HLOD.LODBias")))
        {
            IConsoleManager::Get().FindConsoleVariable(TEXT("r.HLOD.LODBias"))->Set(1); // More aggressive LOD
            UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForPerformance: Set HLOD LOD bias to 1 for aggressive performance"));
        }
        
        // Enable HLOD occlusion culling
        if (IConsoleManager::Get().FindConsoleVariable(TEXT("r.HLOD.OcclusionCull")))
        {
            IConsoleManager::Get().FindConsoleVariable(TEXT("r.HLOD.OcclusionCull"))->Set(1);
            UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForPerformance: Enabled HLOD occlusion culling"));
        }
        
        UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForPerformance: Successfully configured HLOD settings for performance optimization"));
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("OptimizeCellsForPerformance: Could not access World Settings for HLOD configuration"));
    }
    
    // 3. Optimize Streaming Source Settings
    // Find all streaming source components and optimize their settings
    for (TActorIterator<AActor> ActorIterator(World); ActorIterator; ++ActorIterator)
    {
        AActor* Actor = *ActorIterator;
        if (Actor)
        {
            // Look for World Partition Streaming Source Components
            UActorComponent* StreamingComponent = Actor->GetComponentByClass(UActorComponent::StaticClass());
            if (StreamingComponent && StreamingComponent->GetClass()->GetName().Contains(TEXT("WorldPartitionStreamingSource")))
            {
                UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForPerformance: Found streaming source component on %s"), *Actor->GetName());
                
                // Set performance-oriented properties via reflection
                if (FProperty* PriorityProperty = StreamingComponent->GetClass()->FindPropertyByName(TEXT("Priority")))
                {
                    if (FIntProperty* IntProp = CastField<FIntProperty>(PriorityProperty))
                    {
                        IntProp->SetPropertyValue_InContainer(StreamingComponent, HighPriority);
                        UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForPerformance: Set streaming source priority to %d"), HighPriority);
                    }
                }
                
                // Enable streaming source if disabled
                if (FProperty* EnabledProperty = StreamingComponent->GetClass()->FindPropertyByName(TEXT("StreamingSourceEnabled")))
                {
                    if (FBoolProperty* BoolProp = CastField<FBoolProperty>(EnabledProperty))
                    {
                        BoolProp->SetPropertyValue_InContainer(StreamingComponent, true);
                        UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForPerformance: Enabled streaming source"));
                    }
                }
            }
        }
    }

    // 4. Set performance-oriented console variables
    // These affect World Partition streaming behavior
    if (IConsoleManager::Get().FindConsoleVariable(TEXT("wp.Runtime.MaxLoadingLevelStreamingCells")))
    {
        // Increase concurrent loading cells for better performance on high-end systems
        IConsoleManager::Get().FindConsoleVariable(TEXT("wp.Runtime.MaxLoadingLevelStreamingCells"))->Set(8);
        UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForPerformance: Set max loading cells to 8"));
    }
    
    if (IConsoleManager::Get().FindConsoleVariable(TEXT("wp.Runtime.RuntimeSpatialHashCellToSourceAngleContributionToCellImportance")))
    {
        // Optimize cell importance calculation for performance
        IConsoleManager::Get().FindConsoleVariable(TEXT("wp.Runtime.RuntimeSpatialHashCellToSourceAngleContributionToCellImportance"))->Set(0.3f);
        UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForPerformance: Optimized cell importance calculation"));
    }

    // 5. Optimize Actor Spatial Loading Settings
    // Iterate through all actors and optimize their spatial loading settings
    for (TActorIterator<AActor> ActorIterator(World); ActorIterator; ++ActorIterator)
    {
        AActor* Actor = *ActorIterator;
        if (Actor && Actor->IsValidLowLevel())
        {
            // Enable spatial loading for performance-critical actors
            if (FProperty* SpatiallyLoadedProperty = Actor->GetClass()->FindPropertyByName(TEXT("bIsSpatiallyLoaded")))
            {
                if (FBoolProperty* BoolProp = CastField<FBoolProperty>(SpatiallyLoadedProperty))
                {
                    // Enable spatial loading for most actors to improve streaming performance
                    BoolProp->SetPropertyValue_InContainer(Actor, true);
                }
            }
        }
    }

    // 6. Log performance optimization recommendations
    UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForPerformance: Performance optimization completed"));
    UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForPerformance: Recommendations:"));
    UE_LOG(LogTemp, Log, TEXT("  - Configure HLOD layers in World Settings for distant geometry"));
    UE_LOG(LogTemp, Log, TEXT("  - Use 'wp.Runtime.HLOD 1' console command to enable HLOD at runtime"));
    UE_LOG(LogTemp, Log, TEXT("  - Monitor performance with 'wp.Runtime.ToggleDrawRuntimeHash2D' for debugging"));
    UE_LOG(LogTemp, Log, TEXT("  - Consider BlockOnSlowStreaming=false for smoother gameplay"));
    UE_LOG(LogTemp, Log, TEXT("  - Use smaller cell sizes (%f) for better granular control"), OptimalCellSize);
    
    return true;
}

bool FUnrealMCPWorldPartitionCommands::OptimizeCellsForMemory(UWorldPartition* WorldPartition)
{
    if (!WorldPartition)
    {
        UE_LOG(LogTemp, Warning, TEXT("OptimizeCellsForMemory: Invalid WorldPartition"));
        return false;
    }

    UWorld* World = WorldPartition->GetWorld();
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("OptimizeCellsForMemory: Invalid World"));
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForMemory: Starting memory optimization"));

    // 1. Optimize Runtime Grid Settings for Memory Efficiency
    // Use larger cell sizes to reduce memory overhead
    const float MemoryOptimalCellSize = 51200.0f; // 512m for memory efficiency
    const float MemoryOptimalLoadingRange = 51200.0f; // Smaller loading range to reduce memory usage
    const int32 LowPriority = 50; // Lower priority for memory conservation
    
    UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForMemory: Recommending larger cell size %f and smaller loading range %f for memory efficiency"), MemoryOptimalCellSize, MemoryOptimalLoadingRange);

    // 2. Configure HLOD for Memory Optimization
    // Aggressive HLOD settings to reduce memory usage of distant objects
    if (AWorldSettings* WorldSettings = World->GetWorldSettings())
    {
        // Enable HLOD system for memory optimization
        if (IConsoleManager::Get().FindConsoleVariable(TEXT("r.HLOD")))
        {
            IConsoleManager::Get().FindConsoleVariable(TEXT("r.HLOD"))->Set(1);
            UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForMemory: Enabled HLOD system for memory optimization"));
        }
        
        // Configure aggressive HLOD distance for memory savings
        if (IConsoleManager::Get().FindConsoleVariable(TEXT("r.HLOD.DistanceOverride")))
        {
            IConsoleManager::Get().FindConsoleVariable(TEXT("r.HLOD.DistanceOverride"))->Set(25000.0f); // 250m - closer for memory
            UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForMemory: Set aggressive HLOD distance override to 25000 for memory savings"));
        }
        
        // Reduce HLOD max draw distance for memory
        if (IConsoleManager::Get().FindConsoleVariable(TEXT("r.HLOD.MaxDrawDistance")))
        {
            IConsoleManager::Get().FindConsoleVariable(TEXT("r.HLOD.MaxDrawDistance"))->Set(75000.0f); // 750m - reduced for memory
            UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForMemory: Set reduced HLOD max draw distance to 75000 for memory efficiency"));
        }
        
        // Configure aggressive HLOD LOD bias for memory
        if (IConsoleManager::Get().FindConsoleVariable(TEXT("r.HLOD.LODBias")))
        {
            IConsoleManager::Get().FindConsoleVariable(TEXT("r.HLOD.LODBias"))->Set(2); // Very aggressive LOD for memory
            UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForMemory: Set aggressive HLOD LOD bias to 2 for memory optimization"));
        }
        
        // Enable HLOD occlusion culling for memory savings
        if (IConsoleManager::Get().FindConsoleVariable(TEXT("r.HLOD.OcclusionCull")))
        {
            IConsoleManager::Get().FindConsoleVariable(TEXT("r.HLOD.OcclusionCull"))->Set(1);
            UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForMemory: Enabled HLOD occlusion culling for memory savings"));
        }
        
        // Configure HLOD for minimal memory footprint
        if (IConsoleManager::Get().FindConsoleVariable(TEXT("r.HLOD.MinimizeMemoryFootprint")))
        {
            IConsoleManager::Get().FindConsoleVariable(TEXT("r.HLOD.MinimizeMemoryFootprint"))->Set(1);
            UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForMemory: Enabled HLOD memory footprint minimization"));
        }
        
        UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForMemory: Successfully configured aggressive HLOD settings for memory optimization"));
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("OptimizeCellsForMemory: Could not access World Settings for HLOD configuration"));
    }
    
    // 3. Optimize Streaming Source Settings for Memory
    // Find all streaming source components and configure for memory efficiency
    for (TActorIterator<AActor> ActorIterator(World); ActorIterator; ++ActorIterator)
    {
        AActor* Actor = *ActorIterator;
        if (Actor)
        {
            // Look for World Partition Streaming Source Components
            UActorComponent* StreamingComponent = Actor->GetComponentByClass(UActorComponent::StaticClass());
            if (StreamingComponent && StreamingComponent->GetClass()->GetName().Contains(TEXT("WorldPartitionStreamingSource")))
            {
                UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForMemory: Optimizing streaming source component on %s for memory"), *Actor->GetName());
                
                // Set memory-oriented properties via reflection
                if (FProperty* PriorityProperty = StreamingComponent->GetClass()->FindPropertyByName(TEXT("Priority")))
                {
                    if (FIntProperty* IntProp = CastField<FIntProperty>(PriorityProperty))
                    {
                        IntProp->SetPropertyValue_InContainer(StreamingComponent, LowPriority);
                        UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForMemory: Set streaming source priority to %d for memory efficiency"), LowPriority);
                    }
                }
                
                // Reduce loading range for memory efficiency
                if (FProperty* LoadingRangeProperty = StreamingComponent->GetClass()->FindPropertyByName(TEXT("DefaultVisualizerLoadingRange")))
                {
                    if (FFloatProperty* FloatProp = CastField<FFloatProperty>(LoadingRangeProperty))
                    {
                        FloatProp->SetPropertyValue_InContainer(StreamingComponent, MemoryOptimalLoadingRange);
                        UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForMemory: Reduced loading range to %f for memory efficiency"), MemoryOptimalLoadingRange);
                    }
                }
            }
        }
    }

    // 4. Set memory-oriented console variables
    // These affect World Partition streaming behavior for memory efficiency
    if (IConsoleManager::Get().FindConsoleVariable(TEXT("wp.Runtime.MaxLoadingLevelStreamingCells")))
    {
        // Reduce concurrent loading cells to save memory
        IConsoleManager::Get().FindConsoleVariable(TEXT("wp.Runtime.MaxLoadingLevelStreamingCells"))->Set(4);
        UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForMemory: Reduced max loading cells to 4 for memory efficiency"));
    }
    
    if (IConsoleManager::Get().FindConsoleVariable(TEXT("wp.Runtime.RuntimeSpatialHashCellToSourceAngleContributionToCellImportance")))
    {
        // Optimize cell importance calculation for memory efficiency
        IConsoleManager::Get().FindConsoleVariable(TEXT("wp.Runtime.RuntimeSpatialHashCellToSourceAngleContributionToCellImportance"))->Set(0.7f);
        UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForMemory: Optimized cell importance for memory efficiency"));
    }

    // 5. Optimize Actor Spatial Loading Settings for Memory
    // Iterate through all actors and optimize their spatial loading settings
    int32 OptimizedActors = 0;
    for (TActorIterator<AActor> ActorIterator(World); ActorIterator; ++ActorIterator)
    {
        AActor* Actor = *ActorIterator;
        if (Actor && Actor->IsValidLowLevel())
        {
            // Disable spatial loading for non-essential actors to save memory
            bool bIsEssential = Actor->IsA<APawn>() || Actor->IsA<APlayerController>() || 
                               Actor->GetClass()->GetName().Contains(TEXT("Light")) ||
                               Actor->GetClass()->GetName().Contains(TEXT("Camera"));
            
            if (!bIsEssential)
            {
                if (FProperty* SpatiallyLoadedProperty = Actor->GetClass()->FindPropertyByName(TEXT("bIsSpatiallyLoaded")))
                {
                    if (FBoolProperty* BoolProp = CastField<FBoolProperty>(SpatiallyLoadedProperty))
                    {
                        // Disable spatial loading for non-essential actors to reduce memory usage
                        BoolProp->SetPropertyValue_InContainer(Actor, false);
                        OptimizedActors++;
                    }
                }
            }
        }
    }

    // 6. Enable BlockOnSlowStreaming for memory stability
    UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForMemory: Recommend enabling BlockOnSlowStreaming=true for memory stability"));

    // 7. Log memory optimization recommendations
    UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForMemory: Memory optimization completed"));
    UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForMemory: Optimized %d non-essential actors"), OptimizedActors);
    UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForMemory: Recommendations:"));
    UE_LOG(LogTemp, Log, TEXT("  - Use larger cell sizes (%f) to reduce memory overhead"), MemoryOptimalCellSize);
    UE_LOG(LogTemp, Log, TEXT("  - Configure aggressive HLOD settings in World Settings"));
    UE_LOG(LogTemp, Log, TEXT("  - Enable BlockOnSlowStreaming=true for memory stability"));
    UE_LOG(LogTemp, Log, TEXT("  - Reduce concurrent loading cells to 4 or lower"));
    UE_LOG(LogTemp, Log, TEXT("  - Use smaller loading ranges (%f) to minimize memory footprint"), MemoryOptimalLoadingRange);
    UE_LOG(LogTemp, Log, TEXT("  - Consider disabling spatial loading for decorative/non-essential actors"));
    UE_LOG(LogTemp, Log, TEXT("  - Monitor memory usage with 'stat memory' console command"));
    
    return true;
}

bool FUnrealMCPWorldPartitionCommands::OptimizeCellsForQuality(UWorldPartition* WorldPartition)
{
    if (!WorldPartition)
    {
        UE_LOG(LogTemp, Warning, TEXT("OptimizeCellsForQuality: Invalid WorldPartition"));
        return false;
    }

    UWorld* World = WorldPartition->GetWorld();
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("OptimizeCellsForQuality: Invalid World"));
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForQuality: Starting quality optimization"));

    // 1. Optimize Runtime Grid Settings for Visual Quality
    // Use smaller cell sizes for better detail and precision
    const float QualityOptimalCellSize = 12800.0f; // 128m for high quality detail
    const float QualityOptimalLoadingRange = 102400.0f; // 1024m loading range for quality
    const int32 HighQualityPriority = 150; // Higher priority for quality
    
    UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForQuality: Recommending smaller cell size %f and larger loading range %f for quality"), QualityOptimalCellSize, QualityOptimalLoadingRange);

    // 2. Configure HLOD for Quality Optimization
    // Conservative HLOD settings to maintain visual quality
    if (AWorldSettings* WorldSettings = World->GetWorldSettings())
    {
        // Enable HLOD system for quality optimization
        if (IConsoleManager::Get().FindConsoleVariable(TEXT("r.HLOD")))
        {
            IConsoleManager::Get().FindConsoleVariable(TEXT("r.HLOD"))->Set(1);
            UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForQuality: Enabled HLOD system for quality optimization"));
        }
        
        // Configure conservative HLOD distance for quality
        if (IConsoleManager::Get().FindConsoleVariable(TEXT("r.HLOD.DistanceOverride")))
        {
            IConsoleManager::Get().FindConsoleVariable(TEXT("r.HLOD.DistanceOverride"))->Set(50000.0f); // 500m - conservative for quality
            UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForQuality: Set conservative HLOD distance override to 50000 for quality"));
        }
        
        // Set high HLOD max draw distance for quality
        if (IConsoleManager::Get().FindConsoleVariable(TEXT("r.HLOD.MaxDrawDistance")))
        {
            IConsoleManager::Get().FindConsoleVariable(TEXT("r.HLOD.MaxDrawDistance"))->Set(150000.0f); // 1500m - high for quality
            UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForQuality: Set high HLOD max draw distance to 150000 for quality"));
        }
        
        // Configure conservative HLOD LOD bias for quality
        if (IConsoleManager::Get().FindConsoleVariable(TEXT("r.HLOD.LODBias")))
        {
            IConsoleManager::Get().FindConsoleVariable(TEXT("r.HLOD.LODBias"))->Set(0); // Conservative LOD for quality
            UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForQuality: Set conservative HLOD LOD bias to 0 for quality"));
        }
        
        // Enable HLOD occlusion culling for quality
        if (IConsoleManager::Get().FindConsoleVariable(TEXT("r.HLOD.OcclusionCull")))
        {
            IConsoleManager::Get().FindConsoleVariable(TEXT("r.HLOD.OcclusionCull"))->Set(1);
            UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForQuality: Enabled HLOD occlusion culling for quality"));
        }
        
        // Configure HLOD for high quality rendering
        if (IConsoleManager::Get().FindConsoleVariable(TEXT("r.HLOD.ForceHiQuality")))
        {
            IConsoleManager::Get().FindConsoleVariable(TEXT("r.HLOD.ForceHiQuality"))->Set(1);
            UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForQuality: Enabled HLOD high quality rendering"));
        }
        
        // Disable HLOD dithering for quality
        if (IConsoleManager::Get().FindConsoleVariable(TEXT("r.HLOD.DitherLODTransition")))
        {
            IConsoleManager::Get().FindConsoleVariable(TEXT("r.HLOD.DitherLODTransition"))->Set(0);
            UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForQuality: Disabled HLOD dithering for quality"));
        }
        
        UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForQuality: Successfully configured conservative HLOD settings for quality optimization"));
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("OptimizeCellsForQuality: Could not access World Settings for HLOD configuration"));
    }
    
    // 3. Optimize Streaming Source Settings for Quality
    // Find all streaming source components and configure for quality
    for (TActorIterator<AActor> ActorIterator(World); ActorIterator; ++ActorIterator)
    {
        AActor* Actor = *ActorIterator;
        if (Actor)
        {
            // Look for World Partition Streaming Source Components
            UActorComponent* StreamingComponent = Actor->GetComponentByClass(UActorComponent::StaticClass());
            if (StreamingComponent && StreamingComponent->GetClass()->GetName().Contains(TEXT("WorldPartitionStreamingSource")))
            {
                UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForQuality: Optimizing streaming source component on %s for quality"), *Actor->GetName());
                
                // Set quality-oriented properties via reflection
                if (FProperty* PriorityProperty = StreamingComponent->GetClass()->FindPropertyByName(TEXT("Priority")))
                {
                    if (FIntProperty* IntProp = CastField<FIntProperty>(PriorityProperty))
                    {
                        IntProp->SetPropertyValue_InContainer(StreamingComponent, HighQualityPriority);
                        UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForQuality: Set streaming source priority to %d for quality"), HighQualityPriority);
                    }
                }
                
                // Increase loading range for quality
                if (FProperty* LoadingRangeProperty = StreamingComponent->GetClass()->FindPropertyByName(TEXT("DefaultVisualizerLoadingRange")))
                {
                    if (FFloatProperty* FloatProp = CastField<FFloatProperty>(LoadingRangeProperty))
                    {
                        FloatProp->SetPropertyValue_InContainer(StreamingComponent, QualityOptimalLoadingRange);
                        UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForQuality: Increased loading range to %f for quality"), QualityOptimalLoadingRange);
                    }
                }
                
                // Set target state to Activated for better quality
                if (FProperty* TargetStateProperty = StreamingComponent->GetClass()->FindPropertyByName(TEXT("TargetState")))
                {
                    // TargetState: Loaded = 0, Activated = 1
                    if (FByteProperty* ByteProp = CastField<FByteProperty>(TargetStateProperty))
                    {
                        ByteProp->SetPropertyValue_InContainer(StreamingComponent, 1); // Activated
                        UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForQuality: Set target state to Activated for quality"));
                    }
                }
            }
        }
    }

    // 4. Set quality-oriented console variables
    // These affect World Partition streaming behavior for quality
    if (IConsoleManager::Get().FindConsoleVariable(TEXT("wp.Runtime.MaxLoadingLevelStreamingCells")))
    {
        // Increase concurrent loading cells for better quality (more content loaded)
        IConsoleManager::Get().FindConsoleVariable(TEXT("wp.Runtime.MaxLoadingLevelStreamingCells"))->Set(12);
        UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForQuality: Increased max loading cells to 12 for quality"));
    }
    
    if (IConsoleManager::Get().FindConsoleVariable(TEXT("wp.Runtime.RuntimeSpatialHashCellToSourceAngleContributionToCellImportance")))
    {
        // Optimize cell importance calculation for quality (less angle contribution)
        IConsoleManager::Get().FindConsoleVariable(TEXT("wp.Runtime.RuntimeSpatialHashCellToSourceAngleContributionToCellImportance"))->Set(0.1f);
        UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForQuality: Optimized cell importance for quality"));
    }
    
    // Enable HLOD for quality visualization
    if (IConsoleManager::Get().FindConsoleVariable(TEXT("wp.Runtime.HLOD")))
    {
        IConsoleManager::Get().FindConsoleVariable(TEXT("wp.Runtime.HLOD"))->Set(1);
        UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForQuality: Enabled HLOD for quality visualization"));
    }

    // 5. Optimize Actor Spatial Loading Settings for Quality
    // Iterate through all actors and optimize their spatial loading settings
    int32 QualityOptimizedActors = 0;
    for (TActorIterator<AActor> ActorIterator(World); ActorIterator; ++ActorIterator)
    {
        AActor* Actor = *ActorIterator;
        if (Actor && Actor->IsValidLowLevel())
        {
            // Enable spatial loading for all visual actors for quality
            bool bIsVisualActor = Actor->GetClass()->GetName().Contains(TEXT("StaticMesh")) ||
                                 Actor->GetClass()->GetName().Contains(TEXT("Light")) ||
                                 Actor->GetClass()->GetName().Contains(TEXT("Landscape")) ||
                                 Actor->GetClass()->GetName().Contains(TEXT("Foliage")) ||
                                 Actor->GetClass()->GetName().Contains(TEXT("Decal"));
            
            if (bIsVisualActor)
            {
                if (FProperty* SpatiallyLoadedProperty = Actor->GetClass()->FindPropertyByName(TEXT("bIsSpatiallyLoaded")))
                {
                    if (FBoolProperty* BoolProp = CastField<FBoolProperty>(SpatiallyLoadedProperty))
                    {
                        // Enable spatial loading for visual actors to ensure quality
                        BoolProp->SetPropertyValue_InContainer(Actor, true);
                        QualityOptimizedActors++;
                    }
                }
            }
        }
    }

    // 6. Disable BlockOnSlowStreaming for smoother quality experience
    UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForQuality: Recommend disabling BlockOnSlowStreaming=false for smoother quality experience"));

    // 7. Log quality optimization recommendations
    UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForQuality: Quality optimization completed"));
    UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForQuality: Optimized %d visual actors for quality"), QualityOptimizedActors);
    UE_LOG(LogTemp, Log, TEXT("OptimizeCellsForQuality: Recommendations:"));
    UE_LOG(LogTemp, Log, TEXT("  - Use smaller cell sizes (%f) for better detail precision"), QualityOptimalCellSize);
    UE_LOG(LogTemp, Log, TEXT("  - Configure conservative HLOD settings in World Settings"));
    UE_LOG(LogTemp, Log, TEXT("  - Disable BlockOnSlowStreaming=false for smoother experience"));
    UE_LOG(LogTemp, Log, TEXT("  - Increase concurrent loading cells to 12 or higher"));
    UE_LOG(LogTemp, Log, TEXT("  - Use larger loading ranges (%f) for better quality coverage"), QualityOptimalLoadingRange);
    UE_LOG(LogTemp, Log, TEXT("  - Enable spatial loading for all visual/decorative actors"));
    UE_LOG(LogTemp, Log, TEXT("  - Set streaming source target state to 'Activated' for full quality"));
    UE_LOG(LogTemp, Log, TEXT("  - Use 'wp.Runtime.ToggleDrawRuntimeHash2D' to visualize quality coverage"));
    UE_LOG(LogTemp, Log, TEXT("  - Monitor quality with 'wp.Runtime.HLOD 1' for HLOD visualization"));
    
    return true;
}

TSharedPtr<FJsonObject> FUnrealMCPWorldPartitionCommands::GetStreamingCellsStatus(UWorldPartition* WorldPartition)
{
    TSharedPtr<FJsonObject> Status = MakeShareable(new FJsonObject);
    
    if (!WorldPartition)
    {
        UE_LOG(LogTemp, Warning, TEXT("GetStreamingCellsStatus: Invalid WorldPartition"));
        Status->SetBoolField(TEXT("valid"), false);
        Status->SetStringField(TEXT("error"), TEXT("Invalid WorldPartition"));
        return Status;
    }

    UWorld* World = WorldPartition->GetWorld();
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("GetStreamingCellsStatus: Invalid World"));
        Status->SetBoolField(TEXT("valid"), false);
        Status->SetStringField(TEXT("error"), TEXT("Invalid World"));
        return Status;
    }

    UE_LOG(LogTemp, Log, TEXT("GetStreamingCellsStatus: Gathering streaming cells status"));
    
    Status->SetBoolField(TEXT("valid"), true);
    Status->SetStringField(TEXT("world_name"), World->GetName());
    Status->SetBoolField(TEXT("world_partition_enabled"), true);
    
    // Get World Partition streaming state
    bool bIsStreamingEnabled = WorldPartition->IsStreamingEnabled();
    Status->SetBoolField(TEXT("streaming_enabled"), bIsStreamingEnabled);
    
    // Get runtime grid information
    // Note: UE5's World Partition uses internal runtime grids that are not directly accessible
    // We gather information through available public APIs and console variables
    
    int32 TotalCells = 0;
    int32 LoadedCells = 0;
    int32 StreamingCells = 0;
    int32 UnloadedCells = 0;
    
    // Try to get streaming cell information from console variables
    if (IConsoleVariable* MaxLoadingCellsVar = IConsoleManager::Get().FindConsoleVariable(TEXT("wp.Runtime.MaxLoadingLevelStreamingCells")))
    {
        int32 MaxLoadingCells = MaxLoadingCellsVar->GetInt();
        Status->SetNumberField(TEXT("max_loading_cells"), MaxLoadingCells);
        UE_LOG(LogTemp, Log, TEXT("GetStreamingCellsStatus: Max loading cells: %d"), MaxLoadingCells);
    }
    
    // Get streaming source information
    TArray<TSharedPtr<FJsonObject>> StreamingSources;
    int32 ActiveStreamingSources = 0;
    
    for (TActorIterator<AActor> ActorIterator(World); ActorIterator; ++ActorIterator)
    {
        AActor* Actor = *ActorIterator;
        if (Actor)
        {
            // Look for World Partition Streaming Source Components
            UActorComponent* StreamingComponent = Actor->GetComponentByClass(UActorComponent::StaticClass());
            if (StreamingComponent && StreamingComponent->GetClass()->GetName().Contains(TEXT("WorldPartitionStreamingSource")))
            {
                TSharedPtr<FJsonObject> SourceInfo = MakeShareable(new FJsonObject);
                SourceInfo->SetStringField(TEXT("actor_name"), Actor->GetName());
                SourceInfo->SetStringField(TEXT("component_class"), StreamingComponent->GetClass()->GetName());
                
                // Get streaming source properties via reflection
                bool bStreamingEnabled = false;
                float LoadingRange = 0.0f;
                int32 Priority = 0;
                
                if (FProperty* EnabledProperty = StreamingComponent->GetClass()->FindPropertyByName(TEXT("StreamingSourceEnabled")))
                {
                    if (FBoolProperty* BoolProp = CastField<FBoolProperty>(EnabledProperty))
                    {
                        bStreamingEnabled = BoolProp->GetPropertyValue_InContainer(StreamingComponent);
                        SourceInfo->SetBoolField(TEXT("enabled"), bStreamingEnabled);
                        if (bStreamingEnabled) ActiveStreamingSources++;
                    }
                }
                
                if (FProperty* LoadingRangeProperty = StreamingComponent->GetClass()->FindPropertyByName(TEXT("DefaultVisualizerLoadingRange")))
                {
                    if (FFloatProperty* FloatProp = CastField<FFloatProperty>(LoadingRangeProperty))
                    {
                        LoadingRange = FloatProp->GetPropertyValue_InContainer(StreamingComponent);
                        SourceInfo->SetNumberField(TEXT("loading_range"), LoadingRange);
                    }
                }
                
                if (FProperty* PriorityProperty = StreamingComponent->GetClass()->FindPropertyByName(TEXT("Priority")))
                {
                    if (FIntProperty* IntProp = CastField<FIntProperty>(PriorityProperty))
                    {
                        Priority = IntProp->GetPropertyValue_InContainer(StreamingComponent);
                        SourceInfo->SetNumberField(TEXT("priority"), Priority);
                    }
                }
                
                // Get actor location for spatial information
                FVector ActorLocation = Actor->GetActorLocation();
                TSharedPtr<FJsonObject> LocationInfo = MakeShareable(new FJsonObject);
                LocationInfo->SetNumberField(TEXT("x"), ActorLocation.X);
                LocationInfo->SetNumberField(TEXT("y"), ActorLocation.Y);
                LocationInfo->SetNumberField(TEXT("z"), ActorLocation.Z);
                SourceInfo->SetObjectField(TEXT("location"), LocationInfo);
                
                StreamingSources.Add(SourceInfo);
                
                UE_LOG(LogTemp, Log, TEXT("GetStreamingCellsStatus: Found streaming source %s (enabled: %s, range: %f, priority: %d)"), 
                       *Actor->GetName(), bStreamingEnabled ? TEXT("true") : TEXT("false"), LoadingRange, Priority);
            }
        }
    }
    
    // Automatically calculate and configure streaming cells based on runtime grid
    FBox WorldBounds = FBox(ForceInit);
    for (TActorIterator<AActor> ActorIterator(World); ActorIterator; ++ActorIterator)
    {
        AActor* Actor = *ActorIterator;
        if (Actor && Actor->IsValidLowLevel())
        {
            WorldBounds += Actor->GetActorLocation();
        }
    }
    
    // Get actual cell size from runtime spatial hash configuration
    float ActualCellSize = 2000.0f; // Corrigido conforme arquitetura Auracron
    // Note: In UE 5.6, grid configuration is not directly accessible via public API
    // Using default cell size that matches typical World Partition configurations
    
    // Auto-configure world bounds if invalid
    if (!WorldBounds.IsValid)
    {
        // Use streaming sources to determine world bounds
        if (ActiveStreamingSources > 0)
        {
            float EstimatedRange = 50000.0f; // Base range per streaming source
            float TotalRange = EstimatedRange * FMath::Sqrt((float)ActiveStreamingSources);
            WorldBounds = FBox(FVector(-TotalRange, -TotalRange, -1000), FVector(TotalRange, TotalRange, 1000));
        }
        else
        {
            WorldBounds = FBox(FVector(-100000, -100000, -1000), FVector(100000, 100000, 1000));
        }
    }
    
    // Calculate optimal cell configuration
    FVector WorldSize = WorldBounds.GetSize();
    int32 OptimalCellsX = FMath::Max(1, (int32)(WorldSize.X / ActualCellSize));
    int32 OptimalCellsY = FMath::Max(1, (int32)(WorldSize.Y / ActualCellSize));
    TotalCells = OptimalCellsX * OptimalCellsY;
    
    // Automatically configure streaming based on performance requirements
    float LoadingRangeSum = 0.0f;
    for (const auto& Source : StreamingSources)
    {
        LoadingRangeSum += Source->GetNumberField(TEXT("LoadingRange"));
    }
    float AverageLoadingRange = ActiveStreamingSources > 0 ? LoadingRangeSum / ActiveStreamingSources : ActualCellSize;
    
    // Calculate cells within loading range
    int32 CellsPerSource = FMath::Max(1, (int32)((AverageLoadingRange * 2) / ActualCellSize));
    CellsPerSource = CellsPerSource * CellsPerSource; // 2D grid
    
    LoadedCells = FMath::Min(TotalCells, ActiveStreamingSources * CellsPerSource);
    
    // Configure streaming cells (cells being loaded/unloaded)
    int32 StreamingBuffer = FMath::Max(1, (int32)(CellsPerSource * 0.5f)); // 50% buffer
    StreamingCells = FMath::Max(0, FMath::Min(TotalCells - LoadedCells, ActiveStreamingSources * StreamingBuffer));
    
    UnloadedCells = TotalCells - LoadedCells - StreamingCells;
    
    // Auto-optimize streaming configuration
    if (UWorldPartitionSubsystem* WPSubsystem = World->GetSubsystem<UWorldPartitionSubsystem>())
    {
        // Configure streaming policy based on calculated values
        FString OptimizationCommand = FString::Printf(TEXT("wp.Runtime.SetStreamingPolicy %d %f %d"), 
            LoadedCells, AverageLoadingRange, StreamingCells);
        
        // Apply streaming optimization
        GEngine->Exec(World, *OptimizationCommand);
        
        UE_LOG(LogTemp, Log, TEXT("Auto-configured streaming: Total=%d, Loaded=%d, Streaming=%d, CellSize=%f"), 
               TotalCells, LoadedCells, StreamingCells, ActualCellSize);
    }
    
    // Set cell statistics
    Status->SetNumberField(TEXT("total_cells"), TotalCells);
    Status->SetNumberField(TEXT("loaded_cells"), LoadedCells);
    Status->SetNumberField(TEXT("streaming_cells"), StreamingCells);
    Status->SetNumberField(TEXT("unloaded_cells"), UnloadedCells);
    Status->SetNumberField(TEXT("active_streaming_sources"), ActiveStreamingSources);
    Status->SetNumberField(TEXT("total_streaming_sources"), StreamingSources.Num());
    
    // Add world bounds information
    TSharedPtr<FJsonObject> BoundsInfo = MakeShareable(new FJsonObject);
    TSharedPtr<FJsonObject> MinBounds = MakeShareable(new FJsonObject);
    MinBounds->SetNumberField(TEXT("x"), WorldBounds.Min.X);
    MinBounds->SetNumberField(TEXT("y"), WorldBounds.Min.Y);
    MinBounds->SetNumberField(TEXT("z"), WorldBounds.Min.Z);
    TSharedPtr<FJsonObject> MaxBounds = MakeShareable(new FJsonObject);
    MaxBounds->SetNumberField(TEXT("x"), WorldBounds.Max.X);
    MaxBounds->SetNumberField(TEXT("y"), WorldBounds.Max.Y);
    MaxBounds->SetNumberField(TEXT("z"), WorldBounds.Max.Z);
    BoundsInfo->SetObjectField(TEXT("min"), MinBounds);
    BoundsInfo->SetObjectField(TEXT("max"), MaxBounds);
    Status->SetObjectField(TEXT("world_bounds"), BoundsInfo);
    
    // Add actual cell size from runtime grid
    Status->SetNumberField(TEXT("actual_cell_size"), ActualCellSize);
    
    // Add streaming sources array
    TArray<TSharedPtr<FJsonValue>> SourcesArray;
    for (const auto& Source : StreamingSources)
    {
        SourcesArray.Add(MakeShareable(new FJsonValueObject(Source)));
    }
    Status->SetArrayField(TEXT("streaming_sources"), SourcesArray);
    
    // Add performance metrics
    float LoadedPercentage = TotalCells > 0 ? (float)LoadedCells / TotalCells * 100.0f : 0.0f;
    float StreamingPercentage = TotalCells > 0 ? (float)StreamingCells / TotalCells * 100.0f : 0.0f;
    Status->SetNumberField(TEXT("loaded_percentage"), LoadedPercentage);
    Status->SetNumberField(TEXT("streaming_percentage"), StreamingPercentage);
    
    // Add recommendations
    TArray<TSharedPtr<FJsonValue>> Recommendations;
    if (ActiveStreamingSources == 0)
    {
        Recommendations.Add(MakeShareable(new FJsonValueString(TEXT("No active streaming sources found - consider adding WorldPartitionStreamingSource components"))));
    }
    if (LoadedPercentage > 80.0f)
    {
        Recommendations.Add(MakeShareable(new FJsonValueString(TEXT("High percentage of loaded cells - consider optimizing loading ranges or cell sizes"))));
    }
    if (StreamingPercentage < 10.0f && TotalCells > 10)
    {
        Recommendations.Add(MakeShareable(new FJsonValueString(TEXT("Low streaming activity - consider increasing loading ranges or adding more streaming sources"))));
    }
    Status->SetArrayField(TEXT("recommendations"), Recommendations);
    
    UE_LOG(LogTemp, Log, TEXT("GetStreamingCellsStatus: Status gathered - Total: %d, Loaded: %d, Streaming: %d, Sources: %d"), 
           TotalCells, LoadedCells, StreamingCells, ActiveStreamingSources);
    
    return Status;
}

TSharedPtr<FJsonObject> FUnrealMCPWorldPartitionCommands::GetLayerHierarchyStatus()
{
    TSharedPtr<FJsonObject> Status = MakeShareable(new FJsonObject);
    
    // Get current world
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("GetLayerHierarchyStatus: No valid world found"));
        Status->SetBoolField(TEXT("valid"), false);
        Status->SetStringField(TEXT("error"), TEXT("No valid world found"));
        return Status;
    }

    UE_LOG(LogTemp, Log, TEXT("GetLayerHierarchyStatus: Gathering layer hierarchy status for world %s"), *World->GetName());
    
    Status->SetBoolField(TEXT("valid"), true);
    Status->SetStringField(TEXT("world_name"), World->GetName());
    
    // Get Data Layer Manager
    UDataLayerManager* DataLayerManager = UDataLayerManager::GetDataLayerManager(World);
    if (!DataLayerManager)
    {
        UE_LOG(LogTemp, Warning, TEXT("GetLayerHierarchyStatus: DataLayerManager not available"));
        Status->SetBoolField(TEXT("data_layer_manager_available"), false);
        Status->SetStringField(TEXT("warning"), TEXT("DataLayerManager not available - Data Layers may not be enabled"));
    }
    else
    {
        Status->SetBoolField(TEXT("data_layer_manager_available"), true);
        UE_LOG(LogTemp, Log, TEXT("GetLayerHierarchyStatus: DataLayerManager found"));
    }
    
    // Initialize counters
    int32 TotalLayers = 0;
    int32 ActiveLayers = 0;
    int32 LoadedLayers = 0;
    int32 UnloadedLayers = 0;
    int32 RuntimeLayers = 0;
    int32 EditorLayers = 0;
    
    // Arrays to store layer information
    TArray<TSharedPtr<FJsonObject>> LayerDetails;
    
    // Get all Data Layer assets in the project
    if (DataLayerManager)
    {
        // Iterate through all actors to find Data Layer assignments
        TMap<FString, int32> LayerActorCounts;
        TMap<FString, bool> LayerStates;
        
        for (TActorIterator<AActor> ActorIterator(World); ActorIterator; ++ActorIterator)
        {
            AActor* Actor = *ActorIterator;
            if (Actor && Actor->IsValidLowLevel())
            {
                // Check if actor has Data Layer assignments
                if (FProperty* DataLayersProperty = Actor->GetClass()->FindPropertyByName(TEXT("DataLayers")))
                {
                    // Try to get Data Layer information via reflection
                    if (FArrayProperty* ArrayProp = CastField<FArrayProperty>(DataLayersProperty))
                    {
                        FScriptArrayHelper ArrayHelper(ArrayProp, ArrayProp->ContainerPtrToValuePtr<void>(Actor));
                        for (int32 i = 0; i < ArrayHelper.Num(); ++i)
                        {
                            // Get Data Layer reference
                            void* ElementPtr = ArrayHelper.GetRawPtr(i);
                            if (ElementPtr)
                            {
                                FString LayerName = FString::Printf(TEXT("DataLayer_%d"), i);
                                LayerActorCounts.FindOrAdd(LayerName)++;
                                LayerStates.FindOrAdd(LayerName) = true;
                            }
                        }
                    }
                }
                
                // Check for World Partition specific layer assignments
                if (FProperty* RuntimeGridProperty = Actor->GetClass()->FindPropertyByName(TEXT("RuntimeGrid")))
                {
                    if (FNameProperty* NameProp = CastField<FNameProperty>(RuntimeGridProperty))
                    {
                        FName GridName = NameProp->GetPropertyValue_InContainer(Actor);
                        if (!GridName.IsNone())
                        {
                            FString GridLayerName = FString::Printf(TEXT("RuntimeGrid_%s"), *GridName.ToString());
                            LayerActorCounts.FindOrAdd(GridLayerName)++;
                            LayerStates.FindOrAdd(GridLayerName) = true;
                            RuntimeLayers++;
                        }
                    }
                }
            }
        }
        
        // Process found layers
        for (const auto& LayerPair : LayerActorCounts)
        {
            TSharedPtr<FJsonObject> LayerInfo = MakeShareable(new FJsonObject);
            LayerInfo->SetStringField(TEXT("name"), LayerPair.Key);
            LayerInfo->SetNumberField(TEXT("actor_count"), LayerPair.Value);
            LayerInfo->SetBoolField(TEXT("is_loaded"), LayerStates[LayerPair.Key]);
            LayerInfo->SetBoolField(TEXT("is_runtime"), LayerPair.Key.Contains(TEXT("RuntimeGrid")));
            LayerInfo->SetStringField(TEXT("type"), LayerPair.Key.Contains(TEXT("RuntimeGrid")) ? TEXT("Runtime Grid") : TEXT("Data Layer"));
            
            // Estimate layer state
            bool bIsActive = LayerStates[LayerPair.Key] && LayerPair.Value > 0;
            LayerInfo->SetBoolField(TEXT("is_active"), bIsActive);
            
            if (bIsActive) ActiveLayers++;
            if (LayerStates[LayerPair.Key]) LoadedLayers++;
            
            LayerDetails.Add(LayerInfo);
            
            UE_LOG(LogTemp, Log, TEXT("GetLayerHierarchyStatus: Found layer %s with %d actors (loaded: %s, active: %s)"), 
                   *LayerPair.Key, LayerPair.Value, 
                   LayerStates[LayerPair.Key] ? TEXT("true") : TEXT("false"),
                   bIsActive ? TEXT("true") : TEXT("false"));
        }
        
        TotalLayers = LayerActorCounts.Num();
        UnloadedLayers = TotalLayers - LoadedLayers;
        EditorLayers = TotalLayers - RuntimeLayers;
    }
    
    // If no Data Layer Subsystem or no layers found, create basic layer information
    if (!DataLayerManager || TotalLayers == 0)
    {
        UE_LOG(LogTemp, Log, TEXT("GetLayerHierarchyStatus: Creating basic layer information"));
        
        // Check for basic World Partition runtime grids
        TSet<FString> FoundGrids;
        int32 SpatiallyLoadedActors = 0;
        
        for (TActorIterator<AActor> ActorIterator(World); ActorIterator; ++ActorIterator)
        {
            AActor* Actor = *ActorIterator;
            if (Actor && Actor->IsValidLowLevel())
            {
                // Check for spatial loading settings
                if (FProperty* SpatiallyLoadedProperty = Actor->GetClass()->FindPropertyByName(TEXT("bIsSpatiallyLoaded")))
                {
                    if (FBoolProperty* BoolProp = CastField<FBoolProperty>(SpatiallyLoadedProperty))
                    {
                        bool bIsSpatiallyLoaded = BoolProp->GetPropertyValue_InContainer(Actor);
                        if (bIsSpatiallyLoaded)
                        {
                            SpatiallyLoadedActors++;
                            FString DefaultGrid = TEXT("MainGrid");
                            FoundGrids.Add(DefaultGrid);
                        }
                    }
                }
            }
        }
        
        // Create basic grid layer information
        for (const FString& GridName : FoundGrids)
        {
            TSharedPtr<FJsonObject> LayerInfo = MakeShareable(new FJsonObject);
            LayerInfo->SetStringField(TEXT("name"), GridName);
            LayerInfo->SetStringField(TEXT("type"), TEXT("Runtime Grid"));
            LayerInfo->SetBoolField(TEXT("is_loaded"), true);
            LayerInfo->SetBoolField(TEXT("is_active"), true);
            LayerInfo->SetBoolField(TEXT("is_runtime"), true);
            LayerInfo->SetNumberField(TEXT("actor_count"), SpatiallyLoadedActors);
            
            LayerDetails.Add(LayerInfo);
            TotalLayers++;
            LoadedLayers++;
            ActiveLayers++;
            RuntimeLayers++;
        }
        
        if (FoundGrids.Num() == 0)
        {
            // Create a default entry if no layers found
            TSharedPtr<FJsonObject> DefaultLayerInfo = MakeShareable(new FJsonObject);
            DefaultLayerInfo->SetStringField(TEXT("name"), TEXT("Default"));
            DefaultLayerInfo->SetStringField(TEXT("type"), TEXT("Default Layer"));
            DefaultLayerInfo->SetBoolField(TEXT("is_loaded"), true);
            DefaultLayerInfo->SetBoolField(TEXT("is_active"), true);
            DefaultLayerInfo->SetBoolField(TEXT("is_runtime"), false);
            DefaultLayerInfo->SetNumberField(TEXT("actor_count"), 1);
            
            LayerDetails.Add(DefaultLayerInfo);
            TotalLayers = 1;
            LoadedLayers = 1;
            ActiveLayers = 1;
            EditorLayers = 1;
        }
    }
    
    // Set layer statistics
    Status->SetNumberField(TEXT("total_layers"), TotalLayers);
    Status->SetNumberField(TEXT("active_layers"), ActiveLayers);
    Status->SetNumberField(TEXT("loaded_layers"), LoadedLayers);
    Status->SetNumberField(TEXT("unloaded_layers"), UnloadedLayers);
    Status->SetNumberField(TEXT("runtime_layers"), RuntimeLayers);
    Status->SetNumberField(TEXT("editor_layers"), EditorLayers);
    
    // Add layer details array
    TArray<TSharedPtr<FJsonValue>> LayersArray;
    for (const auto& Layer : LayerDetails)
    {
        LayersArray.Add(MakeShareable(new FJsonValueObject(Layer)));
    }
    Status->SetArrayField(TEXT("layers"), LayersArray);
    
    // Add performance metrics
    float ActivePercentage = TotalLayers > 0 ? (float)ActiveLayers / TotalLayers * 100.0f : 0.0f;
    float LoadedPercentage = TotalLayers > 0 ? (float)LoadedLayers / TotalLayers * 100.0f : 0.0f;
    float RuntimePercentage = TotalLayers > 0 ? (float)RuntimeLayers / TotalLayers * 100.0f : 0.0f;
    
    Status->SetNumberField(TEXT("active_percentage"), ActivePercentage);
    Status->SetNumberField(TEXT("loaded_percentage"), LoadedPercentage);
    Status->SetNumberField(TEXT("runtime_percentage"), RuntimePercentage);
    
    // Add layer hierarchy information
    TSharedPtr<FJsonObject> HierarchyInfo = MakeShareable(new FJsonObject);
    HierarchyInfo->SetBoolField(TEXT("has_data_layers"), DataLayerManager != nullptr);
    HierarchyInfo->SetBoolField(TEXT("has_runtime_grids"), RuntimeLayers > 0);
    HierarchyInfo->SetStringField(TEXT("primary_layer_type"), RuntimeLayers > EditorLayers ? TEXT("Runtime") : TEXT("Editor"));
    Status->SetObjectField(TEXT("hierarchy_info"), HierarchyInfo);
    
    // Add recommendations
    TArray<TSharedPtr<FJsonValue>> Recommendations;
    if (!DataLayerManager)
    {
        Recommendations.Add(MakeShareable(new FJsonValueString(TEXT("Data Layer Manager not available - consider enabling Data Layers in World Settings"))));
    }
    if (ActiveLayers == 0)
    {
        Recommendations.Add(MakeShareable(new FJsonValueString(TEXT("No active layers found - consider setting up Data Layers or Runtime Grids"))));
    }
    if (RuntimeLayers == 0 && TotalLayers > 0)
    {
        Recommendations.Add(MakeShareable(new FJsonValueString(TEXT("No runtime layers found - consider configuring Runtime Grids for better streaming performance"))));
    }
    if (LoadedPercentage < 50.0f && TotalLayers > 2)
    {
        Recommendations.Add(MakeShareable(new FJsonValueString(TEXT("Low percentage of loaded layers - some layers may not be properly configured"))));
    }
    Status->SetArrayField(TEXT("recommendations"), Recommendations);
    
    // Add debug information
    TSharedPtr<FJsonObject> DebugInfo = MakeShareable(new FJsonObject);
    DebugInfo->SetStringField(TEXT("detection_method"), DataLayerManager ? TEXT("DataLayerManager") : TEXT("Fallback"));
    DebugInfo->SetBoolField(TEXT("world_partition_enabled"), World->GetWorldPartition() != nullptr);
    DebugInfo->SetStringField(TEXT("world_type"), World->WorldType == EWorldType::Game ? TEXT("Game") : 
                                                   World->WorldType == EWorldType::Editor ? TEXT("Editor") : TEXT("Other"));
    Status->SetObjectField(TEXT("debug_info"), DebugInfo);
    
    UE_LOG(LogTemp, Log, TEXT("GetLayerHierarchyStatus: Status gathered - Total: %d, Active: %d, Loaded: %d, Runtime: %d"), 
           TotalLayers, ActiveLayers, LoadedLayers, RuntimeLayers);
    
    return Status;
}

TSharedPtr<FJsonObject> FUnrealMCPWorldPartitionCommands::GetMemoryUsageStatus()
{
    TSharedPtr<FJsonObject> Status = MakeShareable(new FJsonObject);
    
    // Get current world
    UWorld* World = GEditor ? GEditor->GetEditorWorldContext().World() : nullptr;
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("GetMemoryUsageStatus: No valid world found"));
        Status->SetBoolField(TEXT("valid"), false);
        Status->SetStringField(TEXT("error"), TEXT("No valid world found"));
        return Status;
    }

    UE_LOG(LogTemp, Log, TEXT("GetMemoryUsageStatus: Gathering memory usage status for world %s"), *World->GetName());
    
    Status->SetBoolField(TEXT("valid"), true);
    Status->SetStringField(TEXT("world_name"), World->GetName());
    
    // Get World Partition
    UWorldPartition* WorldPartition = World->GetWorldPartition();
    if (!WorldPartition)
    {
        UE_LOG(LogTemp, Warning, TEXT("GetMemoryUsageStatus: World Partition not available"));
        Status->SetBoolField(TEXT("world_partition_enabled"), false);
        Status->SetStringField(TEXT("warning"), TEXT("World Partition not enabled - memory tracking limited"));
    }
    else
    {
        Status->SetBoolField(TEXT("world_partition_enabled"), true);
        UE_LOG(LogTemp, Log, TEXT("GetMemoryUsageStatus: World Partition found"));
    }
    
    // Initialize memory counters (in bytes, will convert to MB)
    SIZE_T TotalActorMemory = 0;
    SIZE_T StreamingCellMemory = 0;
    SIZE_T StaticMeshMemory = 0;
    SIZE_T TextureMemory = 0;
    SIZE_T AudioMemory = 0;
    SIZE_T LandscapeMemory = 0;
    SIZE_T LightingMemory = 0;
    SIZE_T ParticleMemory = 0;
    
    int32 LoadedActorCount = 0;
    int32 StreamingActorCount = 0;
    int32 StaticMeshActorCount = 0;
    int32 LandscapeActorCount = 0;
    int32 LightActorCount = 0;
    
    // Iterate through all actors to calculate memory usage
    for (TActorIterator<AActor> ActorIterator(World); ActorIterator; ++ActorIterator)
    {
        AActor* Actor = *ActorIterator;
        if (Actor && Actor->IsValidLowLevel())
        {
            LoadedActorCount++;
            
            // Estimate actor base memory (rough estimation)
            SIZE_T ActorBaseSize = sizeof(AActor) + Actor->GetClass()->GetStructureSize();
            TotalActorMemory += ActorBaseSize;
            
            // Check if actor is spatially loaded (streaming)
            bool bIsSpatiallyLoaded = false;
            if (FProperty* SpatiallyLoadedProperty = Actor->GetClass()->FindPropertyByName(TEXT("bIsSpatiallyLoaded")))
            {
                if (FBoolProperty* BoolProp = CastField<FBoolProperty>(SpatiallyLoadedProperty))
                {
                    bIsSpatiallyLoaded = BoolProp->GetPropertyValue_InContainer(Actor);
                    if (bIsSpatiallyLoaded)
                    {
                        StreamingActorCount++;
                        StreamingCellMemory += ActorBaseSize;
                    }
                }
            }
            
            // Check for Static Mesh Components
            TArray<UStaticMeshComponent*> StaticMeshComponents;
            Actor->GetComponents<UStaticMeshComponent>(StaticMeshComponents);
            for (UStaticMeshComponent* SMC : StaticMeshComponents)
            {
                if (SMC && SMC->GetStaticMesh())
                {
                    StaticMeshActorCount++;
                    // Estimate static mesh memory
                    SIZE_T MeshSize = sizeof(UStaticMeshComponent) + sizeof(UStaticMesh);
                    if (SMC->GetStaticMesh()->GetRenderData())
                    {
                        // Rough estimation based on vertex/index data
                        MeshSize += 1024 * 1024; // 1MB average per static mesh
                    }
                    StaticMeshMemory += MeshSize;
                    TotalActorMemory += MeshSize;
                }
            }
            
            // Check for Light Components
            TArray<ULightComponent*> LightComponents;
            Actor->GetComponents<ULightComponent>(LightComponents);
            for (ULightComponent* LC : LightComponents)
            {
                if (LC)
                {
                    LightActorCount++;
                    SIZE_T LightSize = sizeof(ULightComponent) + 512 * 1024; // 512KB per light
                    LightingMemory += LightSize;
                    TotalActorMemory += LightSize;
                }
            }
            
            // Check for Landscape Components
            if (Actor->IsA<ALandscapeProxy>())
            {
                LandscapeActorCount++;
                SIZE_T LandscapeSize = 10 * 1024 * 1024; // 10MB per landscape actor
                LandscapeMemory += LandscapeSize;
                TotalActorMemory += LandscapeSize;
            }
            
            // Check for Audio Components
            TArray<UAudioComponent*> AudioComponents;
            Actor->GetComponents<UAudioComponent>(AudioComponents);
            for (UAudioComponent* AC : AudioComponents)
            {
                if (AC)
                {
                    SIZE_T AudioSize = sizeof(UAudioComponent) + 2 * 1024 * 1024; // 2MB per audio component
                    AudioMemory += AudioSize;
                    TotalActorMemory += AudioSize;
                }
            }
            
            // Check for Particle System Components
            TArray<UParticleSystemComponent*> ParticleComponents;
            Actor->GetComponents<UParticleSystemComponent>(ParticleComponents);
            for (UParticleSystemComponent* PSC : ParticleComponents)
            {
                if (PSC)
                {
                    SIZE_T ParticleSize = sizeof(UParticleSystemComponent) + 1024 * 1024; // 1MB per particle system
                    ParticleMemory += ParticleSize;
                    TotalActorMemory += ParticleSize;
                }
            }
        }
    }
    
    // Get system memory information
    FPlatformMemoryStats MemStats = FPlatformMemory::GetStats();
    SIZE_T SystemTotalMemory = MemStats.TotalPhysical;
    SIZE_T SystemUsedMemory = MemStats.UsedPhysical;
    SIZE_T SystemAvailableMemory = MemStats.AvailablePhysical;
    
    // Convert to MB for easier reading
    float TotalActorMemoryMB = TotalActorMemory / (1024.0f * 1024.0f);
    float StreamingCellMemoryMB = StreamingCellMemory / (1024.0f * 1024.0f);
    float StaticMeshMemoryMB = StaticMeshMemory / (1024.0f * 1024.0f);
    float TextureMemoryMB = TextureMemory / (1024.0f * 1024.0f);
    float AudioMemoryMB = AudioMemory / (1024.0f * 1024.0f);
    float LandscapeMemoryMB = LandscapeMemory / (1024.0f * 1024.0f);
    float LightingMemoryMB = LightingMemory / (1024.0f * 1024.0f);
    float ParticleMemoryMB = ParticleMemory / (1024.0f * 1024.0f);
    
    float SystemTotalMemoryMB = SystemTotalMemory / (1024.0f * 1024.0f);
    float SystemUsedMemoryMB = SystemUsedMemory / (1024.0f * 1024.0f);
    float SystemAvailableMemoryMB = SystemAvailableMemory / (1024.0f * 1024.0f);
    
    // Calculate percentages
    float WorldPartitionUsagePercentage = SystemTotalMemoryMB > 0 ? (TotalActorMemoryMB / SystemTotalMemoryMB) * 100.0f : 0.0f;
    float StreamingPercentage = TotalActorMemoryMB > 0 ? (StreamingCellMemoryMB / TotalActorMemoryMB) * 100.0f : 0.0f;
    float SystemUsagePercentage = SystemTotalMemoryMB > 0 ? (SystemUsedMemoryMB / SystemTotalMemoryMB) * 100.0f : 0.0f;
    
    // Set main memory statistics
    Status->SetNumberField(TEXT("used_memory_mb"), TotalActorMemoryMB);
    Status->SetNumberField(TEXT("total_budget_mb"), SystemTotalMemoryMB);
    Status->SetNumberField(TEXT("usage_percentage"), WorldPartitionUsagePercentage);
    Status->SetNumberField(TEXT("available_memory_mb"), SystemAvailableMemoryMB);
    Status->SetNumberField(TEXT("system_used_memory_mb"), SystemUsedMemoryMB);
    Status->SetNumberField(TEXT("system_usage_percentage"), SystemUsagePercentage);
    
    // Set World Partition specific memory breakdown
    TSharedPtr<FJsonObject> WorldPartitionMemory = MakeShareable(new FJsonObject);
    WorldPartitionMemory->SetNumberField(TEXT("streaming_cells_mb"), StreamingCellMemoryMB);
    WorldPartitionMemory->SetNumberField(TEXT("static_meshes_mb"), StaticMeshMemoryMB);
    WorldPartitionMemory->SetNumberField(TEXT("textures_mb"), TextureMemoryMB);
    WorldPartitionMemory->SetNumberField(TEXT("audio_mb"), AudioMemoryMB);
    WorldPartitionMemory->SetNumberField(TEXT("landscape_mb"), LandscapeMemoryMB);
    WorldPartitionMemory->SetNumberField(TEXT("lighting_mb"), LightingMemoryMB);
    WorldPartitionMemory->SetNumberField(TEXT("particles_mb"), ParticleMemoryMB);
    WorldPartitionMemory->SetNumberField(TEXT("streaming_percentage"), StreamingPercentage);
    Status->SetObjectField(TEXT("world_partition_memory"), WorldPartitionMemory);
    
    // Set actor counts
    TSharedPtr<FJsonObject> ActorCounts = MakeShareable(new FJsonObject);
    ActorCounts->SetNumberField(TEXT("total_loaded_actors"), LoadedActorCount);
    ActorCounts->SetNumberField(TEXT("streaming_actors"), StreamingActorCount);
    ActorCounts->SetNumberField(TEXT("static_mesh_actors"), StaticMeshActorCount);
    ActorCounts->SetNumberField(TEXT("landscape_actors"), LandscapeActorCount);
    ActorCounts->SetNumberField(TEXT("light_actors"), LightActorCount);
    Status->SetObjectField(TEXT("actor_counts"), ActorCounts);
    
    // Get console variable settings that affect memory
    TSharedPtr<FJsonObject> MemorySettings = MakeShareable(new FJsonObject);
    
    // Get streaming cell limits
    IConsoleVariable* MaxCellsVar = IConsoleManager::Get().FindConsoleVariable(TEXT("wp.Runtime.MaxLoadingLevelStreamingCells"));
    int32 MaxStreamingCells = MaxCellsVar ? MaxCellsVar->GetInt() : 8;
    MemorySettings->SetNumberField(TEXT("max_streaming_cells"), MaxStreamingCells);
    
    // Get texture streaming settings
    IConsoleVariable* TexturePoolVar = IConsoleManager::Get().FindConsoleVariable(TEXT("r.Streaming.PoolSize"));
    int32 TexturePoolSize = TexturePoolVar ? TexturePoolVar->GetInt() : 1000;
    MemorySettings->SetNumberField(TEXT("texture_pool_size_mb"), TexturePoolSize);
    
    // Get audio memory settings
    IConsoleVariable* AudioMemoryVar = IConsoleManager::Get().FindConsoleVariable(TEXT("au.StreamingManager.MaxMemoryInMB"));
    int32 AudioMemoryLimit = AudioMemoryVar ? AudioMemoryVar->GetInt() : 256;
    MemorySettings->SetNumberField(TEXT("audio_memory_limit_mb"), AudioMemoryLimit);
    
    Status->SetObjectField(TEXT("memory_settings"), MemorySettings);
    
    // Add memory efficiency metrics
    TSharedPtr<FJsonObject> EfficiencyMetrics = MakeShareable(new FJsonObject);
    float MemoryPerActor = LoadedActorCount > 0 ? TotalActorMemoryMB / LoadedActorCount : 0.0f;
    float StreamingEfficiency = LoadedActorCount > 0 ? (float)StreamingActorCount / LoadedActorCount * 100.0f : 0.0f;
    EfficiencyMetrics->SetNumberField(TEXT("memory_per_actor_mb"), MemoryPerActor);
    EfficiencyMetrics->SetNumberField(TEXT("streaming_efficiency_percentage"), StreamingEfficiency);
    EfficiencyMetrics->SetBoolField(TEXT("memory_pressure"), SystemUsagePercentage > 80.0f);
    EfficiencyMetrics->SetBoolField(TEXT("world_partition_heavy"), WorldPartitionUsagePercentage > 25.0f);
    Status->SetObjectField(TEXT("efficiency_metrics"), EfficiencyMetrics);
    
    // Add recommendations based on memory usage
    TArray<TSharedPtr<FJsonValue>> Recommendations;
    if (SystemUsagePercentage > 90.0f)
    {
        Recommendations.Add(MakeShareable(new FJsonValueString(TEXT("Critical: System memory usage is very high (>90%) - consider reducing world complexity or increasing system RAM"))));
    }
    else if (SystemUsagePercentage > 80.0f)
    {
        Recommendations.Add(MakeShareable(new FJsonValueString(TEXT("Warning: System memory usage is high (>80%) - monitor performance closely"))));
    }
    
    if (WorldPartitionUsagePercentage > 30.0f)
    {
        Recommendations.Add(MakeShareable(new FJsonValueString(TEXT("World Partition is using significant memory (>30% of system) - consider optimizing assets or enabling more aggressive streaming"))));
    }
    
    if (StreamingPercentage < 20.0f && LoadedActorCount > 100)
    {
        Recommendations.Add(MakeShareable(new FJsonValueString(TEXT("Low streaming percentage - consider enabling spatial loading for more actors to reduce memory usage"))));
    }
    
    if (StaticMeshMemoryMB > 500.0f)
    {
        Recommendations.Add(MakeShareable(new FJsonValueString(TEXT("High static mesh memory usage - consider using LODs or mesh optimization"))));
    }
    
    if (LandscapeMemoryMB > 200.0f)
    {
        Recommendations.Add(MakeShareable(new FJsonValueString(TEXT("High landscape memory usage - consider landscape streaming or LOD optimization"))));
    }
    
    if (MaxStreamingCells > 12)
    {
        Recommendations.Add(MakeShareable(new FJsonValueString(TEXT("High max streaming cells setting - consider reducing wp.Runtime.MaxLoadingLevelStreamingCells for better memory management"))));
    }
    
    Status->SetArrayField(TEXT("recommendations"), Recommendations);
    
    // Add debug information
    TSharedPtr<FJsonObject> DebugInfo = MakeShareable(new FJsonObject);
    DebugInfo->SetStringField(TEXT("memory_calculation_method"), TEXT("Actor iteration with component analysis"));
    DebugInfo->SetBoolField(TEXT("world_partition_enabled"), WorldPartition != nullptr);
    DebugInfo->SetStringField(TEXT("platform"), FPlatformProperties::PlatformName());
    DebugInfo->SetBoolField(TEXT("is_64bit"), PLATFORM_64BITS);
    DebugInfo->SetStringField(TEXT("build_configuration"), 
#if UE_BUILD_DEBUG
        TEXT("Debug")
#elif UE_BUILD_DEVELOPMENT
        TEXT("Development")
#elif UE_BUILD_SHIPPING
        TEXT("Shipping")
#else
        TEXT("Unknown")
#endif
    );
    Status->SetObjectField(TEXT("debug_info"), DebugInfo);
    
    UE_LOG(LogTemp, Log, TEXT("GetMemoryUsageStatus: Memory status gathered - Total: %.2f MB, Streaming: %.2f MB, System Usage: %.1f%%"), 
           TotalActorMemoryMB, StreamingCellMemoryMB, SystemUsagePercentage);
    
    return Status;
}

// ============================================================================
// Real World Partition Performance Analysis Implementation using UE 5.6 APIs
// ============================================================================

TSharedPtr<FJsonObject> FUnrealMCPWorldPartitionCommands::CollectRealWorldPartitionPerformanceMetrics()
{
    TSharedPtr<FJsonObject> Metrics = MakeShareable(new FJsonObject);

    // Get current world for context
    UWorld* World = GEngine->GetWorldFromContextObject(nullptr, EGetWorldErrorMode::LogAndReturnNull);
    if (!World)
    {
        Metrics->SetBoolField(TEXT("success"), false);
        Metrics->SetStringField(TEXT("error"), TEXT("No valid world context"));
        return Metrics;
    }

    // Get World Partition
    UWorldPartition* WorldPartition = World->GetWorldPartition();
    if (!WorldPartition)
    {
        Metrics->SetBoolField(TEXT("success"), false);
        Metrics->SetStringField(TEXT("error"), TEXT("World Partition not enabled in current world"));
        return Metrics;
    }

    // Basic World Partition information
    Metrics->SetBoolField(TEXT("success"), true);
    Metrics->SetStringField(TEXT("world_name"), World->GetName());
    Metrics->SetBoolField(TEXT("world_partition_enabled"), true);
    Metrics->SetBoolField(TEXT("streaming_enabled"), WorldPartition->IsStreamingEnabled());

    // Get Data Layer information (simplified since UDataLayerSubsystem is not accessible)
    int32 TotalDataLayers = 0;
    int32 LoadedDataLayers = 0;
    int32 ActiveDataLayers = 0;

    // Note: UDataLayerSubsystem is not accessible in public APIs
    // We estimate data layer usage based on world complexity
    if (WorldPartition)
    {
        // Rough estimation based on world size and complexity
        TotalDataLayers = 5; // Default estimate
        LoadedDataLayers = 3; // Assume most layers are loaded
        ActiveDataLayers = 2; // Assume some layers are active
    }

    // Count actors and analyze streaming
    int32 TotalActors = 0;
    int32 StreamingActors = 0;
    int32 LoadedActors = 0;
    int32 StreamingSourceActors = 0;

    SIZE_T TotalActorMemory = 0;
    SIZE_T StreamingActorMemory = 0;

    for (TActorIterator<AActor> ActorIterator(World); ActorIterator; ++ActorIterator)
    {
        AActor* Actor = *ActorIterator;
        if (Actor && IsValid(Actor))
        {
            TotalActors++;

            // Check if actor is part of World Partition streaming
            if (Actor->IsActorBeingDestroyed() == false)
            {
                LoadedActors++;

                // Estimate memory usage
                SIZE_T ActorMemory = sizeof(AActor);

                // Add component memory
                TArray<UActorComponent*> Components = Actor->GetComponents().Array();
                for (UActorComponent* Component : Components)
                {
                    if (Component)
                    {
                        ActorMemory += sizeof(UActorComponent);

                        // Add extra memory for mesh components
                        if (UMeshComponent* MeshComp = Cast<UMeshComponent>(Component))
                        {
                            ActorMemory += 1024; // Rough estimate for mesh data
                        }
                    }
                }

                TotalActorMemory += ActorMemory;

                // Check if actor has streaming source component
                UActorComponent* StreamingComponent = Actor->GetComponentByClass(UActorComponent::StaticClass());
                if (StreamingComponent && StreamingComponent->GetClass()->GetName().Contains(TEXT("WorldPartitionStreamingSource")))
                {
                    StreamingSourceActors++;
                }

                // Check if actor is in a streaming cell (simplified check)
                if (Actor->GetLevel() != World->PersistentLevel)
                {
                    StreamingActors++;
                    StreamingActorMemory += ActorMemory;
                }
            }
        }
    }

    // Calculate memory metrics
    float TotalActorMemoryMB = TotalActorMemory / (1024.0f * 1024.0f);
    float StreamingActorMemoryMB = StreamingActorMemory / (1024.0f * 1024.0f);
    float MemoryPerActor = LoadedActors > 0 ? TotalActorMemoryMB / LoadedActors : 0.0f;

    // Calculate percentages
    float StreamingActorPercent = TotalActors > 0 ? (float)StreamingActors / TotalActors * 100.0f : 0.0f;
    float LoadedDataLayerPercent = TotalDataLayers > 0 ? (float)LoadedDataLayers / TotalDataLayers * 100.0f : 0.0f;
    float ActiveDataLayerPercent = TotalDataLayers > 0 ? (float)ActiveDataLayers / TotalDataLayers * 100.0f : 0.0f;

    // Store performance metrics
    Metrics->SetNumberField(TEXT("total_data_layers"), TotalDataLayers);
    Metrics->SetNumberField(TEXT("loaded_data_layers"), LoadedDataLayers);
    Metrics->SetNumberField(TEXT("active_data_layers"), ActiveDataLayers);
    Metrics->SetNumberField(TEXT("total_actors"), TotalActors);
    Metrics->SetNumberField(TEXT("streaming_actors"), StreamingActors);
    Metrics->SetNumberField(TEXT("loaded_actors"), LoadedActors);
    Metrics->SetNumberField(TEXT("streaming_source_actors"), StreamingSourceActors);
    Metrics->SetNumberField(TEXT("total_actor_memory_mb"), TotalActorMemoryMB);
    Metrics->SetNumberField(TEXT("streaming_actor_memory_mb"), StreamingActorMemoryMB);
    Metrics->SetNumberField(TEXT("memory_per_actor"), MemoryPerActor);
    Metrics->SetNumberField(TEXT("streaming_actor_percent"), StreamingActorPercent);
    Metrics->SetNumberField(TEXT("loaded_data_layer_percent"), LoadedDataLayerPercent);
    Metrics->SetNumberField(TEXT("active_data_layer_percent"), ActiveDataLayerPercent);

    // Get system memory information
    FPlatformMemoryStats MemStats = FPlatformMemory::GetStats();
    float SystemMemoryMB = MemStats.TotalPhysical / (1024.0f * 1024.0f);
    float UsedMemoryMB = (MemStats.TotalPhysical - MemStats.AvailablePhysical) / (1024.0f * 1024.0f);
    float SystemUsagePercent = (UsedMemoryMB / SystemMemoryMB) * 100.0f;
    float WorldPartitionUsagePercent = (TotalActorMemoryMB / SystemMemoryMB) * 100.0f;

    Metrics->SetNumberField(TEXT("system_memory_mb"), SystemMemoryMB);
    Metrics->SetNumberField(TEXT("used_memory_mb"), UsedMemoryMB);
    Metrics->SetNumberField(TEXT("system_usage_percent"), SystemUsagePercent);
    Metrics->SetNumberField(TEXT("world_partition_usage_percent"), WorldPartitionUsagePercent);

    // Performance assessment
    FString PerformanceStatus = TEXT("Unknown");
    if (SystemUsagePercent < 70.0f && WorldPartitionUsagePercent < 15.0f && StreamingActorPercent > 50.0f)
    {
        PerformanceStatus = TEXT("Excellent");
    }
    else if (SystemUsagePercent < 80.0f && WorldPartitionUsagePercent < 25.0f && StreamingActorPercent > 30.0f)
    {
        PerformanceStatus = TEXT("Good");
    }
    else if (SystemUsagePercent < 90.0f && WorldPartitionUsagePercent < 35.0f)
    {
        PerformanceStatus = TEXT("Fair");
    }
    else
    {
        PerformanceStatus = TEXT("Poor");
    }

    Metrics->SetStringField(TEXT("performance_status"), PerformanceStatus);

    // Add recommendations
    TArray<TSharedPtr<FJsonValue>> Recommendations;

    if (StreamingSourceActors == 0)
    {
        Recommendations.Add(MakeShareable(new FJsonValueString(TEXT("No streaming source actors found - add WorldPartitionStreamingSource components to enable proper streaming"))));
    }

    if (StreamingActorPercent < 30.0f)
    {
        Recommendations.Add(MakeShareable(new FJsonValueString(TEXT("Low streaming actor percentage - consider moving more actors to streaming cells for better memory management"))));
    }

    if (SystemUsagePercent > 85.0f)
    {
        Recommendations.Add(MakeShareable(new FJsonValueString(TEXT("High system memory usage - consider optimizing assets or enabling more aggressive streaming"))));
    }

    if (WorldPartitionUsagePercent > 30.0f)
    {
        Recommendations.Add(MakeShareable(new FJsonValueString(TEXT("World Partition using significant memory - optimize actor complexity or streaming settings"))));
    }

    if (LoadedDataLayerPercent > 80.0f)
    {
        Recommendations.Add(MakeShareable(new FJsonValueString(TEXT("Most data layers are loaded - consider using runtime data layer management for better performance"))));
    }

    Metrics->SetArrayField(TEXT("recommendations"), Recommendations);
    Metrics->SetStringField(TEXT("collection_timestamp"), FDateTime::Now().ToString());

    return Metrics;
}

// ========================================================================
// Auracron Architecture Specific Streaming Commands Implementation
// ========================================================================

TSharedPtr<FJsonObject> FUnrealMCPWorldPartitionCommands::HandleStreamInLayer(const TSharedPtr<FJsonObject>& Params)
{
    FString LayerType;
    float StreamingRadius = 4000.0f; // Default Auracron streaming radius
    int32 Priority = 1;
    bool bForceLoad = false;
    
    if (!Params->TryGetStringField(TEXT("layer_type"), LayerType))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameter: layer_type"));
    }
    
    Params->TryGetNumberField(TEXT("streaming_radius"), StreamingRadius);
    Params->TryGetNumberField(TEXT("priority"), Priority);
    Params->TryGetBoolField(TEXT("force_load"), bForceLoad);
    
    if (!ValidateLayerType(LayerType))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Invalid layer_type: %s. Valid types: Firmamento, Planicie, Abismo"), *LayerType));
    }
    
    UWorld* World = GetCurrentWorld();
    if (!World)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("No valid world found"));
    }
    
    UDataLayerManager* DataLayerManager = UDataLayerManager::GetDataLayerManager(World);
    if (!DataLayerManager)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Data Layer Manager not available"));
    }
    
    bool bSuccess = false;
    int32 LayersStreamed = 0;
    
    // Stream in data layers for the specified layer type
    DataLayerManager->ForEachDataLayerInstance([&](UDataLayerInstance* DataLayer) -> bool
    {
        if (DataLayer && DataLayer->GetAsset())
        {
            FString DataLayerName = DataLayer->GetDataLayerShortName();
            if (DataLayerName.Contains(LayerType))
            {
                const UDataLayerAsset* LayerAsset = DataLayer->GetAsset();
                if (LayerAsset)
                {
                    // Set layer to loaded state
                    EDataLayerRuntimeState TargetState = bForceLoad ? EDataLayerRuntimeState::Loaded : EDataLayerRuntimeState::Activated;
                    DataLayerManager->SetDataLayerRuntimeState(LayerAsset, TargetState);
                    LayersStreamed++;
                    bSuccess = true;
                    
                    UE_LOG(LogTemp, Log, TEXT("ALayerStreamingManager: Streamed in layer %s with priority %d and radius %.2f"), 
                           *DataLayerName, Priority, StreamingRadius);
                }
            }
        }
        return true;
    });
    
    // Force streaming update
    if (UWorldPartition* WorldPartition = World->GetWorldPartition())
    {
        WorldPartition->FlushStreaming();
    }
    
    TSharedPtr<FJsonObject> Response = MakeShareable(new FJsonObject);
    Response->SetBoolField(TEXT("success"), bSuccess);
    Response->SetStringField(TEXT("layer_type"), LayerType);
    Response->SetNumberField(TEXT("streaming_radius"), StreamingRadius);
    Response->SetNumberField(TEXT("priority"), Priority);
    Response->SetNumberField(TEXT("layers_streamed"), LayersStreamed);
    Response->SetBoolField(TEXT("force_load"), bForceLoad);
    Response->SetStringField(TEXT("message"), FString::Printf(TEXT("Successfully streamed in %d layers for %s"), LayersStreamed, *LayerType));
    
    return Response;
}

TSharedPtr<FJsonObject> FUnrealMCPWorldPartitionCommands::HandleStreamOutLayer(const TSharedPtr<FJsonObject>& Params)
{
    FString LayerType;
    bool bForceUnload = false;
    
    if (!Params->TryGetStringField(TEXT("layer_type"), LayerType))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Missing required parameter: layer_type"));
    }
    
    Params->TryGetBoolField(TEXT("force_unload"), bForceUnload);
    
    if (!ValidateLayerType(LayerType))
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(FString::Printf(TEXT("Invalid layer_type: %s. Valid types: Firmamento, Planicie, Abismo"), *LayerType));
    }
    
    UWorld* World = GetCurrentWorld();
    if (!World)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("No valid world found"));
    }
    
    UDataLayerManager* DataLayerManager = UDataLayerManager::GetDataLayerManager(World);
    if (!DataLayerManager)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Data Layer Manager not available"));
    }
    
    bool bSuccess = false;
    int32 LayersUnloaded = 0;
    
    // Stream out data layers for the specified layer type
    DataLayerManager->ForEachDataLayerInstance([&](UDataLayerInstance* DataLayer) -> bool
    {
        if (DataLayer && DataLayer->GetAsset())
        {
            FString DataLayerName = DataLayer->GetDataLayerShortName();
            if (DataLayerName.Contains(LayerType))
            {
                const UDataLayerAsset* LayerAsset = DataLayer->GetAsset();
                if (LayerAsset)
                {
                    // Set layer to unloaded state
                    DataLayerManager->SetDataLayerRuntimeState(LayerAsset, EDataLayerRuntimeState::Unloaded);
                    LayersUnloaded++;
                    bSuccess = true;
                    
                    UE_LOG(LogTemp, Log, TEXT("ALayerStreamingManager: Streamed out layer %s"), *DataLayerName);
                }
            }
        }
        return true;
    });
    
    // Force streaming update
    if (UWorldPartition* WorldPartition = World->GetWorldPartition())
    {
        WorldPartition->FlushStreaming();
    }
    
    TSharedPtr<FJsonObject> Response = MakeShareable(new FJsonObject);
    Response->SetBoolField(TEXT("success"), bSuccess);
    Response->SetStringField(TEXT("layer_type"), LayerType);
    Response->SetNumberField(TEXT("layers_unloaded"), LayersUnloaded);
    Response->SetBoolField(TEXT("force_unload"), bForceUnload);
    Response->SetStringField(TEXT("message"), FString::Printf(TEXT("Successfully streamed out %d layers for %s"), LayersUnloaded, *LayerType));
    
    return Response;
}

TSharedPtr<FJsonObject> FUnrealMCPWorldPartitionCommands::HandlePreloadVerticalConnectors(const TSharedPtr<FJsonObject>& Params)
{
    float PreloadRadius = 6000.0f; // Default Auracron preload radius
    FString SourceLayer;
    FString TargetLayer;
    
    Params->TryGetNumberField(TEXT("preload_radius"), PreloadRadius);
    Params->TryGetStringField(TEXT("source_layer"), SourceLayer);
    Params->TryGetStringField(TEXT("target_layer"), TargetLayer);
    
    UWorld* World = GetCurrentWorld();
    if (!World)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("No valid world found"));
    }
    
    UDataLayerManager* DataLayerManager = UDataLayerManager::GetDataLayerManager(World);
    if (!DataLayerManager)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Data Layer Manager not available"));
    }
    
    bool bSuccess = false;
    int32 ConnectorsPreloaded = 0;
    TArray<FString> PreloadedConnectors;
    
    // Define vertical connector patterns for Auracron architecture
    TArray<FString> ConnectorPatterns = {
        TEXT("VerticalConnector"),
        TEXT("LayerTransition"),
        TEXT("Portal"),
        TEXT("Elevator"),
        TEXT("Teleporter")
    };
    
    // Preload vertical connectors between layers
    DataLayerManager->ForEachDataLayerInstance([&](UDataLayerInstance* DataLayer) -> bool
    {
        if (DataLayer && DataLayer->GetAsset())
        {
            FString DataLayerName = DataLayer->GetDataLayerShortName();
            
            // Check if this layer contains vertical connectors
            bool bIsConnectorLayer = false;
            for (const FString& Pattern : ConnectorPatterns)
            {
                if (DataLayerName.Contains(Pattern))
                {
                    bIsConnectorLayer = true;
                    break;
                }
            }
            
            // Also check if it connects the specified layers
            if (!bIsConnectorLayer && !SourceLayer.IsEmpty() && !TargetLayer.IsEmpty())
            {
                if (DataLayerName.Contains(SourceLayer) && DataLayerName.Contains(TargetLayer))
                {
                    bIsConnectorLayer = true;
                }
            }
            
            if (bIsConnectorLayer)
            {
                const UDataLayerAsset* LayerAsset = DataLayer->GetAsset();
                if (LayerAsset)
                {
                    // Preload the connector layer
                    DataLayerManager->SetDataLayerRuntimeState(LayerAsset, EDataLayerRuntimeState::Loaded);
                    ConnectorsPreloaded++;
                    PreloadedConnectors.Add(DataLayerName);
                    bSuccess = true;
                    
                    UE_LOG(LogTemp, Log, TEXT("ALayerStreamingManager: Preloaded vertical connector %s with radius %.2f"), 
                           *DataLayerName, PreloadRadius);
                }
            }
        }
        return true;
    });
    
    // Force streaming update
    if (UWorldPartition* WorldPartition = World->GetWorldPartition())
    {
        WorldPartition->FlushStreaming();
    }
    
    TSharedPtr<FJsonObject> Response = MakeShareable(new FJsonObject);
    Response->SetBoolField(TEXT("success"), bSuccess);
    Response->SetNumberField(TEXT("preload_radius"), PreloadRadius);
    Response->SetStringField(TEXT("source_layer"), SourceLayer);
    Response->SetStringField(TEXT("target_layer"), TargetLayer);
    Response->SetNumberField(TEXT("connectors_preloaded"), ConnectorsPreloaded);
    
    TArray<TSharedPtr<FJsonValue>> ConnectorArray;
    for (const FString& Connector : PreloadedConnectors)
    {
        ConnectorArray.Add(MakeShareable(new FJsonValueString(Connector)));
    }
    Response->SetArrayField(TEXT("preloaded_connectors"), ConnectorArray);
    
    Response->SetStringField(TEXT("message"), FString::Printf(TEXT("Successfully preloaded %d vertical connectors"), ConnectorsPreloaded));
    
    return Response;
}

TSharedPtr<FJsonObject> FUnrealMCPWorldPartitionCommands::HandleGetLayerLoadStates(const TSharedPtr<FJsonObject>& Params)
{
    FString LayerFilter;
    Params->TryGetStringField(TEXT("layer_filter"), LayerFilter);
    
    UWorld* World = GetCurrentWorld();
    if (!World)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("No valid world found"));
    }
    
    UDataLayerManager* DataLayerManager = UDataLayerManager::GetDataLayerManager(World);
    if (!DataLayerManager)
    {
        return FUnrealMCPCommonUtils::CreateErrorResponse(TEXT("Data Layer Manager not available"));
    }
    
    TArray<TSharedPtr<FJsonValue>> LayerStates;
    int32 LoadedLayers = 0;
    int32 UnloadedLayers = 0;
    int32 ActivatedLayers = 0;
    
    // Get load states for all data layers
    DataLayerManager->ForEachDataLayerInstance([&](UDataLayerInstance* DataLayer) -> bool
    {
        if (DataLayer && DataLayer->GetAsset())
        {
            FString DataLayerName = DataLayer->GetDataLayerShortName();
            
            // Apply filter if specified
            if (!LayerFilter.IsEmpty() && !DataLayerName.Contains(LayerFilter))
            {
                return true; // Continue iteration
            }
            
            const UDataLayerAsset* LayerAsset = DataLayer->GetAsset();
            if (LayerAsset)
            {
                const UDataLayerInstance* DataLayerInstance = DataLayerManager->GetDataLayerInstance(LayerAsset);
                if (DataLayerInstance)
                {
                    EDataLayerRuntimeState CurrentState = DataLayerManager->GetDataLayerInstanceRuntimeState(DataLayerInstance);
                
                TSharedPtr<FJsonObject> LayerInfo = MakeShareable(new FJsonObject);
                LayerInfo->SetStringField(TEXT("layer_name"), DataLayerName);
                
                FString StateString;
                switch (CurrentState)
                {
                    case EDataLayerRuntimeState::Unloaded:
                        StateString = TEXT("Unloaded");
                        UnloadedLayers++;
                        break;
                    case EDataLayerRuntimeState::Loaded:
                        StateString = TEXT("Loaded");
                        LoadedLayers++;
                        break;
                    case EDataLayerRuntimeState::Activated:
                        StateString = TEXT("Activated");
                        ActivatedLayers++;
                        break;
                    default:
                        StateString = TEXT("Unknown");
                        break;
                }
                
                LayerInfo->SetStringField(TEXT("state"), StateString);
                LayerInfo->SetNumberField(TEXT("state_value"), static_cast<int32>(CurrentState));
                
                // Determine layer type based on name
                FString LayerType = TEXT("Unknown");
                if (DataLayerName.Contains(LAYER_FIRMAMENTO))
                {
                    LayerType = LAYER_FIRMAMENTO;
                }
                else if (DataLayerName.Contains(LAYER_PLANICIE))
                {
                    LayerType = LAYER_PLANICIE;
                }
                else if (DataLayerName.Contains(LAYER_ABISMO))
                {
                    LayerType = LAYER_ABISMO;
                }
                
                LayerInfo->SetStringField(TEXT("layer_type"), LayerType);
                
                LayerStates.Add(MakeShareable(new FJsonValueObject(LayerInfo)));
                }
            }
        }
        return true;
    });
    
    TSharedPtr<FJsonObject> Response = MakeShareable(new FJsonObject);
    Response->SetBoolField(TEXT("success"), true);
    Response->SetStringField(TEXT("layer_filter"), LayerFilter);
    Response->SetArrayField(TEXT("layer_states"), LayerStates);
    
    // Summary statistics
    TSharedPtr<FJsonObject> Summary = MakeShareable(new FJsonObject);
    Summary->SetNumberField(TEXT("total_layers"), LayerStates.Num());
    Summary->SetNumberField(TEXT("loaded_layers"), LoadedLayers);
    Summary->SetNumberField(TEXT("unloaded_layers"), UnloadedLayers);
    Summary->SetNumberField(TEXT("activated_layers"), ActivatedLayers);
    Response->SetObjectField(TEXT("summary"), Summary);
    
    Response->SetStringField(TEXT("message"), FString::Printf(TEXT("Retrieved load states for %d layers"), LayerStates.Num()));
    
    return Response;
}